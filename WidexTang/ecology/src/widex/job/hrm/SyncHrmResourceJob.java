package widex.job.hrm;


import localhost.services.HrmService.HrmServiceHttpBindingStub;
import localhost.services.HrmService.HrmServiceLocator;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetDataSource;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.company.DepartmentComInfo;
import weaver.hrm.company.SubCompanyComInfo;
import weaver.hrm.job.JobActivitiesComInfo;
import weaver.hrm.job.JobGroupsComInfo;
import weaver.hrm.job.JobTitlesComInfo;
import weaver.hrm.resource.ResourceComInfo;
import weaver.interfaces.schedule.BaseCronJob;
import weaver.toolbox.core.convert.Convert;
import weaver.toolbox.core.date.DateUtil;
import weaver.toolbox.db.Entity;
import weaver.toolbox.db.recordset.ExecuteUtil;
import weaver.toolbox.db.recordset.QueryUtil;
import weaver.toolbox.modeform.ModeFormUtil;

import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URL;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

/**
 * 功能说明 从中间表读取人力资源信息，并同步至OA系统
 *
 * <AUTHOR>
 * @crete Nov 18, 2022 16:02
 */
public class SyncHrmResourceJob extends BaseCronJob {


    private static final BaseBean log = new BaseBean();

    /**
     * 人员工号前缀-对应分部 的map
     */
    private Map<String, String> fenbuMap;
    /**
     * 人员工号前缀-对应职务 的map
     */
    private Map<String, String> zhiwuMap;
    /**
     * 人员白名单Set
     */
    private Set<String> whiteHrmSet;

    /**
     * 特殊的部门id，这些部门下的人，需要挪到默认部门
     */
    private static final String SPECIAL_DEPTID = "213";

    /**
     * 人力资源基本信息自定义字段名-bu_hr
     */
    private String cusfield_bu_hr;
    /**
     * 人力资源基本信息自定义字段名-employ_company
     */
    private String cusfield_employ_company;
    /**
     * 建模OA的配置表
     * Bu_hr -对应- 分部名称
     */
    private Map<String, String> buhrSubCompanyMap;


    @Override
    public void execute() {
        doExecute();
    }

    private void _init() {
        fenbuMap = new HashMap<>();
        zhiwuMap = new HashMap<>();
        whiteHrmSet = new HashSet<>();
        cusfield_bu_hr = "";
        cusfield_employ_company = "";
        buhrSubCompanyMap = new HashMap<>();

    }

    public synchronized Map<String, Object> doExecute() {
        //初始化
        _init();
        log.writeLog("SyncHrmResourceJobCronJob start");
        Map<String, Object> result = new HashMap<>();
        //人力资源自定义字段配置
        cusfield_bu_hr = Util.null2String(log.getPropValue("hrmsyncnew", "cusfield_bu_hr"));
        cusfield_employ_company = Util.null2String(log.getPropValue("hrmsyncnew", "cusfield_employ_company"));
        log.writeLog("cusfield_bu_hr: " + cusfield_bu_hr);
        log.writeLog("cusfield_employ_company: " + cusfield_employ_company);
        if (StringUtils.isBlank(cusfield_bu_hr) || StringUtils.isBlank(cusfield_employ_company)) {
            log.writeLog("缺失cusfield_bu_hr或cusfield_employ_company参数");
            result.put("paramerror", "缺失cusfield_bu_hr或cusfield_employ_company参数");
            return result;
        }

        //设置map
        setHrmMap();
        //设置人员白名单Set
        setWhiteHrmMap();
        //设置Bu_hr和分部的映射关系
        setBuHrSubCompanyMap();
        result.put("starttime", TimeUtil.getCurrentTimeString());
        StringBuilder allFailCode = new StringBuilder();
        //白名单人员
        List<String> whiteList = new ArrayList<>();
        //编号为空或者a05字段为空人员
        List<String> a05List = new ArrayList<>();
        //PRE开头的人员
        List<String> preList = new ArrayList<>();
        //缺失bu_hr字段人员
        List<String> buhrList = new ArrayList<>();
        // 最后同步时间
        String qrySyncInfo = "select top 1 lastsynctime from uf_hrm_syncinfo";
        String lastSyncTime = QueryUtil.doQueryFieldValue(qrySyncInfo, "lastsynctime");
        log.writeLog("lastSyncTime:" + lastSyncTime);
        // 全部替换
        lastSyncTime = lastSyncTime.replaceAll("&nbsp;", " ");
        log.writeLog("lastSyncTimeFinal:" + lastSyncTime);
        //lastSyncTimeFinal 例如为： 2025-05-08 06:30:01 ，需要转为 2025-05-06 00:00:00，取前两天的日期
        lastSyncTime = lastSyncTime.substring(0, 10);
        lastSyncTime = TimeUtil.dateAdd(lastSyncTime, -2);
        lastSyncTime = lastSyncTime + " 00:00:00";
        log.writeLog("本次同步最终开始时间:" + lastSyncTime);

        // 外部数据源
        RecordSetDataSource rsInterface = new RecordSetDataSource("OAINTERFACE");
        String qryInterface = "select * from CITYRAY_TO_OA_INTERMEDIATE_TABLE where 1=1";
        if (StringUtils.isNotBlank(lastSyncTime)) {
            // 根据input_date判断，人员信息是否需要更新
            qryInterface += " and input_date >= '" + lastSyncTime + "'";
        }
        log.writeLog("本次查询中间表sql:" + qryInterface);
        rsInterface.execute(qryInterface);
        result.put("outsize", rsInterface.getCounts());
        //接口执行的个数
        int executeSize = 0;
        //员工编号或a05字段为空的个数
        int a05Size = 0;
        //PRE开头的个数
        int PRESize = 0;
        //缺失bu_hr字段人员的个数
        int buHRSize = 0;
        // OA人员信息,
        //查所有状态的数据，不然中间表如果有数据，后面判断更新自定义字段表时有就会有问题，会变成重复的数据
        RecordSet rsOa = new RecordSet();
        String qryOa = "select a.id,a.workcode,b.field0,b.field1,b.field2,b.field3,b.field4,b.field5,b.field7 from hrmresource a " +
                " left join cus_fielddata b on a.id = b.id and scope = 'HrmCustomFieldByInfoType' and scopeid = -1 ";
        rsOa.execute(qryOa);
        while (rsInterface.next()) {
            // 当中间表字段employee_no或a05其一为空时不同步至OA
            // 编号
            String employee_no = Util.null2String(rsInterface.getString("employee_no"));
            log.writeLog("employee_no:" + employee_no + "---execute");

            //白名单的
            if (whiteHrmSet.contains(employee_no)) {
                log.writeLog("employee_no:" + employee_no + "---在白名单，不同步");
                whiteList.add(employee_no);
                continue;
            }

            // GID编号
            String a05 = Util.null2String(rsInterface.getString("a05"));
            if (StringUtils.isBlank(employee_no) || StringUtils.isBlank(a05)) {
                log.writeLog("employee_no:" + employee_no + "---a05或者employee_no为空，不同步");
                a05Size++;
                a05List.add(employee_no);
                continue;
            }
            //增加逻辑 若员工编号以PRE开头，则不同步该员工，跳过下个员工
            if (employee_no.trim().startsWith("PRE")) {
                log.writeLog("employee_no:" + employee_no + "---员工编号以PRE开头，不同步");
                PRESize++;
                preList.add(employee_no);
                continue;
            }
            //2025-06-24 增加逻辑，判断中间表Bu_hr字段是否为空，为空的不同步
            String bu_hr = Util.null2String(rsInterface.getString("bu_hr"));
            String employ_company = Util.null2String(rsInterface.getString("employ_company"));
            if (bu_hr.isEmpty()) {
                log.writeLog("employee_no:" + employee_no + "---bu_hr字段为空，不同步");
                buHRSize++;
                buhrList.add(employee_no);
                continue;
            }

            // 分部 通过编号来判定（80开头为SIVANTOS，EMP开头为Widex，LK开头为LK）
            //2023-11-21 更改为读配置表
            // String subcompany = getMapStartWith(employee_no, fenbuMap);

//            if (employee_no.startsWith("80")) {
//                subcompany = "SIVANTOS";
//            } else if (employee_no.startsWith("EMP")) {
//                subcompany = "Widex";
//            } else if (employee_no.startsWith("LK")) {
//                subcompany = "LK";
//            }
            //2025-06-24 修改逻辑，通过新的配置表，根据bu_hr获取分部
            String subcompany = buhrSubCompanyMap.getOrDefault(bu_hr, "");
            log.writeLog("匹配到subcompany:" + subcompany);


            // 部门 拼接示例：aa16 + aa17 + a02 = CoGS>AMC>Custom products Modelling only 需根据编号将人员分配至OA组织架构对应部门；
            //     如上述路径找不到，则将人员同步至特定部门
            String aa16 = Util.null2String(rsInterface.getString("aa16"));
            String aa17 = Util.null2String(rsInterface.getString("aa17"));
            String a02 = Util.null2String(rsInterface.getString("a02"));
            // 姓名
            String employee_name = Util.null2String(rsInterface.getString("employee_name"));
            //if(!"顾羽卿".equals(employee_name)){
            //    continue;
            //}
            // 登录名
            String work_email = Util.null2String(rsInterface.getString("work_email"));
            // 性别 F为女，M为男
            String gender = Util.null2String(rsInterface.getString("Gender"));
            gender = "M".equals(gender) ? "男" : "女";
            // 岗位（即jobtitle）
            String a04 = Util.null2String(rsInterface.getString("a04"));
            // 职务 通过编号来判定（80开头为SIVANTOS，EMP开头为Widex，LK开头为LK）
            //2023-11-21 更改为读配置表
            String jobactivityid = getMapStartWith(employee_no, zhiwuMap);
            log.writeLog("根据人员编号:" + employee_no + "匹配到职务jobactivityid:" + jobactivityid);

//            if (employee_no.startsWith("80")) {
//                jobtitle = "SIVANTOS";
//            } else if (employee_no.startsWith("EMP")) {
//                jobtitle = "Widex";
//            } else if (employee_no.startsWith("LK")) {
//                jobtitle = "LK";
//            }
            // 职务类型 通过编号来判定（80开头为SIVANTOS，EMP开头为Widex，LK开头为LK）
            //2023-11-21 更改为读配置表
            String jobgroupid = getMapStartWith(employee_no, zhiwuMap);
            log.writeLog("根据人员编号:" + employee_no + "匹配到职务jobgroupid:" + jobgroupid);
//            if (employee_no.startsWith("80")) {
//                jobtype = "SIVANTOS";
//            } else if (employee_no.startsWith("EMP")) {
//                jobtype = "Widex";
//            } else if (employee_no.startsWith("LK")) {
//                jobtype = "LK";
//            }
            // 直接上级 supervisor_no对应的value值为中间表字段a05，注意下并不是编号，示例：AU000RT6
            String supervisor_no = Util.null2String(rsInterface.getString("supervisor_no"));
            // 办公地点
            String working_location = Util.null2String(rsInterface.getString("working_location"));
            // 移动电话
            String mobile_no = Util.null2String(rsInterface.getString("mobile_no"));
            // 电子邮件
            // 系统语言 新增人员系统语言默认为简体中文
            // 出生日期
            String date_of_birth = Util.null2String(rsInterface.getString("date_of_birth"));
            // 合同开始日期
            String contract_start_date = Util.null2String(rsInterface.getString("contract_start_date"));
            // 合同结束日期
            String contract_end_date = Util.null2String(rsInterface.getString("contract_end_date"));
            // 试用期结束日期
            String probation_complete_date = Util.null2String(rsInterface.getString("probation_complete_date"));
            // 现居住地
            String address = Util.null2String(rsInterface.getString("address"));
            // 最后更新日期
            String input_date = Util.null2String(rsInterface.getString("input_date"));

            // 操作，默认为新增
            String userId = "-9999";
            String action = "add";
            rsOa.beforFirst();
            while (rsOa.next()) {
                // 编号
                String workcode = Util.null2String(rsOa.getString("workcode"));
                //匹配OA系统中存在的用户，如果有就代表的更新老员工，没有就是新增
                if (employee_no.equals(workcode)) {
                    userId = Util.null2String(rsOa.getString("id"));
                    action = "edit";
                    break;
                }
            }
            // 状态 新增人员设定默认值为正式，若已有员工中间表字段termination_effect_date日期大于 1900-01-01，OA状态为离职
            String employee_status = Util.null2String(rsInterface.getString("employee_status"));
            String termination_effect_date = Util.null2String(rsInterface.getString("termination_effect_date"));
            log.writeLog("employee_status:" + employee_status);
            log.writeLog("termination_effect_date:" + termination_effect_date);

            if (StringUtils.isNotBlank(termination_effect_date) && !"1900-01-01".equals(termination_effect_date)) {
                // 2023-09-15 调整增加判断：当termination_effect_date有值并且小于等于当前日期并且不等于1900-01-01，给这个人离职
                BigDecimal nowDateNum = getBigDecimalValue(TimeUtil.getCurrentDateString().replaceAll("-", ""), BigDecimal.ZERO);
                BigDecimal terminationNum = getBigDecimalValue(termination_effect_date.replaceAll("-", ""), BigDecimal.ZERO);
                log.writeLog("nowDateNum:" + nowDateNum);
                log.writeLog("terminationNum:" + terminationNum);
                //当前时间比 离职时间 大于等于，则把这个人离职
                if (nowDateNum.compareTo(terminationNum) >= 0) {
                    action = "delete";
                }
            }

            // 入职日期 【新增人员】入职日期 = 合同开始日期
            //         【已有人员】如果合同开始日期 <= probation_complete_date将OA中人员的入职日期更新为合同开始日期
            //          如果合同开始日期>probation_complete_date OA中人员的入职日期不更新
            String employmentDate = "";
            if ("add".equals(action)) {
                employmentDate = contract_start_date;
            } else if ("edit".equals(action)) {
                if (DateUtil.compare(Convert.toDate(contract_start_date), Convert.toDate(probation_complete_date)) <= 0) {
                    employmentDate = contract_start_date;
                }
            }
            log.writeLog("contract_start_date:" + contract_start_date);
            log.writeLog("probation_complete_date:" + probation_complete_date);
            log.writeLog("employmentDate:" + employmentDate);
            log.writeLog("action:" + action);

            //组装xml
            Map<String, String> mapXml = getUpdateUserXml(
                    action,
                    employee_no,
                    work_email,
                    employee_name,
                    gender,
                    subcompany,
                    aa16,
                    aa17,
                    a02,
                    working_location,
                    mobile_no,
                    date_of_birth,
                    address,
                    a04,
                    jobactivityid,
                    jobgroupid,
                    supervisor_no,
                    probation_complete_date);
            log.writeLog("employee_no:" + employee_no + ",mapXml:" + mapXml);
            String xml = Util.null2String(mapXml.get("xml"));
            String recoverSql = Util.null2String(mapXml.get("recoverSql"));
            String recoverManagerSql = Util.null2String(mapXml.get("recoverManagerSql"));
            if (xml.isEmpty()) {
                log.writeLog("xml empty");
                continue;
            }
            //成功的执行数量
            if (executeXml(xml)) {
                executeSize++;
                if (!recoverSql.isEmpty()) {
                    RecordSet rs = new RecordSet();
                    log.writeLog("执行恢复分部部门sql：" + recoverSql);
                    rs.executeUpdate(recoverSql);
                }

                if (!recoverManagerSql.isEmpty()) {
                    RecordSet rs = new RecordSet();
                    log.writeLog("执行恢复上级sql：" + recoverManagerSql);
                    rs.executeUpdate(recoverManagerSql);
                }
            } else {
                allFailCode.append(employee_no).append(";");
            }
            // 这个版本的接口不支持更新自定义字段，直接写SQL更新
            if (StringUtils.isNotBlank(input_date) && input_date.length() > 10) {
                input_date = input_date.substring(0, 10);
            }
            //走完接口后，重新根据工号获取一遍现在的OA用户id
            userId = getUserIdByWorkCode(employee_no);
            log.writeLog("now employee_no:" + employee_no + ",userId:" + userId);
            //增加判断，人员id不为空的时候，再执行后续逻辑，id为空的时候，可能是接口同步失败了的，所以查不到人员id
            if (!userId.isEmpty()) {
                //---更新人员自定义字段信息---START----
                // 查询当前用户是否已经存在基本信息扩展字段数据
                String chkCusFieldDataSql = "select id from cus_fielddata where scope = 'HrmCustomFieldByInfoType' and scopeid = -1 and id = " + userId;
                //自定义字段表，已经有该人员id，则更新
                if (QueryUtil.doCheckHasData(chkCusFieldDataSql, "id")) {
                    String sql = "update cus_fielddata set " +
                            "field4 = '" + a05 + "', " + //GID编号
                            "field7 ='" + input_date + "', " + //最后更新日期1
                            cusfield_bu_hr + " = '" + bu_hr + "'," +
                            cusfield_employ_company + " = '" + employ_company + "'";
                    //入职日期不为空时更新，为空的时候不更新（防止更新成空的）
                    if (StringUtils.isNotBlank(employmentDate)) {
                        sql += ",field3 = '" + employmentDate + "' "; //入职日期
                    }
                    sql += " where scope = 'HrmCustomFieldByInfoType' and scopeid = -1 and id = " + userId;
                    log.writeLog("更新人员自定义字段数据sql:" + sql);
                    ExecuteUtil.executeSql(sql);
                } else {
                    //自定义字段表，没有该人员id，则新增
                    //新增的时候不处理入职日期，空的时候，直接插入空的字符串即可
                    String sql = "insert into cus_fielddata (" +
                            "scope," +
                            "scopeid," +
                            "id," +
                            "field3," +
                            "field4," +
                            "field7," +
                            cusfield_bu_hr + "," +
                            cusfield_employ_company + ")" +
                            " values ( " +
                            " 'HrmCustomFieldByInfoType'," +
                            " -1," +
                            userId + "," +
                            "'" + employmentDate + "'," +
                            "'" + a05 + "'," +
                            "'" + input_date + "'," +
                            "'" + bu_hr + "'," +
                            "'" + employ_company + "' )";
                    log.writeLog("新增人员自定义字段数据sql:" + sql);
                    ExecuteUtil.executeSql(sql);
                }
                //---更新人员自定义字段信息---END----

                // SB做的HR接口，没有加到接口的字段，SQL语句更新
                String now = DateUtil.now();
                String updateSql = "update hrmresource set startdate = '" + contract_start_date + "'," +
                        " enddate = '" + contract_end_date + "', " +
                        " probationenddate = '" + probation_complete_date + "'," +
                        " datefield1 = '" + now + "' ";
                //2024-12-30
                //更新，入职日期，更新到标准表里的入职日期
                if (StringUtils.isNotBlank(employmentDate)) {
                    updateSql += ",companystartdate = '" + employmentDate + "' ";
                }
                updateSql += " where id = " + userId;
                log.writeLog("update hrmresource sql:" + updateSql);
                ExecuteUtil.executeSql(updateSql);
            } else {
                log.writeLog("now employee_no:" + employee_no + ",userId:" + userId + ",人员id为空，该人同步失败");
            }
        }

        // 全部更新完成之后，更新最后一次同步时间
        String now = DateUtil.now();
        if (!QueryUtil.doCheckHasData("select id from uf_hrm_syncinfo", "id")) {
            Entity data = new Entity();
            data.setTableName("uf_hrm_syncinfo");
            data.set("lastsynctime", now);
            ModeFormUtil.insertModeData(data, Convert.toInt(QueryUtil.getFormmodeidByTablename("uf_hrm_syncinfo")), 1);
        } else {
            String sql = "update uf_hrm_syncinfo set lastsynctime = '" + now + "'";
            log.writeLog("update lastsynctime sql:" + sql);
            ExecuteUtil.executeSql(sql);
        }
        //增加优化逻辑，不知道为什么会有重复的人员附加信息，同步完清除一下
        String sqlDel = "delete from cus_fielddata where seqorder in( " +
                " select min(seqorder) from cus_fielddata as t1 " +
                " where " +
                " ( " +
                " select count(*) from cus_fielddata t2 " +
                " where " +
                " t2.scope=t1.scope and " +
                " t2.scopeid=t1.scopeid and " +
                " t2.id=t1.id " +
                " )>1 group by scope,scopeid,id " +
                " having count(*)>1 " +
                " )";
        ExecuteUtil.executeSql(sqlDel);

        try {
            removeCache();
        } catch (Exception e) {
            log.writeLog("removeCache error:" + e.getMessage());
        }
        log.writeLog("SyncHrmResourceJobCronJob end");
        result.put("endtime", TimeUtil.getCurrentTimeString());
        result.put("executeSize", executeSize);
        result.put("a05Size", a05Size);
        result.put("PRESize", PRESize);
        result.put("buHRSize", buHRSize);
        result.put("whiteList", whiteList);
        result.put("a05List", a05List);
        result.put("preList", preList);
        result.put("buhrList", buhrList);
        result.put("failCode", allFailCode.toString());
        return result;
    }

    /**
     * @param action                  操作类型
     * @param workcode                工号
     * @param email                   邮箱
     * @param lastname                人员姓名
     * @param sex                     性别
     * @param subcompany              分部
     * @param a16
     * @param a17
     * @param a02
     * @param locationid
     * @param mobile
     * @param birthday
     * @param residentplace           现居住地
     * @param jobtitle
     * @param jobactivityid
     * @param jobgroupid
     * @param managerid
     * @param probation_complete_date
     * @return
     */
    private Map<String, String> getUpdateUserXml(String action,
                                                 String workcode,
                                                 String email,
                                                 String lastname,
                                                 String sex,
                                                 String subcompany,
                                                 String a16,
                                                 String a17,
                                                 String a02,
                                                 String locationid,
                                                 String mobile,
                                                 String birthday,
                                                 String residentplace,
                                                 String jobtitle,
                                                 String jobactivityid,
                                                 String jobgroupid,
                                                 String managerid,
                                                 String probation_complete_date) {
        Map<String, String> result = new HashMap<>();

        log.writeLog("getUpdateUserXml action:" + action);
        log.writeLog("subcompany:" + subcompany);
        log.writeLog("managerid:" + managerid);
        // 处理密码
        String password = "add".equals(action) ? "        <password>Wsa2020</password>\n" : "";
        // 处理密级
        String seclevel = "add".equals(action) ? "        <seclevel>10</seclevel>\n" : "";
        String status = "delete".equals(action) ? "离职" : "正式";
        action = "add".equals(action) ? action : "edit";
        //2024-07-15 修改,正式状态的人工，员工的试用期到期日期+1≥当前日期，该员工的“状态”为“试用”
        if ("正式".equals(status)) {
            if (StringUtils.isNotBlank(probation_complete_date)) {
                //员工的试用期到期日期+1≥当前日期，该员工的“状态”为“试用”
                if (isDatePlusOneDayAfterOrEqualToday(probation_complete_date)) {
                    log.writeLog("workcode:" + workcode + ";lastname:" + lastname + ",试用期到期日期+1>=当前日期，试用状态");
                    status = "试用";
                } else {
                    log.writeLog("workcode:" + workcode + ";lastname:" + lastname + ",试用期到期日期+1<当前日期，正式状态");
                }
            } else {
                log.writeLog("workcode:" + workcode + ";lastname:" + lastname + ",试用期到期日期为空，默认为试用状态");
                status = "试用";
            }
        }

        //转义特殊字符
        workcode = convertSpecialCharacter(workcode);
        email = convertSpecialCharacter(email);
        lastname = convertSpecialCharacter(lastname);
        sex = convertSpecialCharacter(sex);
        subcompany = convertSpecialCharacter(subcompany);
        locationid = convertSpecialCharacter(locationid);
        mobile = convertSpecialCharacter(mobile);
        birthday = convertSpecialCharacter(birthday);
        residentplace = convertSpecialCharacter(residentplace);
        log.writeLog("原jobtitle:" + jobtitle);
        jobtitle = convertSpecialCharacter(jobtitle);
        log.writeLog("转换后jobtitle:" + jobtitle);
        jobactivityid = convertSpecialCharacter(jobactivityid);
        jobgroupid = convertSpecialCharacter(jobgroupid);
        managerid = convertSpecialCharacter(managerid);
        //直属上级的workcode
        String managerCode = "";
        if (StringUtils.isBlank(jobtitle)) {
            log.writeLog("中间表a04也就是岗位jobtitle没有值，默认赋值为Default");
            jobtitle = "Default";
        }
        // 处理上级领导
        // TODO: 默认直接上级在本人数据之前生成，如果遇到问题这里再做修改
//        String qryManager = "select workcode from hrmresource a left join cus_fielddata b on a.id = b.id and scope = 'HrmCustomFieldByInfoType' and scopeid = -1 where b.field4 = '" + managerid + "'";
//        log.writeLog("qryManager:" + qryManager);
//        managerid = QueryUtil.doQueryFieldValue(qryManager, "workcode");
        //从中间表查上级
        RecordSetDataSource rsMan = new RecordSetDataSource("OAINTERFACE");
        String queryMan = "select employee_no from CITYRAY_TO_OA_INTERMEDIATE_TABLE where 1=1 and a05 = '" + managerid + "' ";
        rsMan.execute(queryMan);
        if (rsMan.next()) {
            managerCode = Util.null2String(rsMan.getString("employee_no"));
        }

        log.writeLog("managerCode:" + managerCode);
        boolean hasUser = checkWorkUser(managerCode);
        if (!hasUser) {
            //如果中间表中的上级不存在，或者在OA中没有，则在执行完接口后需要恢复原上级
            log.writeLog("中间表中上级员工：" + managerCode + "为空或者在OA中不存在，先置为空，接口执行后再恢复上级");
            managerCode = "";
            RecordSet rs = new RecordSet();
            String supManagerid;
            rs.executeQuery("select managerid from hrmresource where workcode = ?", workcode);
            if (rs.next()) {
                supManagerid = Util.null2String(rs.getString("managerid"));
                String recoverManagerSql = "update hrmresource set managerid = " + supManagerid + " where workcode = '" + workcode + "' ";
                result.put("recoverManagerSql", recoverManagerSql);
            }
        }

        log.writeLog("a16:" + a16);
        log.writeLog("a17:" + a17);
        log.writeLog("a02:" + a02);
        String department = "";
        //分部默认使用接口的分部，如果部门不存在，则要放入默认分部-部门
        String defaultSubCompany = Util.null2String(log.getPropValue("hrmsync", "defaultSubCompany"));
        String defaultDeptPath = Util.null2String(log.getPropValue("hrmsync", "defaultDeptPath"));
        String subDeptXml = "";
        log.writeLog("defaultSubCompany:" + defaultSubCompany);
        log.writeLog("defaultDeptPath:" + defaultDeptPath);
        department = a16;
        if (StringUtils.isBlank(department)) {
            department = a17;
        } else {
            department = department + ">" + a17;
        }
        if (StringUtils.isBlank(department)) {
            department = a02;
        } else {
            department = department + ">" + a02;
        }
        log.writeLog("department path:" + department);
        boolean deptFlag = checkDept(department, subcompany);
        department = convertSpecialCharacter(department);
        defaultDeptPath = convertSpecialCharacter(defaultDeptPath);
        log.writeLog("deptFlag:" + deptFlag);
        //判断是否是新员工
        if ("add".equals(action)) {
            //判断部门是否存在
            if (deptFlag) {
                //存在放入对应的分部-部门
                subDeptXml = "        <subcompany> " + subcompany + "</subcompany>\n" +
                        "        <department>" + department + "</department>\n";
            } else {
                //缺失部门的时候，放入默认部门路径（未分配人员）
                subDeptXml = "        <subcompany>" + defaultSubCompany + "</subcompany>\n" +
                        "        <department>" + defaultDeptPath + "</department>\n";
            }
        } else {
            //老员工情况下，如果部门存在，则变更部门
            //如果部门不存在，则不变更部门
            //如果接口部门在OA存在，则直接更新到新的部门
            if (deptFlag) {
                subDeptXml = "        <subcompany>" + subcompany + "</subcompany>\n" +
                        "        <department>" + department + "</department>\n";
            } else {
                //如果接口部门在OA不存在，要先挪到指定默认分部部门
                log.writeLog("老员工，先放到指定部门，接口执行后再恢复部门");
                subDeptXml = "        <subcompany>" + defaultSubCompany + "</subcompany>\n" +
                        "        <department>" + defaultDeptPath + "</department>\n";

                //不变更部门的情况下，需要将原始的部门路径拿过来--这种做法很麻烦，需要递归查部门名称，而且名称有奇怪的乱码
                //做法二，先根据工号获取当前的部门和分部id数据，接口完成之后，再用update语句更新回原来的分部部门
                RecordSet rs = new RecordSet();
                String supdepid, subcompanyid1;
                rs.executeQuery("select departmentid,subcompanyid1 from hrmresource where workcode = ?", workcode);
                if (rs.next()) {
                    supdepid = Util.null2String(rs.getString("departmentid"));
                    subcompanyid1 = Util.null2String(rs.getString("subcompanyid1"));
                    String recoverSql = "update hrmresource set departmentid = " + supdepid + ",subcompanyid1=" + subcompanyid1 + " where workcode = '" + workcode + "' ";

                    // 情况1：如果原有人分部id为213，则挪到指定的默认分部部门，即不记录在恢复sql列表里（未分配人员）
                    // 情况2：其他的，先挪到指定的默认分部部门，再恢复原有的部门
                    if (!SPECIAL_DEPTID.equals(supdepid)) {
                        result.put("recoverSql", recoverSql);
                        log.writeLog("recoverSql:" + recoverSql);
                    } else {
                        log.writeLog(workcode + "为老员工，但是部门为213，不恢复原有部门");
                    }

                }

            }
        }
        log.writeLog("final subDeptXml:" + subDeptXml);
        String xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<root>\n" +
                "  <hrmlist>\n" +
                "     <hrm action=\"" + action + "\">\n" +
                "        <workcode>" + workcode + "</workcode>\n" +
                "        <loginid>" + email + "</loginid>\n" +
                "        <lastname>" + lastname + "</lastname>\n" +
                "        <sex>" + sex + "</sex>\n" +
                subDeptXml +
                "        <status>" + status + "</status>\n" +
                seclevel +
                "        <locationid>" + locationid + "</locationid>\n" +
                "        <mobile>" + mobile + "</mobile>\n" +
                "        <email>" + email + "</email>\n" +
                "        <birthday>" + birthday + "</birthday>\n" +
                "        <residentplace>" + residentplace + "</residentplace>\n" +
                password +
                "        <jobtitle>" + jobtitle + "</jobtitle>\n" +
                "        <jobactivityid>" + jobactivityid + "</jobactivityid>\n" +
                "        <jobgroupid>" + jobgroupid + "</jobgroupid>\n" +
                "        <managerid>" + managerCode + "</managerid>\n" +
                "     </hrm>\n" +
                "  </hrmlist>\n" +
                "</root>";
        result.put("xml", xml);
        return result;
    }

    private String convertSpecialCharacter(String str) {
        String result = str;
        if (result.contains("&")) {
            log.writeLog("转&符号，原值：" + str);
            result = result.replaceAll("&", "&amp;");
        }
        if (result.contains("<")) {
            log.writeLog("转<符号，原值：" + str);
            result = result.replaceAll("<", "&lt;");
        }
        if (result.contains(">")) {
            log.writeLog("转>符号，原值：" + str);
            result = result.replaceAll(">", "&gt;");
        }
        if (result.contains("\"")) {
            log.writeLog("转双引号，原值：" + str);
            result = result.replaceAll("\"", "&quot;");
        }
        if (result.contains("'")) {
            log.writeLog("转双单引号，原值：" + str);
            result = result.replaceAll("'", "&apos;");
        }
        return result;
    }

    private boolean checkWorkUser(String workCode) {
        if (!workCode.isEmpty()) {
            RecordSet rs = new RecordSet();
            rs.executeQuery("select id from hrmresource where workcode = ?", workCode);
            return rs.next();
        } else {
            return false;
        }
    }

    /**
     * 是否找不到部门，false代表找不到，只要有一个部门找不到，就是false
     * 部门路径: 部门名称A>部门名称B
     * 按照从顶级部门从上往下找，完全匹配才行
     * 首先顶级部门要在对应的分部下
     *
     * @param deptPath
     * @return
     */
    private boolean checkDept(String deptPath, String subCompamy) {
        //是否找不到部门，false代表找不到，只要有一个部门找不到，就是false
        boolean deptFlag = true;
        //获取部门路径，如果有不存在的
        String[] pathArray = deptPath.split(">");
        int subDept;
        RecordSet rs = new RecordSet();
        String sql;
        String currentDeptStr, subDeptStr;
//        if (pathStr.length > 1) {
//            for (int i = pathStr.length - 1; i >= 0; i--) {
//                subDept = i - 1;
//                //当前部门
//                currentDeptStr = pathStr[i];
//                //除了最顶级，查询上下级部门关系是否存在
//                if (i > 0) {
//                    //上级部门
//                    subDeptStr = pathStr[subDept];
//                    //查询当前部门和上级部门关系是否存在
//                    rs = new RecordSet();
//                    sql = "select id from hrmdepartment where departmentname = ? " +
//                            " and supdepid in (select id from hrmdepartment where departmentname = ?)";
//                    log.writeLog("query subdept sql :" + sql);
//                    rs.executeQuery(sql, currentDeptStr, subDeptStr);
//                    if (!rs.next()) {
//                        deptFlag = false;
//                        break;
//                    }
//                }
//            }
//        } else {
//            rs = new RecordSet();
//            //查询顶级的部门id
//            sql = "select id from hrmdepartment where departmentname = ? and supdepid = 0 ";
//            log.writeLog("query oneDept sql :" + sql);
//            rs.executeQuery(sql, deptPath);
//            if (!rs.next()) {
//                deptFlag = false;
//            }
//        }
        //2023-09-06 修改，部门路径从顶级部门开始，自上而下全匹配，匹配不到则是false
        log.writeLog("查询分部部门路径是否存在:" + subCompamy + ",deptPath:" + deptPath);
        for (int i = 0; i < pathArray.length; i++) {
            currentDeptStr = pathArray[i];
            if (i == 0) {
                //分部为空的
                if (Util.null2String(subCompamy).isEmpty()) {
                    deptFlag = false;
                    break;
                }
                //查询顶级的部门id，排除封存的，并且要在对应的分部下
                sql = "select id from hrmdepartment where departmentname = '" + currentDeptStr + "' " +
                        "  and supdepid = 0 " +
                        " and (canceled != 1 or canceled is null) " +
                        " and subcompanyid1 in ( select id from hrmsubcompany where subcompanyname = '" + subCompamy + "' )";
                log.writeLog("query topDept sql :" + sql);
                if (rs.executeQuery(sql)) {
                    if (!rs.next()) {
                        deptFlag = false;
                        break;
                    } else {
                        log.writeLog("顶级部门存在:" + Util.null2String(rs.getString("id")));
                    }
                } else {
                    log.writeLog("顶级部门查询sql出错:" + rs.getExceptionMsg());
                    deptFlag = false;
                    break;
                }
            } else {
                //上级部门名称
                subDeptStr = pathArray[i - 1];
                //依次查询下级部门
                sql = "select id from hrmdepartment where departmentname = '" + currentDeptStr + "' " +
                        " and supdepid in (select id from hrmdepartment where departmentname = '" + subDeptStr + "' ) " +
                        " and (canceled != 1 or canceled is null) " +
                        //2025-08-11 bug修复，查询下级部门的时候，需要加上限制顶级分部名称，否则其他分部有同名的，也会当做存在的部门的了
                        " and subcompanyid1 in ( select id from hrmsubcompany where subcompanyname = '" + subCompamy + "' )";
                log.writeLog("query eachDept sql :" + sql);
                if (rs.executeQuery(sql)) {
                    if (!rs.next()) {
                        log.writeLog("下级部门不存在");
                        deptFlag = false;
                        break;
                    } else {
                        log.writeLog("下级部门存在:" + Util.null2String(rs.getString("id")));
                    }
                } else {
                    log.writeLog("下级部门查询sql出错:" + rs.getExceptionMsg());
                    deptFlag = false;
                    break;
                }
            }
        }
        log.writeLog("查询分部部门路径是否存在结果:" + deptFlag);
        return deptFlag;

    }

    private boolean executeXml(String xml) {
        boolean success = false;
        URL wsUrl;
        try {
            String oaAddress = log.getPropValue("hrmsync", "oaAddress");
            wsUrl = new URL(oaAddress + "/services/HrmService");
        } catch (MalformedURLException e) {
            log.writeLog("wsUrl expt:" + e.getMessage());
            return false;
        }

        HrmServiceLocator locator;
        HrmServiceHttpBindingStub service;
        try {
            locator = new HrmServiceLocator();
            log.writeLog("executeXml locator xml:" + xml);

            service = new HrmServiceHttpBindingStub(wsUrl, locator);
            log.writeLog("executeXml service");
            service.setTimeout(30000);
            String res = service.synHrmResource("", xml);
            log.writeLog("executeXml res:" + res);
            if (res.contains("成功")) {
                success = true;
            }
        } catch (Exception e) {
            log.writeLog("catch executeXml expt:" + e.getMessage() + ";" + Arrays.toString(e.getStackTrace()));
        }
        return success;
    }

    private void removeCache() throws Exception {
        ResourceComInfo resourcecominfo = new ResourceComInfo();
        DepartmentComInfo departmentComInfo = new DepartmentComInfo();
        SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
        JobActivitiesComInfo jobActivitiesComInfo = new JobActivitiesComInfo();
        JobTitlesComInfo jobTitlesComInfo = new JobTitlesComInfo();
        JobGroupsComInfo jobGroupsComInfo = new JobGroupsComInfo();
        resourcecominfo.removeResourceCache();
        departmentComInfo.removeCompanyCache();
        subCompanyComInfo.removeCompanyCache();
        jobActivitiesComInfo.removeJobActivitiesCache();
        jobTitlesComInfo.removeJobTitlesCache();
        jobGroupsComInfo.removeCompanyCache();
    }

    public BigDecimal getBigDecimalValue(String v, BigDecimal def) {
        try {
            if (StringUtils.isBlank(v)) {
                return new BigDecimal(0);
            }
            return new BigDecimal(v);
        } catch (Exception var3) {
            return def;
        }
    }


    private void setHrmMap() {
        RecordSet rs = new RecordSet();
        String sql = "select * from uf_rytbfbpzb";
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                fenbuMap.put(Util.null2String(rs.getString("rybhqz")), Util.null2String(rs.getString("fbmc")));
                zhiwuMap.put(Util.null2String(rs.getString("rybhqz")), Util.null2String(rs.getString("zwmc")));
            }
        }
    }

    /**
     * 设置Bu_hr 和对应分部的映射关系
     *
     * @return
     */
    private void setBuHrSubCompanyMap() {
        RecordSet rs = new RecordSet();
        String sql = "select * from uf_buhr_subcom ";
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                buhrSubCompanyMap.put(Util.null2String(rs.getString("buhr")), Util.null2String(rs.getString("dyfb")));
            }
        }
    }

    /**
     * 获取map中匹配的工号开头对应的值
     *
     * @param employeeNo
     * @param map
     * @return
     */
    private String getMapStartWith(String employeeNo, Map<String, String> map) {
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if (employeeNo.startsWith(entry.getKey())) {
                return entry.getValue();
            }
        }
        return "";
    }

    private void setWhiteHrmMap() {
        RecordSet rs = new RecordSet();
        String sql = "select * from uf_zjjgyytbbmd";
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                whiteHrmSet.add(Util.null2String(rs.getString("rybh")));

            }
        }
    }

    private String getUserIdByWorkCode(String workCode) {
        String userId = "";
        RecordSet rs = new RecordSet();
        String sql = "select id from hrmresource where workcode='" + workCode + "'";
        log.writeLog("getUserIdByWorkCode sql:" + sql);
        if (rs.executeQuery(sql)) {
            if (rs.next()) {
                userId = Util.null2String(rs.getString("id"));
            }
        } else {
            log.writeLog("getUserIdByWorkCode sql error:" + rs.getExceptionMsg() + ";sql:" + sql);
        }
        return userId;
    }

    private boolean isDatePlusOneDayAfterOrEqualToday(String dateString) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        try {
            LocalDate inputDate = LocalDate.parse(dateString, formatter);
            LocalDate inputDatePlusOne = inputDate.plusDays(1);
            LocalDate currentDate = LocalDate.now();

            return inputDatePlusOne.isAfter(currentDate) || inputDatePlusOne.isEqual(currentDate);
        } catch (DateTimeParseException e) {
            // 如果输入日期格式错误，可以抛出异常或者返回false
            // throw new IllegalArgumentException("Invalid date format, expected yyyy-MM-dd", e);
            return false;
        }
    }

}
