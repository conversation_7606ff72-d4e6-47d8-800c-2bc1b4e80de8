body {
    /*禁止手机端双击放大页面*/
    touch-action: manipulation;
}


/* 基础样式重置 */
.SD_Page * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    -webkit-tap-highlight-color: transparent;
}

.SD_Page {
    width: 100%;
    height: 100vh;
    background-color: #f0f0f0;
    color: #333;
    line-height: 1.5;
    -webkit-overflow-scrolling: touch;
    overflow: hidden;
}

/* 确保emoji图标正确显示 */
.SD_Page .form-icon {
    font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", "Segoe UI Symbol", sans-serif !important;
}

.container {
    width: 100%;
    height: 100%;
    padding: 0;
    background: #f0f0f0;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

/* 顶部搜索栏 */
.search-bar {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #fff;
    padding: 18px 20px 0px 20px;
    box-shadow: 0 4px 16px rgba(24, 144, 255, 0.08);
}

.search-box {
    display: flex;
    align-items: center;
    background-color: #f1f3f6;
    border-radius: 22px;
    padding: 10px 18px;
    width: 100%;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.04);
}

.search-box input {
    flex: 1;
    border: none;
    background: transparent;
    outline: none;
    font-size: 15px;
    padding: 4px 0;
    -webkit-appearance: none;
    appearance: none;
    user-select: text;
    -webkit-user-select: text;
    color: #333;
}

.search-box input::placeholder {
    color: #999;
}

.search-box input:focus {
    outline: none;
}

/* 搜索框交互效果 */
.search-box:hover,
.search-box:focus-within {
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.12);
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

/* 清除按钮交互效果 */
.clear-all-btn:hover,
.clear-all-btn:active,
.clear-all-btn:focus {
    color: #ff4d4f;
    transform: scale(1.2);
    transition: all 0.2s ease;
}

/* 移动端清除按钮触摸反馈 */
@media (hover: none) and (pointer: coarse) {
    .clear-all-btn:active {
        color: #ff4d4f;
        transform: scale(1.2);
        background-color: rgba(255, 77, 79, 0.1);
        border-radius: 50%;
    }
}

.search-icon {
    color: #1890ff;
    margin-right: 10px;
    font-size: 18px;
    flex-shrink: 0;
}

/* Tab切换 - 优化设计 */
.tab-container {
    display: flex;
    background-color: #fff;
    position: sticky;
    top: 74px;
    z-index: 20;
    padding: 0 20px;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.06);
    border-radius: 0 0 16px 16px;
    margin-bottom: 8px;
}

.tab-item {
    flex: 1;
    text-align: center;
    padding: 14px 0;
    font-size: 16px;
    font-weight: 500;
    color: #b0b0b0;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
    border-bottom: 3px solid transparent;
}

.tab-item.active {
    color: #1890ff;
    border-bottom: 3px solid #1890ff;
    background: none;
}

/* Tab项交互效果 - 兼容移动端 */
.tab-item:hover,
.tab-item:active,
.tab-item:focus {
    color: #1890ff;
    background-color: rgba(24, 144, 255, 0.05);
    transform: scale(1.03);
    transition: all 0.3s ease;
}

/* 移动端Tab触摸反馈 */
@media (hover: none) and (pointer: coarse) {
    .tab-item:active {
        background-color: rgba(24, 144, 255, 0.1);
        transform: scale(0.95);
    }
}

/* 分类标题 */
.category-title {
    font-size: 17px;
    font-weight: 600;
    color: #333;
    margin: 24px 20px 14px 20px;
    display: flex;
    align-items: center;
    letter-spacing: 0.5px;
}

.category-title:first-of-type {
    margin-top: 16px;
}

.category-title:before {
    content: '';
    display: inline-block;
    width: 5px;
    height: 18px;
    background-color: #1890ff;
    margin-right: 10px;
    border-radius: 2px;
    flex-shrink: 0;
}

/* 表单列表 - 缩小卡片并增加圆角 */
.form-list {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    width: 100%;
    margin: 0 0 20px 0;
    padding: 0 12px;
    align-items: stretch;
}

.form-card {
    background-color: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 16px rgba(24, 144, 255, 0.13);
    transition: transform 0.2s, box-shadow 0.2s;
    cursor: pointer;
    min-width: 0;
    word-break: break-all;
    font-size: 13px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    aspect-ratio: 1 / 0.8;
    padding: 12px 8px;
    justify-content: flex-start;
    position: relative;
}

/* 移动端hover效果 - 使用active状态替代hover */
.form-card:hover,
.form-card:active,
.form-card:focus {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 25px rgba(24, 144, 255, 0.25);
    transition: all 0.2s ease;
}

/* 移动端触摸反馈 */
@media (hover: none) and (pointer: coarse) {
    .form-card:active {
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 8px 25px rgba(24, 144, 255, 0.25);
        background-color: #f8f9fa;
    }
}

.form-icon {
    font-size: 18px;
    width: 28px;
    height: 28px;
    background-color: #e6f7ff;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    color: #1890ff;
    flex-shrink: 0;
    font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
    line-height: 1;
}

.form-name {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 4px;
    color: #222;
    line-height: 1.3;
    word-break: break-word;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.form-desc {
    font-size: 12px;
    color: #999;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    line-height: 1.2;
    margin-top: auto;
}

/* 暂无数据样式 - 添加白色背景并居中 */
.no-data {
    text-align: center;
    padding: 28px 0;
    color: #b0b0b0;
    font-size: 15px;
    background-color: #fff;
    border-radius: 14px;
    grid-column: 1 / -1;
    margin: 0;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.05);
}

/* 搜索匹配结果样式 */
.search-matched {
    display: block;
    background-color: #fff;
    border-radius: 12px;
    margin: 16px 20px;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.07);
}

.search-matched-title {
    font-size: 17px;
    font-weight: 600;
    color: #333;
    padding: 18px 18px 10px 18px;
    border-bottom: 1px solid #f0f0f0;
}

.search-history-section {
    background-color: #fff;
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 头部：标题（历史搜索）+ 删除按钮 左右分布 */
.history-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.history-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

/* 删除全部按钮 */
.clear-all-btn {
    background: transparent !important;
    border: none !important;
    cursor: pointer;
    font-size: 18px;
    padding: 0 !important;
    height: auto !important;
    line-height: 1 !important;
}

/* 标签列表：自动换行 + 间距 */
.tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

/* 单个历史标签：圆角 + 浅灰边框 + 悬浮高亮 */
.history-tag {
    padding: 4px 10px;
    border: 1px solid #e5e5e5;
    border-radius: 18px;
    color: #666;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s, color 0.2s;
    white-space: nowrap;
}

/* 历史标签交互效果 - 兼容移动端 */
.history-tag:hover,
.history-tag:active,
.history-tag:focus {
    background-color: #fafafa;
    color: #333;
    transform: scale(1.08);
    transition: all 0.2s ease;
}

/* 移动端历史标签触摸反馈 */
@media (hover: none) and (pointer: coarse) {
    .history-tag:active {
        background-color: #e6f7ff;
        color: #1890ff;
        transform: scale(1.08);
    }
}

/* 无历史时的提示 */
.no-history-tip {
    color: #999;
    font-size: 12px;
}

/* 原始表单容器 */
.original-forms {
    width: 100%;
}

/* 无权限状态样式 */
.no-permission {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    min-height: 40vh;
}

.no-permission-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.6;
}

.no-permission-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.no-permission-desc {
    font-size: 14px;
    color: #999;
    line-height: 1.5;
    max-width: 280px;
}


/* antd组件样式覆盖 */
.am-search-bar {
    background: transparent !important;
    border: none !important;
}

.am-search-bar .am-search-bar-input {
    background: transparent !important;
    border: none !important;
    font-size: 15px !important;
}

.am-search-bar .am-search-bar-input input {
    background: transparent !important;
    border: none !important;
    outline: none !important;
    font-size: 15px !important;
    padding: 4px 0 !important;
}