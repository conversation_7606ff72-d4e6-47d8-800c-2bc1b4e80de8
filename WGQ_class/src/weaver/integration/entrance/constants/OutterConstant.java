/*    */ package weaver.integration.entrance.constants;
/*    */ 
/*    */ import weaver.general.GCONST;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class OutterConstant
/*    */ {
/*    */   public static final String ENTRANCE_JSP = "/interface/Entrance.jsp";
/* 11 */   public static final String ENTRANCE_QQEMAIL_JSP = GCONST.getContextPath() + "/interface/EntranceQQEmail.jsp";
/* 12 */   public static final String ENTRANCE_EMAIL263_JSP = GCONST.getContextPath() + "/interface/Entrance_email263.jsp";
/* 13 */   public static final String ENTRANCE_EMAIL163_JSP = GCONST.getContextPath() + "/interface/Entrance_email163.jsp";
/* 14 */   public static final String K3CLOUD_ENTRANCE_JSP = GCONST.getContextPath() + "/interface/K3CloudEntrance.jsp";
/* 15 */   public static final String K3CLOUD_ENTRANCE_JSPV2 = GCONST.getContextPath() + "/interface/K3CloudEntranceV2.jsp";
/* 16 */   public static final String NC6ENTRANCE_JSP = GCONST.getContextPath() + "/interface/Nc6Entrance.jsp";
/* 17 */   public static final String NCENTRANCE_JSP = GCONST.getContextPath() + "/interface/NcEntrance.jsp";
/* 18 */   public static final String ENTRANCE_COREMAIL_JSP = GCONST.getContextPath() + "/interface/Entrance_CoreMail.jsp";
/* 19 */   public static final String NOIMPLEMENTED_JSP = GCONST.getContextPath() + "/interface/NoImplemented.jsp";
/* 20 */   public static final String EASENTRANCE_JSP = GCONST.getContextPath() + "/interface/EASEntrance.jsp";
/* 21 */   public static final String ALYENTRANCE_JSP = GCONST.getContextPath() + "/interface/EntranceALYMail.jsp";
/* 22 */   public static final String ENTRANCE_NEWEMAIL163_JSP = GCONST.getContextPath() + "/interface/Entrance_newemail263.jsp";
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/constants/OutterConstant.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */