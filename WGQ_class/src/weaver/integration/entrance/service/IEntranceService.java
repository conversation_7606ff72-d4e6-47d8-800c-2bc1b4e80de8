package weaver.integration.entrance.service;

import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import weaver.integration.entrance.bean.OutterLoginBean;

public interface IEntranceService {
  OutterLoginBean getLoginBean(IOutterSysServer paramIOutterSysServer, String paramString1, String paramString2, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ClassNotFoundException, NoSuchMethodException, IllegalAccessException, InstantiationException;
  
  Map<String, Object> getUrl(IOutterSysServer paramIOutterSysServer, OutterLoginBean paramOutterLoginBean, String paramString, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/service/IEntranceService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */