package weaver.integration.entrance.service.encrypt;

import java.util.Map;
import weaver.integration.entrance.bean.OutterLoginBean;
import weaver.integration.entrance.bean.OutterSysBean;
import weaver.integration.entrance.bean.OutterSysParamBean;

public interface IEncryptService {
  Map<String, Object> encryptAccountAndPassword(OutterSysBean paramOutterSysBean, OutterLoginBean paramOutterLoginBean);
  
  boolean encryptAccount(OutterSysBean paramOutterSysBean, OutterLoginBean paramOutterLoginBean);
  
  boolean encryptPassword(OutterSysBean paramOutterSysBean, OutterLoginBean paramOutterLoginBean);
  
  Map<String, Object> encryptParamValue(OutterSysBean paramOutterSysBean, OutterSysParamBean paramOutterSysParamBean);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/service/encrypt/IEncryptService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */