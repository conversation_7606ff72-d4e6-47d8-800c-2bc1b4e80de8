/*     */ package weaver.integration.entrance.service.encrypt;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.integration.entrance.bean.OutterLoginBean;
/*     */ import weaver.integration.entrance.bean.OutterSysBean;
/*     */ import weaver.integration.entrance.bean.OutterSysParamBean;
/*     */ import weaver.integration.entrance.enums.TypeName;
/*     */ import weaver.integration.entrance.utils.StringUtils;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ public class EncryptService
/*     */   implements IEncryptService
/*     */ {
/*  19 */   Logger log = LoggerFactory.getLogger();
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> encryptAccountAndPassword(OutterSysBean paramOutterSysBean, OutterLoginBean paramOutterLoginBean) {
/*  24 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  26 */     boolean bool1 = true;
/*  27 */     boolean bool2 = true;
/*  28 */     if (!TypeName.COMMON_TYPE.name().equals(paramOutterSysBean.getEncryptType())) {
/*     */       
/*  30 */       if ("1".equals(paramOutterSysBean.getUrlParaEncrypt1()) && !StringUtils.isBlank(paramOutterLoginBean.getAccount()) && !"7".equals(paramOutterSysBean.getTypeName()) && !"11".equals(paramOutterSysBean.getTypeName()) && 
/*  31 */         null != paramOutterSysBean.getObject() && null != paramOutterSysBean.getMethodEncode()) {
/*  32 */         bool1 = encryptAccount(paramOutterSysBean, paramOutterLoginBean);
/*     */       }
/*     */ 
/*     */ 
/*     */       
/*  37 */       if ("1".equals(paramOutterSysBean.getUrlParaEncrypt2()) && !StringUtils.isBlank(paramOutterLoginBean.getPassword()) && !"8".equals(paramOutterSysBean.getTypeName()) && 
/*  38 */         null != paramOutterSysBean.getObject() && null != paramOutterSysBean.getMethodEncode()) {
/*  39 */         bool2 = encryptPassword(paramOutterSysBean, paramOutterLoginBean);
/*     */       }
/*     */     } 
/*     */     
/*  43 */     if (bool1 && bool2) {
/*  44 */       return (Map)hashMap;
/*     */     }
/*  46 */     hashMap.put("isError", "true");
/*  47 */     hashMap.put("errorMsg", "" + SystemEnv.getHtmlLabelName(********, ThreadVarLanguage.getLang()) + "");
/*  48 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean encryptAccount(OutterSysBean paramOutterSysBean, OutterLoginBean paramOutterLoginBean) {
/*     */     try {
/*  54 */       if (StringUtils.isBlank(paramOutterSysBean.getEncryptCode1())) {
/*  55 */         paramOutterSysBean.setEncryptCode1(paramOutterSysBean.getOutterEncryptClassBean().getPassword());
/*     */       }
/*  57 */       if (null != paramOutterSysBean.getMethodSetpwd() && "1".equals(paramOutterSysBean.getOutterEncryptClassBean().getIsNeedPwd()) && 
/*  58 */         !StringUtils.isBlank(paramOutterSysBean.getEncryptCode1())) {
/*  59 */         Object[] arrayOfObject1 = new Object[1];
/*  60 */         arrayOfObject1[0] = paramOutterSysBean.getEncryptCode1();
/*  61 */         paramOutterSysBean.getMethodSetpwd().invoke(paramOutterSysBean.getObject(), arrayOfObject1);
/*     */       } 
/*     */       
/*  64 */       if (StringUtils.isBlank(paramOutterSysBean.getEncryptIv1())) {
/*  65 */         paramOutterSysBean.setEncryptIv1(paramOutterSysBean.getOutterEncryptClassBean().getIvParam());
/*     */       }
/*     */       
/*  68 */       if (null != paramOutterSysBean.getMethodSetiv() && "1".equals(paramOutterSysBean.getOutterEncryptClassBean().getIsNeedIv()) && 
/*  69 */         !StringUtils.isBlank(paramOutterSysBean.getEncryptIv1())) {
/*  70 */         Object[] arrayOfObject1 = new Object[1];
/*  71 */         arrayOfObject1[0] = paramOutterSysBean.getEncryptIv1();
/*  72 */         paramOutterSysBean.getMethodSetiv().invoke(paramOutterSysBean.getObject(), arrayOfObject1);
/*     */       } 
/*  74 */       Object[] arrayOfObject = new Object[1];
/*  75 */       arrayOfObject[0] = paramOutterLoginBean.getAccount();
/*  76 */       paramOutterLoginBean.setAccount((String)paramOutterSysBean.getMethodEncode().invoke(paramOutterSysBean.getObject(), arrayOfObject));
/*     */ 
/*     */       
/*  79 */       if (paramOutterLoginBean.getAccount() == null) {
/*  80 */         this.log.error(" - interface/Entrance.jsp：账号加密异常");
/*  81 */         return false;
/*     */       } 
/*  83 */       if ("-1".equals(paramOutterSysBean.getResult())) {
/*  84 */         paramOutterLoginBean.setAccount(paramOutterLoginBean.getAccount().toLowerCase());
/*  85 */       } else if ("1".equals(paramOutterSysBean.getResult())) {
/*  86 */         paramOutterLoginBean.setAccount(paramOutterLoginBean.getAccount().toUpperCase());
/*     */       } 
/*  88 */     } catch (Exception exception) {
/*  89 */       this.log.error(" - interface/Entrance.jsp：加密异常，请确认加密方法是否正确，密钥或向量长度是否符合要求", exception);
/*     */     } 
/*  91 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean encryptPassword(OutterSysBean paramOutterSysBean, OutterLoginBean paramOutterLoginBean) {
/*     */     try {
/*  97 */       if (StringUtils.isBlank(paramOutterSysBean.getEncryptCode2())) {
/*  98 */         paramOutterSysBean.setEncryptCode2(paramOutterSysBean.getOutterEncryptClassBean().getPassword());
/*     */       }
/*     */       
/* 101 */       if (null != paramOutterSysBean.getMethodSetpwd() && "1".equals(paramOutterSysBean.getOutterEncryptClassBean().getIsNeedPwd()) && 
/* 102 */         !StringUtils.isBlank(paramOutterSysBean.getEncryptCode2())) {
/* 103 */         Object[] arrayOfObject1 = new Object[1];
/* 104 */         arrayOfObject1[0] = paramOutterSysBean.getEncryptCode2();
/* 105 */         paramOutterSysBean.getMethodSetpwd().invoke(paramOutterSysBean.getObject(), arrayOfObject1);
/*     */       } 
/*     */       
/* 108 */       if (StringUtils.isBlank(paramOutterSysBean.getEncryptIv2())) {
/* 109 */         paramOutterSysBean.setEncryptIv1(paramOutterSysBean.getOutterEncryptClassBean().getIvParam());
/*     */       }
/*     */       
/* 112 */       if (null != paramOutterSysBean.getMethodSetiv() && "1".equals(paramOutterSysBean.getOutterEncryptClassBean().getIsNeedIv()) && 
/* 113 */         !StringUtils.isBlank(paramOutterSysBean.getEncryptIv2())) {
/* 114 */         Object[] arrayOfObject1 = new Object[1];
/* 115 */         arrayOfObject1[0] = paramOutterSysBean.getEncryptIv2();
/* 116 */         paramOutterSysBean.getMethodSetiv().invoke(paramOutterSysBean.getObject(), arrayOfObject1);
/*     */       } 
/* 118 */       Object[] arrayOfObject = new Object[1];
/* 119 */       arrayOfObject[0] = paramOutterLoginBean.getPassword();
/* 120 */       paramOutterLoginBean.setPassword((String)paramOutterSysBean.getMethodEncode().invoke(paramOutterSysBean.getObject(), arrayOfObject));
/*     */ 
/*     */       
/* 123 */       if (paramOutterLoginBean.getPassword() == null) {
/* 124 */         this.log.error(" - interface/Entrance.jsp：密码加密异常");
/* 125 */         return false;
/*     */       } 
/*     */       
/* 128 */       if ("-1".equals(paramOutterSysBean.getResult())) {
/* 129 */         paramOutterLoginBean.setPassword(paramOutterLoginBean.getPassword().toLowerCase());
/* 130 */       } else if ("1".equals(paramOutterSysBean.getResult())) {
/* 131 */         paramOutterLoginBean.setPassword(paramOutterLoginBean.getPassword().toUpperCase());
/*     */       } 
/* 133 */     } catch (Exception exception) {
/* 134 */       this.log.error(" - interface/Entrance.jsp：加密异常，请确认加密方法是否正确，密钥或向量长度是否符合要求", exception);
/*     */     } 
/* 136 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> encryptParamValue(OutterSysBean paramOutterSysBean, OutterSysParamBean paramOutterSysParamBean) {
/* 142 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 144 */     if (!"0".equals(paramOutterSysBean.getEncryptType()) && 
/* 145 */       "1".equals(paramOutterSysParamBean.getParaEncrypt()) && !StringUtils.isBlank(paramOutterSysParamBean.getParamValue()) && 
/* 146 */       null != paramOutterSysBean.getObject() && null != paramOutterSysBean.getMethodEncode()) {
/*     */       try {
/* 148 */         if (StringUtils.isBlank(paramOutterSysParamBean.getEncryptCode())) {
/* 149 */           paramOutterSysParamBean.setEncryptCode(paramOutterSysBean.getOutterEncryptClassBean().getPassword());
/*     */         }
/*     */         
/* 152 */         if (null != paramOutterSysBean.getMethodSetpwd() && "1".equals(paramOutterSysBean.getOutterEncryptClassBean().getIsNeedPwd()) && 
/* 153 */           !StringUtils.isBlank(paramOutterSysParamBean.getEncryptCode())) {
/* 154 */           Object[] arrayOfObject1 = new Object[1];
/* 155 */           arrayOfObject1[0] = paramOutterSysParamBean.getEncryptCode();
/* 156 */           paramOutterSysBean.getMethodSetpwd().invoke(paramOutterSysBean.getObject(), arrayOfObject1);
/*     */         } 
/*     */         
/* 159 */         if (StringUtils.isBlank(paramOutterSysParamBean.getEncryptIv())) {
/* 160 */           paramOutterSysParamBean.setEncryptCode(paramOutterSysBean.getOutterEncryptClassBean().getIvParam());
/*     */         }
/*     */         
/* 163 */         if (null != paramOutterSysBean.getMethodSetiv() && "1".equals(paramOutterSysBean.getOutterEncryptClassBean().getIsNeedIv()) && 
/* 164 */           !StringUtils.isBlank(paramOutterSysParamBean.getEncryptIv())) {
/* 165 */           Object[] arrayOfObject1 = new Object[1];
/* 166 */           arrayOfObject1[0] = paramOutterSysParamBean.getEncryptIv();
/* 167 */           paramOutterSysBean.getMethodSetiv().invoke(paramOutterSysBean.getObject(), arrayOfObject1);
/*     */         } 
/* 169 */         Object[] arrayOfObject = new Object[1];
/* 170 */         arrayOfObject[0] = paramOutterSysParamBean.getParamValue();
/* 171 */         paramOutterSysParamBean.setParamValue((String)paramOutterSysBean.getMethodEncode().invoke(paramOutterSysBean.getObject(), arrayOfObject));
/*     */ 
/*     */         
/* 174 */         if (paramOutterSysParamBean.getParamValue() == null) {
/* 175 */           hashMap.put("isError", "true");
/* 176 */           hashMap.put("errorMsg", "" + SystemEnv.getHtmlLabelName(10003689, ThreadVarLanguage.getLang()) + "");
/* 177 */           this.log.error("其他参数加密异常");
/* 178 */           return (Map)hashMap;
/*     */         } 
/*     */         
/* 181 */         if ("-1".equals(paramOutterSysBean.getResult())) {
/* 182 */           paramOutterSysParamBean.setParamValue(paramOutterSysParamBean.getParamValue().toLowerCase());
/* 183 */         } else if ("1".equals(paramOutterSysBean.getResult())) {
/* 184 */           paramOutterSysParamBean.setParamValue(paramOutterSysParamBean.getParamValue().toUpperCase());
/*     */         } 
/* 186 */       } catch (Exception exception) {
/* 187 */         hashMap.put("isError", "true");
/* 188 */         hashMap.put("errorMsg", "" + SystemEnv.getHtmlLabelName(10003690, ThreadVarLanguage.getLang()) + "");
/* 189 */         this.log.error(" - interface/Entrance.jsp：加密异常，请确认加密方法是否正确，密钥或向量长度是否符合要求", exception);
/* 190 */         return (Map)hashMap;
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/* 195 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/service/encrypt/EncryptService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */