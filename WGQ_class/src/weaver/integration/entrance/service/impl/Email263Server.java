/*    */ package weaver.integration.entrance.service.impl;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.general.Util;
/*    */ import weaver.integration.entrance.bean.OutterLoginBean;
/*    */ import weaver.integration.entrance.service.OutterSysServer;
/*    */ import weaver.integration.entrance.utils.StringUtils;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Email263Server
/*    */   extends OutterSysServer
/*    */ {
/*    */   public Map<String, Object> getRedirectUrl(OutterLoginBean paramOutterLoginBean, String paramString, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 21 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 22 */     if (StringUtils.isBlank(paramString) && 
/* 23 */       !StringUtils.isBlank(paramOutterLoginBean.getServerUrl()) && paramOutterLoginBean.getServerUrl().indexOf("accountSetting") > -1) {
/* 24 */       hashMap.put("isRedirect", "true");
/* 25 */       hashMap.put("redirectUrl", paramOutterLoginBean.getServerUrl());
/* 26 */       hashMap.put("previewUrl", paramOutterLoginBean.getServerUrl());
/* 27 */       return (Map)hashMap;
/*    */     } 
/*    */ 
/*    */     
/* 31 */     String str1 = paramOutterLoginBean.getOutterSysBean().getBaseParam1();
/*    */     
/* 33 */     String str2 = "";
/* 34 */     if (!StringUtils.isBlank(str1)) {
/* 35 */       str2 = "&" + str1 + "=" + paramOutterLoginBean.getAccount();
/*    */     }
/*    */     
/* 38 */     String str3 = "";
/* 39 */     String str4 = "";
/*    */     
/* 41 */     str3 = Util.getEncrypt("cid=" + paramOutterLoginBean.getOutterSysBean().getEmail263Cid() + "&domain=" + paramOutterLoginBean.getOutterSysBean().getEmail263Domain() + "&uid=" + paramOutterLoginBean
/* 42 */         .getAccount() + "&key=" + paramOutterLoginBean.getOutterSysBean().getEmail263Key());
/* 43 */     str4 = str4 + ((str4.length() == 0) ? "" : "&") + "cid=" + paramOutterLoginBean.getOutterSysBean().getEmail263Cid() + "";
/* 44 */     str4 = str4 + ((str4.length() == 0) ? "" : "&") + "domain=" + paramOutterLoginBean.getOutterSysBean().getEmail263Domain() + "";
/* 45 */     str4 = str4 + ((str4.length() == 0) ? "" : "") + str2 + "";
/* 46 */     str4 = str4 + ((str4.length() == 0) ? "" : "&") + "sign=" + str3 + "";
/*    */     
/* 48 */     String str5 = "";
/* 49 */     if (!StringUtils.isBlank(paramOutterLoginBean.getServerUrl()) && paramOutterLoginBean.getServerUrl().indexOf("?") > -1) {
/* 50 */       str5 = paramOutterLoginBean.getServerUrl() + "&" + str4;
/*    */     } else {
/* 52 */       str5 = paramOutterLoginBean.getServerUrl() + "?" + str4;
/*    */     } 
/* 54 */     hashMap.put("isRedirect", "false");
/* 55 */     hashMap.put("redirectUrl", "<script language='javascript'>window.location.href='" + str5 + "'</script>");
/* 56 */     hashMap.put("previewUrl", str5);
/* 57 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/service/impl/Email263Server.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */