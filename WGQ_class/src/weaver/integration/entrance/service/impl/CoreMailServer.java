/*     */ package weaver.integration.entrance.service.impl;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.integration.entrance.bean.OutterLoginBean;
/*     */ import weaver.integration.entrance.service.OutterSysServer;
/*     */ import weaver.integration.entrance.utils.StringUtils;
/*     */ import weaver.interfaces.email.CoreMailAPINew;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CoreMailServer
/*     */   extends OutterSysServer
/*     */ {
/*     */   public Map<String, Object> getRedirectUrl(OutterLoginBean paramOutterLoginBean, String paramString, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  27 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("mid"));
/*  28 */     User user = getUser(paramHttpServletRequest, paramHttpServletResponse);
/*  29 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  30 */     String str2 = paramOutterLoginBean.getOutterSysBean().getBaseParam2();
/*     */     
/*  32 */     if ((StringUtils.isBlank(paramString) || "main".equals(paramString)) && 
/*  33 */       !StringUtils.isBlank(paramOutterLoginBean.getServerUrl()) && paramOutterLoginBean.getServerUrl().indexOf("accountSetting") > -1) {
/*  34 */       hashMap.put("isRedirect", "true");
/*  35 */       hashMap.put("redirectUrl", paramOutterLoginBean.getServerUrl());
/*  36 */       hashMap.put("previewUrl", paramOutterLoginBean.getServerUrl());
/*  37 */       return (Map)hashMap;
/*     */     } 
/*     */ 
/*     */     
/*     */     try {
/*  42 */       CoreMailAPINew coreMailAPINew = CoreMailAPINew.getInstance(Integer.parseInt(paramOutterLoginBean.getOutterSysBean().getCoremailType()));
/*  43 */       boolean bool = CoreMailAPINew.InitClient();
/*  44 */       if (!bool) {
/*  45 */         hashMap.put("isError", "true");
/*  46 */         hashMap.put("errorMsg", SystemEnv.getHtmlLabelName(508549, Util.getIntValue(user.getLanguage())));
/*  47 */         return (Map)hashMap;
/*     */       } 
/*     */       
/*  50 */       if (StringUtils.isBlank(paramString) || "main".equals(paramString)) {
/*  51 */         String str = "";
/*  52 */         RecordSet recordSet = new RecordSet();
/*     */ 
/*     */         
/*  55 */         recordSet.execute("select email from hrmresource where id = " + user.getUID());
/*     */         
/*  57 */         if (recordSet.next()) {
/*  58 */           String str3 = Util.null2String(recordSet.getString("email"));
/*  59 */           if (!"".equals(str3)) {
/*  60 */             if (!"".equals(str2) && str2 != null) {
/*  61 */               if (CoreMailAPINew.authenticate(str3, paramOutterLoginBean.getPassword())) {
/*  62 */                 str = CoreMailAPINew.userLogin(str3);
/*  63 */                 if (!"".equals(str)) {
/*  64 */                   hashMap.put("isRedirect", "true");
/*  65 */                   hashMap.put("redirectUrl", paramOutterLoginBean.getServerUrl() + "?sid=" + str + "#mail.list|{\"fid\":1}");
/*  66 */                   hashMap.put("previewUrl", paramOutterLoginBean.getServerUrl() + "?sid=" + str + "#mail.list|{\"fid\":1}");
/*     */                 } else {
/*  68 */                   hashMap.put("isError", "true");
/*  69 */                   hashMap.put("errorMsg", SystemEnv.getHtmlLabelName(508555, Util.getIntValue(user.getLanguage())));
/*  70 */                   this.log.error("登录失败，CoreMail邮箱不存在此账号");
/*     */                 } 
/*     */               } else {
/*     */                 
/*  74 */                 String str4 = paramOutterLoginBean.getSysId();
/*  75 */                 RecordSet recordSet1 = new RecordSet();
/*  76 */                 recordSet1.executeQuery("select basetype2 from outter_sys where sysid=?", new Object[] { str4 });
/*  77 */                 recordSet1.next();
/*     */                 
/*  79 */                 if ("0".equals(recordSet1.getString("basetype2"))) {
/*  80 */                   this.log.info("CoreMail账号或密码错误并且为用户录入，跳转至密码修改页面");
/*  81 */                   hashMap.put("isRedirect", "false");
/*  82 */                   String str5 = "/spa/integration/static4engine/engine.html#/main/integration/accountSetting/" + str4;
/*  83 */                   hashMap.put("redirectUrl", "<script language='javascript'>window.location.href='" + str5 + "'</script>");
/*  84 */                   hashMap.put("previewUrl", str5);
/*     */                 } 
/*  86 */                 hashMap.put("isError", "true");
/*  87 */                 hashMap.put("errorMsg", SystemEnv.getHtmlLabelName(508556, Util.getIntValue(user.getLanguage())));
/*  88 */                 this.log.error("登录失败，请检查CoreMail账号或密码是否正确");
/*     */               } 
/*  90 */               return (Map)hashMap;
/*     */             } 
/*  92 */             str = CoreMailAPINew.userLogin(str3);
/*  93 */             if (!"".equals(str)) {
/*  94 */               hashMap.put("isRedirect", "true");
/*  95 */               hashMap.put("redirectUrl", paramOutterLoginBean.getServerUrl() + "?sid=" + str + "#mail.list|{\"fid\":1}");
/*  96 */               hashMap.put("previewUrl", paramOutterLoginBean.getServerUrl() + "?sid=" + str + "#mail.list|{\"fid\":1}");
/*     */             } else {
/*  98 */               hashMap.put("isError", "true");
/*  99 */               hashMap.put("errorMsg", SystemEnv.getHtmlLabelName(508555, Util.getIntValue(user.getLanguage())));
/* 100 */               this.log.error("登录失败，CoreMail邮箱不存在此账号");
/*     */             } 
/* 102 */             return (Map)hashMap;
/*     */           } 
/*     */         } else {
/*     */           
/* 106 */           hashMap.put("isError", "true");
/* 107 */           hashMap.put("errorMsg", SystemEnv.getHtmlLabelName(508599, Util.getIntValue(user.getLanguage())));
/* 108 */           return (Map)hashMap;
/*     */         } 
/*     */       } else {
/* 111 */         String str3 = "";
/* 112 */         String str4 = Util.null2String(paramHttpServletRequest.getParameter("email"));
/* 113 */         if (!"".equals(str4)) {
/* 114 */           if (!"".equals(str2) && str2 != null) {
/* 115 */             if (CoreMailAPINew.authenticate(str4, paramOutterLoginBean.getPassword())) {
/* 116 */               str3 = CoreMailAPINew.userLogin(str4);
/* 117 */               if (!"".equals(str3)) {
/* 118 */                 hashMap.put("isRedirect", "true");
/* 119 */                 hashMap.put("redirectUrl", paramOutterLoginBean.getServerUrl() + "?sid=" + str3 + "#mail.list|{\"fid\":1}");
/* 120 */                 hashMap.put("previewUrl", paramOutterLoginBean.getServerUrl() + "?sid=" + str3 + "#mail.list|{\"fid\":1}");
/*     */               } else {
/* 122 */                 hashMap.put("isError", "true");
/* 123 */                 hashMap.put("errorMsg", SystemEnv.getHtmlLabelName(508555, Util.getIntValue(user.getLanguage())));
/* 124 */                 this.log.error("登录失败，CoreMail邮箱不存在此账号");
/*     */               } 
/*     */             } else {
/*     */               
/* 128 */               String str = paramOutterLoginBean.getSysId();
/* 129 */               RecordSet recordSet = new RecordSet();
/* 130 */               recordSet.executeQuery("select basetype2 from outter_sys where sysid=?", new Object[] { str });
/* 131 */               recordSet.next();
/*     */               
/* 133 */               if ("0".equals(recordSet.getString("basetype2"))) {
/* 134 */                 this.log.info("CoreMail账号或密码错误并且为用户录入，跳转至密码修改页面");
/* 135 */                 hashMap.put("isRedirect", "false");
/* 136 */                 String str5 = "/spa/integration/static4engine/engine.html#/main/integration/accountSetting/" + str;
/* 137 */                 hashMap.put("redirectUrl", "<script language='javascript'>window.location.href='" + str5 + "'</script>");
/* 138 */                 hashMap.put("previewUrl", str5);
/*     */               } 
/* 140 */               hashMap.put("isError", "true");
/* 141 */               hashMap.put("errorMsg", SystemEnv.getHtmlLabelName(508556, Util.getIntValue(user.getLanguage())));
/* 142 */               this.log.error("登录失败，请检查CoreMail账号或密码是否正确！");
/*     */             } 
/* 144 */             return (Map)hashMap;
/*     */           } 
/*     */           
/* 147 */           str3 = CoreMailAPINew.userLogin(str4);
/* 148 */           if (!"".equals(str3)) {
/* 149 */             hashMap.put("isRedirect", "true");
/* 150 */             hashMap.put("redirectUrl", paramOutterLoginBean.getServerUrl() + "?sid=" + str3 + "#mail.list|{\"fid\":1}");
/* 151 */             hashMap.put("previewUrl", paramOutterLoginBean.getServerUrl() + "?sid=" + str3 + "#mail.list|{\"fid\":1}");
/*     */           } else {
/* 153 */             hashMap.put("isError", "true");
/* 154 */             hashMap.put("errorMsg", SystemEnv.getHtmlLabelName(508555, Util.getIntValue(user.getLanguage())));
/* 155 */             this.log.error("登录失败，CoreMail邮箱不存在此账号！");
/*     */           } 
/* 157 */           return (Map)hashMap;
/*     */         }
/*     */       
/*     */       } 
/* 161 */     } catch (Exception exception) {
/* 162 */       hashMap.put("isError", "true");
/* 163 */       hashMap.put("errorMsg", SystemEnv.getHtmlLabelName(508559, Util.getIntValue(user.getLanguage())));
/* 164 */       this.log.error("发生异常: " + exception.getMessage());
/*     */     } 
/*     */     
/* 167 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/service/impl/CoreMailServer.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */