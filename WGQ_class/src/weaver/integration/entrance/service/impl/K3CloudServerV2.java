/*     */ package weaver.integration.entrance.service.impl;
/*     */ 
/*     */ import java.io.UnsupportedEncodingException;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.K3Utils;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.integration.entrance.bean.OutterLoginBean;
/*     */ import weaver.integration.entrance.service.OutterSysServer;
/*     */ import weaver.integration.entrance.utils.StringUtils;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class K3CloudServerV2
/*     */   extends OutterSysServer
/*     */ {
/*     */   public Map<String, Object> getRedirectUrl(OutterLoginBean paramOutterLoginBean, String paramString, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  25 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */     
/*  27 */     User user = getUser(paramHttpServletRequest, paramHttpServletResponse);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  33 */     if (StringUtils.isBlank(paramOutterLoginBean.getOutterSysBean().getK3server())) {
/*  34 */       hashMap1.put("isError", "true");
/*  35 */       hashMap1.put("errorMsg", SystemEnv.getHtmlLabelName(508566, Util.getIntValue(user.getLanguage())));
/*  36 */       return (Map)hashMap1;
/*     */     } 
/*  38 */     if (StringUtils.isBlank(paramOutterLoginBean.getOutterSysBean().getDbid())) {
/*  39 */       hashMap1.put("isError", "true");
/*  40 */       hashMap1.put("errorMsg", SystemEnv.getHtmlLabelName(508567, Util.getIntValue(user.getLanguage())));
/*  41 */       return (Map)hashMap1;
/*     */     } 
/*  43 */     if (StringUtils.isBlank(getUser(paramHttpServletRequest, paramHttpServletResponse).getLoginid())) {
/*  44 */       hashMap1.put("isError", "true");
/*  45 */       hashMap1.put("errorMsg", SystemEnv.getHtmlLabelName(508568, Util.getIntValue(user.getLanguage())));
/*  46 */       return (Map)hashMap1;
/*     */     } 
/*  48 */     if (StringUtils.isBlank(paramOutterLoginBean.getOutterSysBean().getAppid())) {
/*  49 */       hashMap1.put("isError", "true");
/*  50 */       hashMap1.put("errorMsg", SystemEnv.getHtmlLabelName(508569, Util.getIntValue(user.getLanguage())));
/*  51 */       return (Map)hashMap1;
/*     */     } 
/*  53 */     if (StringUtils.isBlank(paramOutterLoginBean.getOutterSysBean().getAppsecret())) {
/*  54 */       hashMap1.put("isError", "true");
/*  55 */       hashMap1.put("errorMsg", SystemEnv.getHtmlLabelName(508570, Util.getIntValue(user.getLanguage())));
/*  56 */       return (Map)hashMap1;
/*     */     } 
/*  58 */     if (StringUtils.isBlank(paramOutterLoginBean.getOutterSysBean().getTimestamp())) {
/*  59 */       hashMap1.put("isError", "true");
/*  60 */       hashMap1.put("errorMsg", SystemEnv.getHtmlLabelName(508597, Util.getIntValue(user.getLanguage())));
/*  61 */       return (Map)hashMap1;
/*     */     } 
/*  63 */     if (StringUtils.isBlank(paramOutterLoginBean.getOutterSysBean().getLcid())) {
/*  64 */       hashMap1.put("isError", "true");
/*  65 */       hashMap1.put("errorMsg", SystemEnv.getHtmlLabelName(508571, Util.getIntValue(user.getLanguage())));
/*  66 */       return (Map)hashMap1;
/*     */     } 
/*     */     
/*  69 */     String str1 = "";
/*     */     
/*  71 */     if (1 == paramOutterLoginBean.getOutterSysBean().getBaseType1().intValue()) {
/*  72 */       str1 = getUser(paramHttpServletRequest, paramHttpServletResponse).getLoginid();
/*     */     } else {
/*  74 */       str1 = getUser(paramHttpServletRequest, paramHttpServletResponse).getLoginid();
/*  75 */       RecordSet recordSet = new RecordSet();
/*  76 */       recordSet.executeQuery("SELECT * FROM outter_account WHERE sysid=? AND userid=?", new Object[] { paramOutterLoginBean.getOutterSysBean().getSysId(), Integer.valueOf(getUser(paramHttpServletRequest, paramHttpServletResponse).getUID()) });
/*  77 */       if (recordSet.next()) {
/*  78 */         str1 = recordSet.getString("account");
/*     */       }
/*     */     } 
/*     */     
/*  82 */     if (!StringUtils.isBlank(paramString)) {
/*  83 */       str1 = paramOutterLoginBean.getAccount();
/*     */     }
/*     */     
/*  86 */     String str2 = paramOutterLoginBean.getOutterSysBean().getBaseParam1();
/*  87 */     str1 = encodeStr(str1, paramOutterLoginBean.getOutterSysBean().getUrlEncodeFlag());
/*     */     
/*  89 */     String str3 = "";
/*  90 */     String str4 = "";
/*     */     
/*  92 */     Map<?, ?> map = paramOutterLoginBean.getParamValues();
/*  93 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */     
/*  95 */     hashMap2.putAll(map);
/*     */     
/*  97 */     Iterator<Map.Entry> iterator = map.entrySet().iterator();
/*     */     
/*  99 */     while (iterator.hasNext()) {
/* 100 */       Map.Entry entry = iterator.next();
/* 101 */       if ("k3server".equals(entry.getKey())) {
/* 102 */         iterator.remove();
/*     */       }
/* 104 */       if ("dbid".equals(entry.getKey())) {
/* 105 */         iterator.remove();
/*     */       }
/* 107 */       if ("appid".equals(entry.getKey())) {
/* 108 */         iterator.remove();
/*     */       }
/* 110 */       if ("appsecret".equals(entry.getKey())) {
/* 111 */         iterator.remove();
/*     */       }
/* 113 */       if ("lcid".equals(entry.getKey())) {
/* 114 */         iterator.remove();
/*     */       }
/* 116 */       if ("origintype".equals(entry.getKey())) {
/* 117 */         iterator.remove();
/*     */       }
/*     */     } 
/*     */     
/*     */     try {
/* 122 */       str3 = K3Utils.getK3CloudUrlV2New((String)hashMap2.get("k3server"), (String)hashMap2.get("dbid"), str2, str1, (String)hashMap2
/* 123 */           .get("appid"), (String)hashMap2.get("appsecret"), paramOutterLoginBean
/* 124 */           .getOutterSysBean().getTimestamp(), (String)hashMap2.get("lcid"), (String)hashMap2.get("origintype"), map);
/*     */       
/* 126 */       hashMap1.put("isRedirect", "true");
/* 127 */       hashMap1.put("redirectUrl", str3);
/*     */       
/* 129 */       str4 = K3Utils.getK3CloudUrlV2PRENew((String)hashMap2.get("k3server"), (String)hashMap2.get("dbid"), str2, str1, (String)hashMap2
/* 130 */           .get("appid"), (String)hashMap2.get("appsecret"), paramOutterLoginBean
/* 131 */           .getOutterSysBean().getTimestamp(), (String)hashMap2.get("lcid"), (String)hashMap2.get("origintype"), map);
/*     */ 
/*     */       
/* 134 */       hashMap1.put("previewUrl", str4);
/* 135 */     } catch (UnsupportedEncodingException unsupportedEncodingException) {
/* 136 */       unsupportedEncodingException.printStackTrace();
/* 137 */       hashMap1.put("isError", "true");
/* 138 */       hashMap1.put("errorMsg", SystemEnv.getHtmlLabelName(508572, Util.getIntValue(user.getLanguage())));
/*     */     } 
/* 140 */     return (Map)hashMap1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/service/impl/K3CloudServerV2.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */