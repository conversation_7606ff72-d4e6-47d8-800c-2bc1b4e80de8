/*    */ package weaver.integration.entrance.service.impl;
/*    */ 
/*    */ import java.net.HttpURLConnection;
/*    */ import java.net.MalformedURLException;
/*    */ import java.net.URL;
/*    */ import java.net.URLConnection;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.integration.entrance.bean.OutterLoginBean;
/*    */ import weaver.integration.entrance.service.OutterSysServer;
/*    */ import weaver.integration.entrance.utils.StringUtils;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Nc6Server
/*    */   extends OutterSysServer
/*    */ {
/*    */   public Map<String, Object> getRedirectUrl(OutterLoginBean paramOutterLoginBean, String paramString, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 27 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 29 */     User user = getUser(paramHttpServletRequest, paramHttpServletResponse);
/*    */     
/* 31 */     if (StringUtils.isBlank(paramString) && 
/* 32 */       !StringUtils.isBlank(paramOutterLoginBean.getServerUrl()) && paramOutterLoginBean.getServerUrl().indexOf("accountSetting") > -1) {
/* 33 */       hashMap.put("isRedirect", "true");
/* 34 */       hashMap.put("redirectUrl", paramOutterLoginBean.getServerUrl());
/* 35 */       hashMap.put("previewUrl", paramOutterLoginBean.getServerUrl());
/* 36 */       return (Map)hashMap;
/*    */     } 
/*    */ 
/*    */     
/* 40 */     String str1 = paramHttpServletRequest.getRequestedSessionId();
/* 41 */     String str2 = paramOutterLoginBean.getOutterSysBean().getBaseParam1();
/*    */     
/* 43 */     String str3 = "";
/* 44 */     if (!StringUtils.isBlank(str2)) {
/* 45 */       str3 = "&" + str2 + "=" + encodeStr(paramOutterLoginBean.getAccount(), paramOutterLoginBean.getOutterSysBean().getUrlEncodeFlag());
/*    */     }
/*    */ 
/*    */     
/* 49 */     String str4 = "";
/* 50 */     for (Map.Entry entry : paramOutterLoginBean.getParamValues().entrySet()) {
/* 51 */       str4 = str4 + "&" + (String)entry.getKey() + "=" + (String)entry.getValue();
/*    */     }
/*    */     
/* 54 */     String str5 = str1;
/* 55 */     URL uRL = null;
/*    */     
/*    */     try {
/* 58 */       uRL = new URL(paramOutterLoginBean.getServerUrl() + "/service/ssoRegServlet?ssoKey=" + str5 + str3 + str4);
/* 59 */     } catch (MalformedURLException malformedURLException) {
/* 60 */       hashMap.put("isError", "true");
/* 61 */       hashMap.put("errorMsg", SystemEnv.getHtmlLabelName(508598, Util.getIntValue(user.getLanguage())));
/* 62 */       malformedURLException.printStackTrace();
/* 63 */       return (Map)hashMap;
/*    */     } 
/*    */     
/* 66 */     URLConnection uRLConnection = null;
/* 67 */     String str6 = null;
/* 68 */     int i = 0;
/*    */     try {
/* 70 */       uRLConnection = uRL.openConnection();
/* 71 */       uRLConnection.setConnectTimeout(24000);
/* 72 */       uRLConnection.setReadTimeout(24000);
/* 73 */       uRLConnection.setDoOutput(true);
/*    */       
/* 75 */       HttpURLConnection httpURLConnection = (HttpURLConnection)uRLConnection;
/* 76 */       str6 = httpURLConnection.getResponseMessage();
/* 77 */       i = httpURLConnection.getResponseCode();
/* 78 */     } catch (Exception exception) {
/* 79 */       hashMap.put("isError", "true");
/* 80 */       hashMap.put("errorMsg", SystemEnv.getHtmlLabelName(510042, user.getLanguage()) + uRL.toString());
/* 81 */       exception.printStackTrace();
/* 82 */       return (Map)hashMap;
/*    */     } 
/*    */     
/* 85 */     if (str6.equals("OK")) {
/* 86 */       hashMap.put("isRedirect", "true");
/* 87 */       hashMap.put("redirectUrl", paramOutterLoginBean.getServerUrl() + "/login.jsp?ssoKey=" + str5);
/* 88 */       hashMap.put("previewUrl", paramOutterLoginBean.getServerUrl() + "/login.jsp?ssoKey=" + str5);
/* 89 */       return (Map)hashMap;
/*    */     } 
/* 91 */     hashMap.put("isError", "true");
/* 92 */     if (i == 403) {
/* 93 */       hashMap.put("errorMsg", SystemEnv.getHtmlLabelName(510030, user.getLanguage()) + uRL.toString());
/* 94 */       return (Map)hashMap;
/*    */     } 
/* 96 */     hashMap.put("errorMsg", SystemEnv.getHtmlLabelName(510031, user.getLanguage()) + uRL.toString());
/* 97 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/service/impl/Nc6Server.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */