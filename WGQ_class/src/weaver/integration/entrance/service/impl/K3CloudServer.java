/*     */ package weaver.integration.entrance.service.impl;
/*     */ 
/*     */ import java.io.UnsupportedEncodingException;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.K3Utils;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.integration.entrance.bean.OutterLoginBean;
/*     */ import weaver.integration.entrance.service.OutterSysServer;
/*     */ import weaver.integration.entrance.utils.StringUtils;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class K3CloudServer
/*     */   extends OutterSysServer
/*     */ {
/*     */   public Map<String, Object> getRedirectUrl(OutterLoginBean paramOutterLoginBean, String paramString, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  26 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */     
/*  28 */     User user = getUser(paramHttpServletRequest, paramHttpServletResponse);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  44 */     if (StringUtils.isBlank(paramOutterLoginBean.getOutterSysBean().getK3server())) {
/*  45 */       hashMap1.put("isError", "true");
/*  46 */       hashMap1.put("errorMsg", SystemEnv.getHtmlLabelName(508566, Util.getIntValue(user.getLanguage())));
/*  47 */       return (Map)hashMap1;
/*     */     } 
/*  49 */     if (StringUtils.isBlank(paramOutterLoginBean.getOutterSysBean().getDbid())) {
/*  50 */       hashMap1.put("isError", "true");
/*  51 */       hashMap1.put("errorMsg", SystemEnv.getHtmlLabelName(508567, Util.getIntValue(user.getLanguage())));
/*  52 */       return (Map)hashMap1;
/*     */     } 
/*  54 */     if (StringUtils.isBlank(getUser(paramHttpServletRequest, paramHttpServletResponse).getLoginid())) {
/*  55 */       hashMap1.put("isError", "true");
/*  56 */       hashMap1.put("errorMsg", SystemEnv.getHtmlLabelName(508568, Util.getIntValue(user.getLanguage())));
/*  57 */       return (Map)hashMap1;
/*     */     } 
/*  59 */     if (StringUtils.isBlank(paramOutterLoginBean.getOutterSysBean().getAppid())) {
/*  60 */       hashMap1.put("isError", "true");
/*  61 */       hashMap1.put("errorMsg", SystemEnv.getHtmlLabelName(508569, Util.getIntValue(user.getLanguage())));
/*  62 */       return (Map)hashMap1;
/*     */     } 
/*  64 */     if (StringUtils.isBlank(paramOutterLoginBean.getOutterSysBean().getAppsecret())) {
/*  65 */       hashMap1.put("isError", "true");
/*  66 */       hashMap1.put("errorMsg", SystemEnv.getHtmlLabelName(508570, Util.getIntValue(user.getLanguage())));
/*  67 */       return (Map)hashMap1;
/*     */     } 
/*  69 */     if (StringUtils.isBlank(paramOutterLoginBean.getOutterSysBean().getTimestamp())) {
/*  70 */       hashMap1.put("isError", "true");
/*  71 */       hashMap1.put("errorMsg", SystemEnv.getHtmlLabelName(508597, Util.getIntValue(user.getLanguage())));
/*  72 */       return (Map)hashMap1;
/*     */     } 
/*  74 */     if (StringUtils.isBlank(paramOutterLoginBean.getOutterSysBean().getLcid())) {
/*  75 */       hashMap1.put("isError", "true");
/*  76 */       hashMap1.put("errorMsg", SystemEnv.getHtmlLabelName(508571, Util.getIntValue(user.getLanguage())));
/*  77 */       return (Map)hashMap1;
/*     */     } 
/*     */     
/*  80 */     String str1 = "";
/*     */ 
/*     */     
/*  83 */     if (1 == paramOutterLoginBean.getOutterSysBean().getBaseType1().intValue()) {
/*  84 */       str1 = getUser(paramHttpServletRequest, paramHttpServletResponse).getLoginid();
/*     */     } else {
/*  86 */       str1 = getUser(paramHttpServletRequest, paramHttpServletResponse).getLoginid();
/*  87 */       RecordSet recordSet = new RecordSet();
/*  88 */       recordSet.executeQuery("SELECT * FROM outter_account WHERE sysid=? AND userid=?", new Object[] { paramOutterLoginBean.getOutterSysBean().getSysId(), Integer.valueOf(getUser(paramHttpServletRequest, paramHttpServletResponse).getUID()) });
/*  89 */       if (recordSet.next()) {
/*  90 */         str1 = recordSet.getString("account");
/*     */       }
/*     */     } 
/*     */     
/*  94 */     if (!StringUtils.isBlank(paramString)) {
/*  95 */       str1 = paramOutterLoginBean.getAccount();
/*     */     }
/*  97 */     str1 = encodeStr(str1, paramOutterLoginBean.getOutterSysBean().getUrlEncodeFlag());
/*     */     
/*  99 */     String str2 = "";
/* 100 */     String str3 = "";
/*     */     
/* 102 */     Map map = paramOutterLoginBean.getParamValues();
/* 103 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 104 */     Iterator<Map.Entry> iterator = map.entrySet().iterator();
/*     */     
/* 106 */     while (iterator.hasNext()) {
/* 107 */       Map.Entry entry = iterator.next();
/* 108 */       hashMap2.put(entry.getKey(), entry.getValue());
/* 109 */       if ("k3server".equals(entry.getKey())) {
/* 110 */         iterator.remove();
/*     */       }
/* 112 */       if ("dbid".equals(entry.getKey())) {
/* 113 */         iterator.remove();
/*     */       }
/* 115 */       if ("appid".equals(entry.getKey())) {
/* 116 */         iterator.remove();
/*     */       }
/* 118 */       if ("appsecret".equals(entry.getKey())) {
/* 119 */         iterator.remove();
/*     */       }
/* 121 */       if ("lcid".equals(entry.getKey())) {
/* 122 */         iterator.remove();
/*     */       }
/*     */     } 
/*     */     
/*     */     try {
/* 127 */       str2 = K3Utils.getK3CloudUrlNew((String)hashMap2.get("k3server"), (String)hashMap2.get("dbid"), str1, (String)hashMap2
/* 128 */           .get("appid"), (String)hashMap2.get("appsecret"), paramOutterLoginBean
/* 129 */           .getOutterSysBean().getTimestamp(), (String)hashMap2.get("lcid"), map);
/*     */       
/* 131 */       hashMap1.put("isRedirect", "true");
/* 132 */       hashMap1.put("redirectUrl", str2);
/*     */       
/* 134 */       str3 = K3Utils.getK3CloudUrlPRENew((String)hashMap2.get("k3server"), (String)hashMap2.get("dbid"), str1, (String)hashMap2
/* 135 */           .get("appid"), (String)hashMap2.get("appsecret"), paramOutterLoginBean
/* 136 */           .getOutterSysBean().getTimestamp(), (String)hashMap2.get("lcid"), map);
/*     */       
/* 138 */       hashMap1.put("previewUrl", str3);
/* 139 */     } catch (UnsupportedEncodingException unsupportedEncodingException) {
/* 140 */       unsupportedEncodingException.printStackTrace();
/* 141 */       hashMap1.put("isError", "true");
/* 142 */       hashMap1.put("errorMsg", SystemEnv.getHtmlLabelName(508572, Util.getIntValue(user.getLanguage())));
/*     */     } 
/*     */     
/* 145 */     return (Map)hashMap1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/service/impl/K3CloudServer.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */