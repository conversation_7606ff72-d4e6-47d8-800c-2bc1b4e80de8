/*    */ package weaver.integration.entrance.service.impl;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.integration.entrance.bean.OutterLoginBean;
/*    */ import weaver.integration.entrance.service.OutterSysServer;
/*    */ import weaver.integration.entrance.utils.StringUtils;
/*    */ import weaver.interfaces.email.EntranceALYEmail;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ALYMailServer
/*    */   extends OutterSysServer
/*    */ {
/*    */   public Map<String, Object> getRedirectUrl(OutterLoginBean paramOutterLoginBean, String paramString, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 25 */     RecordSet recordSet = new RecordSet();
/* 26 */     EntranceALYEmail entranceALYEmail = new EntranceALYEmail();
/*    */     
/* 28 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 30 */     User user = getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 31 */     String str1 = null;
/* 32 */     if (StringUtils.isBlank(paramString)) {
/* 33 */       recordSet.execute("select * from hrmresource where id = " + user.getUID());
/* 34 */       if (!recordSet.next()) {
/* 35 */         hashMap.put("isError", "true");
/* 36 */         hashMap.put("errorMsg", SystemEnv.getHtmlLabelNames("508599", user.getLanguage()));
/* 37 */         return (Map)hashMap;
/*    */       } 
/* 39 */       String str = Util.null2String(recordSet.getString("email"));
/* 40 */       if (!StringUtils.isBlank(str)) {
/* 41 */         str1 = entranceALYEmail.getSingleUrl(str, paramOutterLoginBean.getOutterSysBean().getSysId());
/* 42 */         hashMap.put("isRedirect", "true");
/* 43 */         hashMap.put("redirectUrl", str1);
/* 44 */         hashMap.put("previewUrl", str1);
/*    */       } else {
/* 46 */         hashMap.put("isError", "true");
/* 47 */         hashMap.put("errorMsg", SystemEnv.getHtmlLabelNames("508600", user.getLanguage()));
/*    */       } 
/* 49 */       return (Map)hashMap;
/*    */     } 
/*    */     
/* 52 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("email"));
/* 53 */     if (!StringUtils.isBlank(str2)) {
/* 54 */       str1 = entranceALYEmail.getSingleUrl(str2, paramOutterLoginBean.getOutterSysBean().getSysId());
/* 55 */       hashMap.put("isRedirect", "true");
/* 56 */       hashMap.put("redirectUrl", str1);
/* 57 */       hashMap.put("previewUrl", str1);
/*    */     } else {
/* 59 */       hashMap.put("isError", "true");
/* 60 */       hashMap.put("errorMsg", SystemEnv.getHtmlLabelNames("508600", user.getLanguage()));
/*    */     } 
/* 62 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/service/impl/ALYMailServer.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */