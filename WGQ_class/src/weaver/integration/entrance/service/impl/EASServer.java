/*    */ package weaver.integration.entrance.service.impl;
/*    */ 
/*    */ import com.kingdee.eas.cp.eip.sso.ltpa.LtpaTokenManager;
/*    */ import java.io.File;
/*    */ import java.net.URLEncoder;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.integration.entrance.bean.OutterLoginBean;
/*    */ import weaver.integration.entrance.bean.OutterSysParamBean;
/*    */ import weaver.integration.entrance.service.OutterSysServer;
/*    */ import weaver.integration.entrance.utils.StringUtils;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class EASServer
/*    */   extends OutterSysServer
/*    */ {
/*    */   public Map<String, Object> getRedirectUrl(OutterLoginBean paramOutterLoginBean, String paramString, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 25 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 26 */     if (StringUtils.isBlank(paramString) && 
/* 27 */       !StringUtils.isBlank(paramOutterLoginBean.getServerUrl()) && paramOutterLoginBean.getServerUrl().indexOf("accountSetting") > -1) {
/* 28 */       hashMap.put("isRedirect", "true");
/* 29 */       hashMap.put("redirectUrl", paramOutterLoginBean.getServerUrl());
/* 30 */       hashMap.put("previewUrl", paramOutterLoginBean.getServerUrl());
/* 31 */       return (Map)hashMap;
/*    */     } 
/*    */ 
/*    */     
/* 35 */     String str1 = "";
/* 36 */     String str2 = paramHttpServletRequest.getSession().getServletContext().getRealPath("/");
/* 37 */     String str3 = str2 + "WEB-INF" + File.separator + "prop" + File.separator + "LtpaToken.properties";
/*    */     try {
/* 39 */       str1 = LtpaTokenManager.generate(paramOutterLoginBean.getAccount(), str3).toString();
/* 40 */     } catch (NullPointerException nullPointerException) {
/* 41 */       (new BaseBean()).writeLog("解析路径出错：" + str3 + " e:" + nullPointerException);
/*    */     } 
/*    */     
/* 44 */     String str4 = "";
/*    */ 
/*    */     
/* 47 */     String str5 = "<html><body>\n<form name=Loginform action='" + paramOutterLoginBean.getServerUrl() + "' method=post target='_self'>";
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 52 */     str5 = str5 + "<INPUT type='hidden' NAME='" + paramOutterLoginBean.getOutterSysBean().getBaseParam1() + "' VALUE='" + encodeStr(paramOutterLoginBean.getAccount(), paramOutterLoginBean.getOutterSysBean().getUrlEncodeFlag()) + "'>";
/* 53 */     str4 = str4 + ((str4.length() == 0) ? "" : "&") + paramOutterLoginBean.getOutterSysBean().getBaseParam1() + "=" + encodeStr(paramOutterLoginBean.getAccount(), paramOutterLoginBean.getOutterSysBean().getUrlEncodeFlag()) + "";
/*    */ 
/*    */     
/* 56 */     str5 = str5 + "<INPUT type='hidden' NAME='password' VALUE='" + str1 + "'>";
/*    */     
/* 58 */     str4 = str4 + ((str4.length() == 0) ? "" : "&") + "password=" + str1 + "";
/*    */     
/* 60 */     List list = paramOutterLoginBean.getOutterSysBean().getParamBeanList();
/*    */ 
/*    */     
/* 63 */     for (OutterSysParamBean outterSysParamBean : list) {
/* 64 */       String str6 = outterSysParamBean.getParamValue();
/* 65 */       String str7 = outterSysParamBean.getParamName();
/* 66 */       str5 = str5 + "<INPUT type='hidden' NAME='" + str7 + "' VALUE='" + str6 + "'>";
/* 67 */       if ("redirectTo".equalsIgnoreCase(str7) && (str6.contains("&") || str6.contains("?"))) {
/* 68 */         str6 = URLEncoder.encode(str6);
/*    */       }
/* 70 */       str4 = str4 + ((str4.length() == 0) ? "" : "&") + str7 + "=" + str6;
/*    */     } 
/*    */     
/* 73 */     if (paramOutterLoginBean.getOutterSysBean().getRequestType().equalsIgnoreCase("POST")) {
/* 74 */       str5 = str5 + "</form></body></html><script>Loginform.submit();</script>";
/* 75 */       hashMap.put("isRedirect", "true");
/* 76 */       hashMap.put("redirectUrl", str5);
/* 77 */       hashMap.put("previewUrl", paramOutterLoginBean.getServerUrl());
/* 78 */       return (Map)hashMap;
/* 79 */     }  if (paramOutterLoginBean.getOutterSysBean().getRequestType().equalsIgnoreCase("GET")) {
/* 80 */       String str = "";
/* 81 */       if (!StringUtils.isBlank(paramOutterLoginBean.getServerUrl()) && paramOutterLoginBean.getServerUrl().indexOf("?") > -1) {
/* 82 */         str = paramOutterLoginBean.getServerUrl() + "&" + str4;
/*    */       }
/* 84 */       else if (!"".equals(str4)) {
/* 85 */         str = paramOutterLoginBean.getServerUrl() + "?" + str4;
/*    */       } else {
/* 87 */         str = paramOutterLoginBean.getServerUrl();
/*    */       } 
/*    */       
/* 90 */       hashMap.put("isRedirect", "true");
/* 91 */       hashMap.put("redirectUrl", str.toString());
/* 92 */       hashMap.put("previewUrl", str.toString());
/*    */     } 
/* 94 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/service/impl/EASServer.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */