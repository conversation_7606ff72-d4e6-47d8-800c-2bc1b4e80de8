/*    */ package weaver.integration.entrance.service.impl;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.integration.entrance.bean.OutterLoginBean;
/*    */ import weaver.integration.entrance.service.OutterSysServer;
/*    */ import weaver.integration.entrance.utils.StringUtils;
/*    */ import weaver.interfaces.security.RSATool;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Email163Server
/*    */   extends OutterSysServer
/*    */ {
/*    */   public Map<String, Object> getRedirectUrl(OutterLoginBean paramOutterLoginBean, String paramString, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 20 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 21 */     if (StringUtils.isBlank(paramString) && 
/* 22 */       !StringUtils.isBlank(paramOutterLoginBean.getServerUrl()) && paramOutterLoginBean.getServerUrl().indexOf("accountSetting") > -1) {
/* 23 */       hashMap.put("isRedirect", "true");
/* 24 */       hashMap.put("redirectUrl", paramOutterLoginBean.getServerUrl());
/* 25 */       hashMap.put("previewUrl", paramOutterLoginBean.getServerUrl());
/* 26 */       return (Map)hashMap;
/*    */     } 
/*    */ 
/*    */ 
/*    */     
/* 31 */     long l = System.currentTimeMillis();
/*    */ 
/*    */ 
/*    */     
/* 35 */     RSATool rSATool = new RSATool();
/*    */     
/* 37 */     rSATool.setPrivateKey(paramOutterLoginBean.getOutterSysBean().getEmail163Key());
/*    */     
/* 39 */     String str1 = RSATool.generateSHA1withRSASigature(paramOutterLoginBean.getAccount() + paramOutterLoginBean.getOutterSysBean().getEmail163Domain() + l, rSATool
/* 40 */         .getPrivateKey());
/*    */ 
/*    */ 
/*    */     
/* 44 */     String str2 = paramOutterLoginBean.getOutterSysBean().getBaseParam1();
/*    */     
/* 46 */     String str3 = "";
/* 47 */     if (!StringUtils.isBlank(str2)) {
/* 48 */       str3 = "&" + str2 + "=" + paramOutterLoginBean.getAccount();
/*    */     }
/*    */     
/* 51 */     String str4 = "0";
/* 52 */     StringBuffer stringBuffer = new StringBuffer(paramOutterLoginBean.getServerUrl());
/* 53 */     stringBuffer.append("?domain=")
/* 54 */       .append(paramOutterLoginBean.getOutterSysBean().getEmail163Domain())
/*    */ 
/*    */       
/* 57 */       .append(str3)
/* 58 */       .append("&time=")
/* 59 */       .append(l)
/* 60 */       .append("&enc=")
/* 61 */       .append(str1)
/* 62 */       .append("&lang=")
/* 63 */       .append(str4);
/*    */ 
/*    */ 
/*    */     
/* 67 */     hashMap.put("isRedirect", "true");
/* 68 */     hashMap.put("redirectUrl", stringBuffer.toString());
/* 69 */     hashMap.put("previewUrl", stringBuffer.toString());
/* 70 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/service/impl/Email163Server.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */