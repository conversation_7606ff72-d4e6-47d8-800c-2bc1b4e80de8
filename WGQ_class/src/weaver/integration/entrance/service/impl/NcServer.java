/*     */ package weaver.integration.entrance.service.impl;
/*     */ 
/*     */ import java.net.HttpURLConnection;
/*     */ import java.net.MalformedURLException;
/*     */ import java.net.URL;
/*     */ import java.net.URLConnection;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.integration.entrance.bean.OutterLoginBean;
/*     */ import weaver.integration.entrance.service.OutterSysServer;
/*     */ import weaver.integration.entrance.utils.StringUtils;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class NcServer
/*     */   extends OutterSysServer
/*     */ {
/*     */   public Map<String, Object> getRedirectUrl(OutterLoginBean paramOutterLoginBean, String paramString, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  30 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  31 */     User user = getUser(paramHttpServletRequest, paramHttpServletResponse);
/*     */     
/*  33 */     if (StringUtils.isBlank(paramString) && 
/*  34 */       !StringUtils.isBlank(paramOutterLoginBean.getServerUrl()) && paramOutterLoginBean.getServerUrl().indexOf("accountSetting") > -1) {
/*  35 */       hashMap.put("isRedirect", "true");
/*  36 */       hashMap.put("redirectUrl", paramOutterLoginBean.getServerUrl());
/*  37 */       hashMap.put("previewUrl", paramOutterLoginBean.getServerUrl());
/*  38 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  41 */     String str1 = paramHttpServletRequest.getRequestedSessionId();
/*  42 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("tourl"));
/*     */     
/*  44 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
/*  45 */     String str3 = simpleDateFormat.format(new Date());
/*  46 */     String str4 = "";
/*     */ 
/*     */     
/*  49 */     for (Map.Entry entry : paramOutterLoginBean.getParamValues().entrySet()) {
/*  50 */       str4 = str4 + "&" + (String)entry.getKey() + "=" + (String)entry.getValue();
/*     */     }
/*     */ 
/*     */     
/*  54 */     String str5 = paramOutterLoginBean.getOutterSysBean().getBaseParam1();
/*  55 */     String str6 = paramOutterLoginBean.getOutterSysBean().getBaseParam2();
/*  56 */     String str7 = "";
/*  57 */     String str8 = "";
/*  58 */     if (!StringUtils.isBlank(str5)) {
/*  59 */       str7 = "&" + str5 + "=" + encodeStr(paramOutterLoginBean.getAccount(), paramOutterLoginBean.getOutterSysBean().getUrlEncodeFlag());
/*     */     }
/*  61 */     if (!StringUtils.isBlank(str6)) {
/*  62 */       str8 = "&" + str6 + "=" + encodeStr(paramOutterLoginBean.getPassword(), paramOutterLoginBean.getOutterSysBean().getUrlEncodeFlag());
/*     */     }
/*     */     
/*  65 */     URL uRL = null;
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/*  71 */       uRL = new URL(paramOutterLoginBean.getServerUrl() + "/service/RegisterServlet?key=" + str1 + "&accountcode=" + paramOutterLoginBean.getOutterSysBean().getNcAccountCode() + "&workdate=" + str3 + "&language=simpchn" + str7 + str8 + str4);
/*     */     
/*     */     }
/*  74 */     catch (MalformedURLException malformedURLException) {
/*  75 */       hashMap.put("isError", "true");
/*  76 */       hashMap.put("errorMsg", SystemEnv.getHtmlLabelName(508598, Util.getIntValue(user.getLanguage())));
/*  77 */       malformedURLException.printStackTrace();
/*  78 */       return (Map)hashMap;
/*     */     } 
/*     */     
/*  81 */     URLConnection uRLConnection = null;
/*  82 */     String str9 = null;
/*  83 */     int i = 0;
/*     */     try {
/*  85 */       uRLConnection = uRL.openConnection();
/*  86 */       uRLConnection.setConnectTimeout(24000);
/*  87 */       uRLConnection.setReadTimeout(24000);
/*  88 */       uRLConnection.setDoOutput(true);
/*     */       
/*  90 */       HttpURLConnection httpURLConnection = (HttpURLConnection)uRLConnection;
/*  91 */       str9 = httpURLConnection.getResponseMessage();
/*  92 */       i = httpURLConnection.getResponseCode();
/*  93 */     } catch (Exception exception) {
/*  94 */       hashMap.put("isError", "true");
/*  95 */       hashMap.put("errorMsg", SystemEnv.getHtmlLabelName(510042, user.getLanguage()) + uRL.toString());
/*  96 */       exception.printStackTrace();
/*  97 */       return (Map)hashMap;
/*     */     } 
/*  99 */     if (str9.equals("OK")) {
/* 100 */       hashMap.put("isRedirect", "true");
/* 101 */       hashMap.put("redirectUrl", paramOutterLoginBean.getServerUrl() + "/login.jsp?key=" + str1 + "&ncnode=" + str2);
/* 102 */       hashMap.put("previewUrl", paramOutterLoginBean.getServerUrl() + "/login.jsp?key=" + str1 + "&ncnode=" + str2);
/* 103 */       return (Map)hashMap;
/*     */     } 
/* 105 */     hashMap.put("isError", "true");
/* 106 */     if (i == 403) {
/* 107 */       hashMap.put("errorMsg", SystemEnv.getHtmlLabelName(510030, user.getLanguage()) + uRL.toString());
/* 108 */       return (Map)hashMap;
/*     */     } 
/* 110 */     hashMap.put("errorMsg", SystemEnv.getHtmlLabelName(510031, user.getLanguage()) + uRL.toString());
/* 111 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/service/impl/NcServer.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */