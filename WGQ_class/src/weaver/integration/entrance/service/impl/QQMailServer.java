/*    */ package weaver.integration.entrance.service.impl;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.integration.entrance.bean.OutterLoginBean;
/*    */ import weaver.integration.entrance.service.OutterSysServer;
/*    */ import weaver.integration.entrance.utils.StringUtils;
/*    */ import weaver.interfaces.email.EntranceQQEmail;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class QQMailServer
/*    */   extends OutterSysServer
/*    */ {
/*    */   public Map<String, Object> getRedirectUrl(OutterLoginBean paramOutterLoginBean, String paramString, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 24 */     RecordSet recordSet = new RecordSet();
/* 25 */     EntranceQQEmail entranceQQEmail = new EntranceQQEmail();
/*    */     
/* 27 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/* 29 */     User user = getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 30 */     String str1 = null;
/* 31 */     if (StringUtils.isBlank(paramString)) {
/* 32 */       recordSet.execute("select * from hrmresource where id = " + user.getUID());
/* 33 */       if (!recordSet.next()) {
/* 34 */         hashMap.put("isError", "true");
/* 35 */         hashMap.put("errorMsg", SystemEnv.getHtmlLabelName(508599, Util.getIntValue(user.getLanguage())));
/* 36 */         return (Map)hashMap;
/*    */       } 
/* 38 */       String str = Util.null2String(recordSet.getString("email"));
/* 39 */       if (!StringUtils.isBlank(str)) {
/* 40 */         str1 = entranceQQEmail.getSingleUrl(str, paramOutterLoginBean.getOutterSysBean().getSysId());
/* 41 */         hashMap.put("isRedirect", "true");
/* 42 */         hashMap.put("redirectUrl", str1);
/* 43 */         hashMap.put("previewUrl", str1);
/*    */       } else {
/* 45 */         hashMap.put("isError", "true");
/* 46 */         hashMap.put("errorMsg", SystemEnv.getHtmlLabelName(508600, Util.getIntValue(user.getLanguage())));
/*    */       } 
/* 48 */       return (Map)hashMap;
/*    */     } 
/*    */     
/* 51 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("email"));
/* 52 */     if (!StringUtils.isBlank(str2)) {
/* 53 */       str1 = entranceQQEmail.getSingleUrl(str2, paramOutterLoginBean.getOutterSysBean().getSysId());
/* 54 */       hashMap.put("isRedirect", "true");
/* 55 */       hashMap.put("redirectUrl", str1);
/* 56 */       hashMap.put("previewUrl", str1);
/*    */     } else {
/* 58 */       hashMap.put("isError", "true");
/* 59 */       hashMap.put("errorMsg", SystemEnv.getHtmlLabelName(508600, Util.getIntValue(user.getLanguage())));
/*    */     } 
/* 61 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/service/impl/QQMailServer.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */