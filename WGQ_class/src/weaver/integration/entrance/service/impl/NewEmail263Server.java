/*    */ package weaver.integration.entrance.service.impl;
/*    */ 
/*    */ import java.util.Date;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.MD5;
/*    */ import weaver.general.Util;
/*    */ import weaver.integration.entrance.bean.OutterLoginBean;
/*    */ import weaver.integration.entrance.bean.OutterSysBean;
/*    */ import weaver.integration.entrance.service.OutterSysServer;
/*    */ import weaver.integration.entrance.utils.StringUtils;
/*    */ 
/*    */ 
/*    */ public class NewEmail263Server
/*    */   extends OutterSysServer
/*    */ {
/*    */   public Map<String, Object> getRedirectUrl(OutterLoginBean paramOutterLoginBean, String paramString, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 21 */     this.log.setClassname(NewEmail263Server.class.getName());
/* 22 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 23 */     String str1 = "";
/* 24 */     if (!StringUtils.isBlank(paramString)) {
/* 25 */       if (!StringUtils.isBlank(paramOutterLoginBean.getServerUrl())) {
/* 26 */         str1 = Util.null2String(paramHttpServletRequest.getParameter("email"));
/*    */       } else {
/* 28 */         this.log.info("263邮箱单点登录地址为空");
/*    */       } 
/*    */     } else {
/*    */       
/* 32 */       str1 = "";
/*    */       try {
/* 34 */         String str = "select email from HrmResource where loginid= ?";
/* 35 */         RecordSet recordSet = new RecordSet();
/* 36 */         recordSet.executeQuery(str, new Object[] { paramOutterLoginBean.getAccount() });
/* 37 */         if (recordSet.next()) {
/* 38 */           str1 = Util.null2String(recordSet.getString("email"));
/*    */         } else {
/* 40 */           this.log.error("263邮箱v2根据登录账号获取email属性为空");
/*    */         } 
/* 42 */       } catch (Exception exception) {
/* 43 */         this.log.error("263邮箱v2根据登录账号获取email属性异常：" + exception);
/*    */       } 
/*    */     } 
/* 46 */     OutterSysBean outterSysBean = paramOutterLoginBean.getOutterSysBean();
/* 47 */     String str2 = Long.toString((new Date()).getTime() + 3600000L);
/*    */     
/* 49 */     String str3 = outterSysBean.getEmail263Key();
/*    */     
/* 51 */     String str4 = outterSysBean.getEmail263PartnerId();
/*    */     
/* 53 */     String str5 = outterSysBean.getEmail263AuthCorpId();
/*    */     
/* 55 */     String str6 = outterSysBean.getBaseParam1();
/*    */     
/* 57 */     String str7 = "windows";
/*    */     
/* 59 */     String str8 = "READMAIL";
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 64 */     String str9 = "";
/* 65 */     String str10 = "";
/*    */     
/* 67 */     String str11 = str3 + str7 + str8 + str4 + str5 + str1 + str2;
/*    */     
/* 69 */     MD5 mD5 = new MD5();
/* 70 */     str9 = mD5.getMD5ofStr(str11).toLowerCase();
/*    */     
/* 72 */     str10 = str10 + ((str10.length() == 0) ? "" : "&") + "timestamp=" + str2;
/* 73 */     str10 = str10 + ((str10.length() == 0) ? "" : "&") + "loginPlatform=" + str7;
/* 74 */     str10 = str10 + ((str10.length() == 0) ? "" : "&") + "type=" + str8;
/* 75 */     str10 = str10 + ((str10.length() == 0) ? "" : "&") + "partnerid=" + str4;
/* 76 */     str10 = str10 + ((str10.length() == 0) ? "" : "&") + "authcorpid=" + str5;
/* 77 */     str10 = str10 + ((str10.length() == 0) ? "" : "&") + "userid=" + str1;
/* 78 */     str10 = str10 + ((str10.length() == 0) ? "" : "&") + "sign=" + str9;
/*    */     
/* 80 */     String str12 = "";
/* 81 */     if (!StringUtils.isBlank(paramOutterLoginBean.getServerUrl()) && paramOutterLoginBean.getServerUrl().indexOf("?") > -1) {
/* 82 */       str12 = paramOutterLoginBean.getServerUrl() + "&" + str10;
/*    */     } else {
/* 84 */       str12 = paramOutterLoginBean.getServerUrl() + "?" + str10;
/*    */     } 
/*    */     
/* 87 */     hashMap.put("isRedirect", "false");
/* 88 */     hashMap.put("redirectUrl", "<script language='javascript'>window.location.href='" + str12 + "'</script>");
/* 89 */     hashMap.put("previewUrl", str12);
/*    */     
/* 91 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/service/impl/NewEmail263Server.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */