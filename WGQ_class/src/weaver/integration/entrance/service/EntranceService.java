/*    */ package weaver.integration.entrance.service;
/*    */ 
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.integration.entrance.bean.OutterLoginBean;
/*    */ import weaver.integration.entrance.bean.OutterSysBean;
/*    */ import weaver.integration.entrance.utils.StringUtils;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class EntranceService
/*    */   implements IEntranceService
/*    */ {
/*    */   public OutterLoginBean getLoginBean(IOutterSysServer paramIOutterSysServer, String paramString1, String paramString2, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ClassNotFoundException, NoSuchMethodException, IllegalAccessException, InstantiationException {
/* 22 */     if (StringUtils.isBlank(paramString1)) {
/* 23 */       throw new IllegalAccessException("sysid can not be null");
/*    */     }
/*    */     
/* 26 */     OutterSysBean outterSysBean = paramIOutterSysServer.getOutterSysBeanFromDB(paramString1, paramString2, paramHttpServletRequest, paramHttpServletResponse);
/*    */     
/*    */     try {
/* 29 */       paramIOutterSysServer.setOutterEncryptBeanFromDB(outterSysBean);
/* 30 */     } catch (Exception exception) {
/* 31 */       exception.printStackTrace();
/* 32 */       logger.error("===========设置加密类出错。。。");
/*    */     } 
/*    */     
/*    */     try {
/* 36 */       paramIOutterSysServer.setEncryptMethod(outterSysBean);
/* 37 */     } catch (Exception exception) {
/* 38 */       exception.printStackTrace();
/* 39 */       logger.error("===========设置加密方法出错。。。");
/*    */     } 
/*    */     
/* 42 */     paramIOutterSysServer.getLoginBean(outterSysBean, paramString2, paramHttpServletRequest, paramHttpServletResponse);
/*    */     
/* 44 */     paramIOutterSysServer.setOutterSysParamBeanFromDB(outterSysBean, paramString2, paramHttpServletRequest, paramHttpServletResponse);
/*    */     
/* 46 */     return paramIOutterSysServer.getLoginBean(outterSysBean, paramString2, paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> getUrl(IOutterSysServer paramIOutterSysServer, OutterLoginBean paramOutterLoginBean, String paramString, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 51 */     return paramIOutterSysServer.getRedirectUrl(paramOutterLoginBean, paramString, paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */   
/* 54 */   private static Logger logger = LoggerFactory.getLogger(EntranceService.class);
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/service/EntranceService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */