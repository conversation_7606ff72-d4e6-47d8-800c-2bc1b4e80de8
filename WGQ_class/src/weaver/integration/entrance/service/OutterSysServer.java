/*     */ package weaver.integration.entrance.service;
/*     */ import java.lang.reflect.Field;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Enumeration;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.SecurityHelper;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.HrmUserVarify;
/*     */ import weaver.hrm.User;
/*     */ import weaver.integration.entrance.bean.OutterEncryptClassBean;
/*     */ import weaver.integration.entrance.bean.OutterLoginBean;
/*     */ import weaver.integration.entrance.bean.OutterSysBean;
/*     */ import weaver.integration.entrance.bean.OutterSysParamBean;
/*     */ import weaver.integration.entrance.constants.OutterConstant;
/*     */ import weaver.integration.entrance.exceptions.OtherParamsEncryptException;
/*     */ import weaver.integration.entrance.service.encrypt.EncryptService;
/*     */ import weaver.integration.entrance.service.impl.ALYMailServer;
/*     */ import weaver.integration.entrance.service.impl.CoreMailServer;
/*     */ import weaver.integration.entrance.service.impl.EASServer;
/*     */ import weaver.integration.entrance.service.impl.Email163Server;
/*     */ import weaver.integration.entrance.service.impl.Email263Server;
/*     */ import weaver.integration.entrance.service.impl.K3CloudServer;
/*     */ import weaver.integration.entrance.service.impl.K3CloudServerV2;
/*     */ import weaver.integration.entrance.service.impl.Nc6Server;
/*     */ import weaver.integration.entrance.service.impl.NcServer;
/*     */ import weaver.integration.entrance.service.impl.NewEmail263Server;
/*     */ import weaver.integration.entrance.service.impl.QQMailServer;
/*     */ import weaver.integration.entrance.utils.StringUtils;
/*     */ import weaver.integration.util.ConstantsUtil;
/*     */ import weaver.interfaces.outter.CheckIpNetWork;
/*     */ import weaver.interfaces.outter.OutterUtil;
/*     */ import weaver.rsa.security.RSA;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class OutterSysServer implements IOutterSysServer {
/*  42 */   protected Logger log = LoggerFactory.getLogger();
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getRedirectUrl(OutterLoginBean paramOutterLoginBean, String paramString, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  47 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */ 
/*     */     
/*  50 */     if ("6".equals(paramOutterLoginBean.getOutterSysBean().getTypeName())) {
/*  51 */       QQMailServer qQMailServer = new QQMailServer();
/*  52 */       return qQMailServer.getRedirectUrl(paramOutterLoginBean, paramString, paramHttpServletRequest, paramHttpServletResponse);
/*     */     } 
/*     */     
/*  55 */     if (StringUtils.isBlank(paramString)) {
/*  56 */       if (!StringUtils.isBlank(paramOutterLoginBean.getServerUrl()) && paramOutterLoginBean.getServerUrl().indexOf("accountSetting") > -1) {
/*  57 */         String str = ConstantsUtil.INTEGRATION_OUTTER_ACCOUNTSET1 + paramOutterLoginBean.getOutterSysBean().getSysId();
/*  58 */         hashMap.put("isRedirect", "true");
/*  59 */         hashMap.put("redirectUrl", str);
/*  60 */         hashMap.put("previewUrl", str);
/*  61 */         return (Map)hashMap;
/*     */       } 
/*  63 */       if (paramOutterLoginBean.getOutterSysBean().getEncodeFlag().equals("1")) {
/*  64 */         hashMap.put("isRedirect", "true");
/*  65 */         hashMap.put("redirectUrl", "/interface/Entrance_gbk.jsp?id=" + paramOutterLoginBean.getOutterSysBean().getSysId());
/*  66 */         hashMap.put("previewUrl", "/interface/Entrance_gbk.jsp?id=" + paramOutterLoginBean.getOutterSysBean().getSysId());
/*  67 */         return (Map)hashMap;
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/*  72 */     if (!"0".equals(paramOutterLoginBean.getOutterSysBean().getTypeName())) {
/*  73 */       return goRedirectUrl(paramOutterLoginBean, paramString, paramHttpServletRequest, paramHttpServletResponse);
/*     */     }
/*     */     
/*  76 */     if (!ConstantsUtil.INTEGRATION_OUTTER_LOGIN.equals(paramOutterLoginBean.getOutterSysBean().getEntranceUrl())) {
/*  77 */       hashMap.put("isRedirect", "true");
/*  78 */       hashMap.put("redirectUrl", paramOutterLoginBean.getOutterSysBean().getEntranceUrl() + "?id=" + paramOutterLoginBean.getOutterSysBean().getSysId());
/*  79 */       hashMap.put("previewUrl", paramOutterLoginBean.getOutterSysBean().getEntranceUrl() + "?id=" + paramOutterLoginBean.getOutterSysBean().getSysId());
/*  80 */       return (Map)hashMap;
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/*  85 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("gopage"));
/*  86 */     String str2 = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  96 */     if (!StringUtils.isBlank(paramOutterLoginBean.getServerUrl()) && paramOutterLoginBean.getServerUrl().indexOf("ftp://") > -1) {
/*  97 */       String str = "ftp://" + paramOutterLoginBean.getAccount() + ":" + paramOutterLoginBean.getPassword() + "@" + paramOutterLoginBean.getServerUrl().substring(6);
/*  98 */       hashMap.put("isRedirect", "true");
/*  99 */       hashMap.put("redirectUrl", str);
/* 100 */       hashMap.put("previewUrl", str);
/* 101 */       return (Map)hashMap;
/*     */     } 
/*     */     
/* 104 */     String str3 = "<html><body>\n<form name=Loginform action='" + paramOutterLoginBean.getServerUrl() + "' method=" + paramOutterLoginBean.getOutterSysBean().getRequestType() + " target='_self'><INPUT type='hidden' NAME='gopage' VALUE='" + str1 + "'>";
/*     */     
/* 106 */     List list = paramOutterLoginBean.getOutterSysBean().getParamBeanList();
/*     */ 
/*     */     
/* 109 */     for (OutterSysParamBean outterSysParamBean : list) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 119 */       str3 = str3 + "<INPUT type='hidden' NAME='" + outterSysParamBean.getParamName() + "' VALUE='" + outterSysParamBean.getParamValue() + "'>";
/* 120 */       str2 = str2 + ((str2.length() == 0) ? "" : "&") + outterSysParamBean.getParamName() + "=" + encodeStr(outterSysParamBean.getParamValue(), paramOutterLoginBean
/* 121 */           .getOutterSysBean().getUrlEncodeFlag());
/*     */     } 
/*     */     
/* 124 */     if (!StringUtils.isBlank(paramOutterLoginBean.getOutterSysBean().getBaseParam1())) {
/* 125 */       str3 = str3 + "<INPUT type='hidden' NAME='" + paramOutterLoginBean.getOutterSysBean().getBaseParam1() + "' VALUE='" + paramOutterLoginBean.getAccount() + "'>";
/* 126 */       str2 = str2 + ((str2.length() == 0) ? "" : "&") + paramOutterLoginBean.getOutterSysBean().getBaseParam1() + "=" + encodeStr(paramOutterLoginBean.getAccount(), paramOutterLoginBean
/* 127 */           .getOutterSysBean().getUrlEncodeFlag()) + "";
/*     */     } 
/* 129 */     if (!StringUtils.isBlank(paramOutterLoginBean.getOutterSysBean().getBaseParam2())) {
/* 130 */       str3 = str3 + "<INPUT type='hidden' NAME='" + paramOutterLoginBean.getOutterSysBean().getBaseParam2() + "' VALUE='" + paramOutterLoginBean.getPassword() + "'>";
/* 131 */       str2 = str2 + ((str2.length() == 0) ? "" : "&") + paramOutterLoginBean.getOutterSysBean().getBaseParam2() + "=" + encodeStr(paramOutterLoginBean.getPassword(), paramOutterLoginBean
/* 132 */           .getOutterSysBean().getUrlEncodeFlag()) + "";
/*     */     } 
/*     */     
/* 135 */     if (StringUtils.isBlank(paramString)) {
/* 136 */       Enumeration<String> enumeration = paramHttpServletRequest.getParameterNames();
/* 137 */       if (null != enumeration) {
/* 138 */         while (enumeration.hasMoreElements()) {
/* 139 */           String str = Util.null2String(enumeration.nextElement());
/* 140 */           if (!"id".equals(str) && !"gopage".equals(str)) {
/* 141 */             String str4 = Util.null2String(paramHttpServletRequest.getParameter(str));
/* 142 */             str3 = str3 + "<INPUT type='hidden' NAME='" + str + "' VALUE='" + str4 + "'>";
/*     */           } 
/*     */         } 
/*     */       }
/*     */     } 
/*     */     
/* 148 */     if (paramOutterLoginBean.getOutterSysBean().getRequestType().equalsIgnoreCase("POST")) {
/* 149 */       str3 = str3 + "</form></body></html><script>Loginform.submit();</script>";
/* 150 */       hashMap.put("isRedirect", "false");
/* 151 */       hashMap.put("redirectUrl", str3);
/* 152 */       hashMap.put("previewUrl", paramOutterLoginBean.getServerUrl());
/* 153 */       return (Map)hashMap;
/* 154 */     }  if (paramOutterLoginBean.getOutterSysBean().getRequestType().equalsIgnoreCase("GET")) {
/* 155 */       String str = "";
/* 156 */       if (!StringUtils.isBlank(paramOutterLoginBean.getServerUrl()) && paramOutterLoginBean.getServerUrl().indexOf("?") > -1) {
/* 157 */         str = paramOutterLoginBean.getServerUrl() + "&" + str2;
/*     */       }
/* 159 */       else if (!"".equals(str2)) {
/* 160 */         str = paramOutterLoginBean.getServerUrl() + "?" + str2;
/*     */       } else {
/* 162 */         str = paramOutterLoginBean.getServerUrl();
/*     */       } 
/*     */       
/* 165 */       hashMap.put("isRedirect", "false");
/* 166 */       hashMap.put("redirectUrl", "<script language='javascript'>window.location.href='" + str + "'</script>");
/* 167 */       hashMap.put("previewUrl", str);
/* 168 */       return (Map)hashMap;
/*     */     } 
/* 170 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public OutterSysBean getOutterSysBeanFromDB(String paramString1, String paramString2, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws IllegalAccessException {
/* 176 */     OutterSysBean outterSysBean = null;
/*     */     
/* 178 */     RecordSet recordSet = new RecordSet();
/* 179 */     recordSet.execute("select * from outter_sys where sysid = '" + paramString1 + "' ");
/*     */     
/* 181 */     if (recordSet.next()) {
/* 182 */       outterSysBean = new OutterSysBean();
/* 183 */       outterSysBean.setRequestType(Util.null2String(recordSet.getString("requesttype")));
/* 184 */       outterSysBean.setSysId(Util.null2String(recordSet.getString("sysid")));
/* 185 */       if (StringUtils.isBlank(outterSysBean.getRequestType())) {
/* 186 */         outterSysBean.setRequestType(RequestMethod.POST.toString());
/*     */       }
/* 188 */       outterSysBean.setEncryptType(Util.null2String(recordSet.getString("encrypttype")));
/*     */       
/* 190 */       outterSysBean.setBaseParam1(Util.null2String(recordSet.getString("baseparam1")));
/* 191 */       outterSysBean.setUrlParaEncrypt1(Util.null2String(recordSet.getString("urlparaencrypt1")));
/* 192 */       outterSysBean.setEncryptCode1(Util.null2String(recordSet.getString("encryptcode1")));
/* 193 */       outterSysBean.setEncryptIv1(Util.null2String(recordSet.getString("encryptiv1")));
/*     */       
/* 195 */       outterSysBean.setBaseParam2(Util.null2String(recordSet.getString("baseparam2")));
/* 196 */       outterSysBean.setUrlParaEncrypt2(Util.null2String(recordSet.getString("urlparaencrypt2")));
/* 197 */       outterSysBean.setEncryptCode2(Util.null2String(recordSet.getString("encryptcode2")));
/* 198 */       outterSysBean.setEncryptIv2(Util.null2String(recordSet.getString("encryptiv2")));
/*     */       
/* 200 */       outterSysBean.setIurl(Util.null2String(recordSet.getString("iurl")));
/* 201 */       outterSysBean.setOurl(Util.null2String(recordSet.getString("ourl")));
/* 202 */       outterSysBean.setTypeName(Util.null2String(recordSet.getString("typename")));
/* 203 */       outterSysBean.setUrlEncodeFlag(Util.null2String(recordSet.getString("urlencodeflag")));
/* 204 */       outterSysBean.setEntranceUrl(Util.null2String(recordSet.getString("entranceurl")));
/* 205 */       outterSysBean.setResult(Util.null2String(recordSet.getString("result")));
/* 206 */       outterSysBean.setEncodeFlag(Util.null2String(recordSet.getString("encodeflag")));
/* 207 */       outterSysBean.setBaseType1(Integer.valueOf(Util.getIntValue(recordSet.getString("basetype1"), 0)));
/* 208 */       outterSysBean.setBaseType2(Integer.valueOf(Util.getIntValue(recordSet.getString("basetype2"), 0)));
/* 209 */       outterSysBean.setAutoLogin(Util.null2String(recordSet.getString("autologin")));
/*     */ 
/*     */       
/* 212 */       outterSysBean.setEmail163Domain(Util.null2String(recordSet.getString("email163_domain")));
/* 213 */       outterSysBean.setEmail163Key(Util.null2String(recordSet.getString("email163_key")));
/*     */       
/* 215 */       outterSysBean.setEmail263Cid(Util.null2String(recordSet.getString("email263_cid")));
/* 216 */       outterSysBean.setEmail263Domain(Util.null2String(recordSet.getString("email263_domain")));
/* 217 */       outterSysBean.setEmail263Key(Util.null2String(recordSet.getString("email263_key")));
/*     */       
/* 219 */       outterSysBean.setEmail263AuthCorpId(Util.null2String(recordSet.getString("email263_authcorpid")));
/* 220 */       outterSysBean.setEmail263PartnerId(Util.null2String(recordSet.getString("email263_partnerid")));
/*     */ 
/*     */       
/* 223 */       outterSysBean.setNcAccountCode(Util.null2String(recordSet.getString("ncaccountcode")));
/* 224 */       outterSysBean.setCoremailType(Util.null2String(recordSet.getString("coremailtype")));
/*     */ 
/*     */       
/* 227 */       outterSysBean.setSysMap(getMap(recordSet, outterSysBean));
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 233 */     recordSet.execute("select * from outter_sysparam where sysid ='" + paramString1 + "'");
/* 234 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 236 */     while (recordSet.next()) {
/* 237 */       String str = recordSet.getString("paramname");
/* 238 */       if (str.equals("k3server")) {
/* 239 */         outterSysBean.setK3server(recordSet.getString("paramvalue")); continue;
/* 240 */       }  if (str.equals("dbid")) {
/* 241 */         outterSysBean.setDbid(recordSet.getString("paramvalue")); continue;
/* 242 */       }  if (str.equals("appid")) {
/* 243 */         outterSysBean.setAppid(recordSet.getString("paramvalue")); continue;
/* 244 */       }  if (str.equals("lcid")) {
/* 245 */         outterSysBean.setLcid(recordSet.getString("paramvalue")); continue;
/* 246 */       }  if (str.equals("appsecret")) {
/* 247 */         outterSysBean.setAppsecret(recordSet.getString("paramvalue")); continue;
/*     */       } 
/* 249 */       hashMap.put(str, Util.null2String(recordSet.getString("paramvalue")));
/*     */     } 
/*     */ 
/*     */     
/* 253 */     if (outterSysBean == null) {
/* 254 */       throw new IllegalAccessException("outterSysBean is null");
/*     */     }
/* 256 */     outterSysBean.setOtherParams(hashMap);
/* 257 */     outterSysBean.setTimestamp(Long.toString(System.currentTimeMillis() / 1000L));
/* 258 */     return outterSysBean;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setOutterEncryptBeanFromDB(OutterSysBean paramOutterSysBean) throws IllegalAccessException {
/* 263 */     RecordSet recordSet = new RecordSet();
/* 264 */     OutterEncryptClassBean outterEncryptClassBean = null;
/* 265 */     if ("2".equals(paramOutterSysBean.getEncryptType())) {
/* 266 */       recordSet.executeSql("select t2.encryptclass as encryptclass1,t2.encryptmethod as encryptmethod1,t2.isneedpwd,t2.password,t2.isneediv,t2.ivparam from outter_sys t1 LEFT JOIN outter_encryptclass t2 on t1.encryptclassId = t2.id where sysid = '" + paramOutterSysBean
/* 267 */           .getSysId() + "' ");
/*     */     } else {
/* 269 */       recordSet.executeSql("select t2.encryptclass as encryptclass1,t2.encryptmethod as encryptmethod1,t2.isneedpwd,t2.password,t2.isneediv,t2.ivparam from outter_sys t1 LEFT JOIN outter_encryptclass_sys t2 on t1.encrypttype = t2.id where sysid = '" + paramOutterSysBean
/* 270 */           .getSysId() + "' ");
/*     */     } 
/*     */     
/* 273 */     if (recordSet.next()) {
/* 274 */       outterEncryptClassBean = new OutterEncryptClassBean();
/* 275 */       outterEncryptClassBean.setIsNeedPwd(Util.null2String(recordSet.getString("isneedpwd")));
/* 276 */       outterEncryptClassBean.setIsNeedIv(Util.null2String(recordSet.getString("isneediv")));
/* 277 */       if (!StringUtils.isBlank(Util.null2String(recordSet.getString("password")))) {
/* 278 */         outterEncryptClassBean.setPassword(SecurityHelper.decryptSimple(Util.null2String(recordSet.getString("password"))));
/*     */       }
/* 280 */       if (!StringUtils.isBlank(Util.null2String(recordSet.getString("ivparam")))) {
/* 281 */         outterEncryptClassBean.setIvParam(SecurityHelper.decryptSimple(Util.null2String(recordSet.getString("ivparam"))));
/*     */       }
/*     */       
/* 284 */       outterEncryptClassBean.setEncryptMap(getMap(recordSet, outterEncryptClassBean));
/*     */       
/* 286 */       paramOutterSysBean.setEncryptClass(recordSet.getString("encryptclass1"));
/* 287 */       paramOutterSysBean.setEncryptMethod(recordSet.getString("encryptmethod1"));
/*     */       
/* 289 */       outterEncryptClassBean.setEncryptClass(recordSet.getString("encryptclass1"));
/* 290 */       outterEncryptClassBean.setEncryptMethod(recordSet.getString("encryptmethod1"));
/*     */     } 
/*     */     
/* 293 */     if (outterEncryptClassBean == null) {
/* 294 */       throw new IllegalAccessException("outterEncryptClassBean is null");
/*     */     }
/* 296 */     paramOutterSysBean.setOutterEncryptClassBean(outterEncryptClassBean);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setOutterSysParamBeanFromDB(OutterSysBean paramOutterSysBean, String paramString, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 309 */     RecordSet recordSet1 = new RecordSet();
/* 310 */     RSA rSA = new RSA();
/* 311 */     ArrayList<OutterSysParamBean> arrayList = new ArrayList();
/* 312 */     recordSet1.executeSql("select * from outter_sysparam where sysid='" + paramOutterSysBean.getSysId() + "' order by indexid");
/* 313 */     User user = getUser(paramHttpServletRequest, paramHttpServletResponse);
/*     */     
/* 315 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 316 */     OutterUtil outterUtil = new OutterUtil();
/* 317 */     Map<String, String> map = outterUtil.expressionValueMap(paramOutterSysBean.getSysId(), getUser(paramHttpServletRequest, paramHttpServletResponse), rSA.decrypt(null, user.getPwd(), true));
/* 318 */     RecordSet recordSet2 = new RecordSet();
/* 319 */     String str1 = "";
/* 320 */     String str2 = "";
/* 321 */     recordSet2.executeSql("select * from outter_sys where sysid = '" + paramOutterSysBean.getSysId() + "' ");
/* 322 */     if (recordSet2.next()) {
/* 323 */       str1 = Util.null2String(recordSet2.getString("baseparam1"));
/* 324 */       str2 = Util.null2String(recordSet2.getString("baseparam2"));
/*     */     } 
/* 326 */     if (StringUtils.isBlank(paramString)) {
/* 327 */       OutterSysServer outterSysServer = new OutterSysServer();
/*     */       
/* 329 */       OutterLoginBean outterLoginBean = outterSysServer.getLoginBeanNew(paramOutterSysBean, "", paramHttpServletRequest, paramHttpServletResponse);
/* 330 */       if (!"".equals(str1)) {
/* 331 */         map.put(str1, outterLoginBean.getAccount());
/*     */       }
/* 333 */       if (!"".equals(str2)) {
/* 334 */         map.put(str2, outterLoginBean.getPassword());
/*     */       }
/*     */     } else {
/* 337 */       if (!"".equals(str1)) {
/* 338 */         map.put(str1, paramHttpServletRequest.getParameter(str1));
/*     */       }
/* 340 */       if (!"".equals(str2)) {
/* 341 */         map.put(str2, paramHttpServletRequest.getParameter(str2));
/*     */       }
/*     */     } 
/* 344 */     while (recordSet1.next()) {
/* 345 */       OutterSysParamBean outterSysParamBean = new OutterSysParamBean();
/* 346 */       outterSysParamBean.setParamType(Integer.valueOf(Util.getIntValue(recordSet1.getString("paramtype"), 0)));
/* 347 */       outterSysParamBean.setParamName(recordSet1.getString("paramname"));
/* 348 */       outterSysParamBean.setParamValue(recordSet1.getString("paramvalue"));
/* 349 */       outterSysParamBean.setParaEncrypt(recordSet1.getString("paraencrypt"));
/* 350 */       outterSysParamBean.setEncryptCode(recordSet1.getString("encryptcode"));
/* 351 */       outterSysParamBean.setEncryptIv(recordSet1.getString("encryptiv"));
/*     */ 
/*     */       
/* 354 */       outterSysParamBean.setParamMap(getMap(recordSet1, outterSysParamBean));
/*     */       
/* 356 */       if (StringUtils.isBlank(paramString)) {
/*     */ 
/*     */ 
/*     */         
/* 360 */         if (outterSysParamBean.getParamType().intValue() != 0)
/*     */         {
/* 362 */           if (outterSysParamBean.getParamType().intValue() == 1) {
/* 363 */             RecordSet recordSet = new RecordSet();
/* 364 */             recordSet.executeSql("select * from outter_params where sysid='" + paramOutterSysBean
/* 365 */                 .getSysId() + "' and userid=" + user.getUID() + " and paramname='" + outterSysParamBean
/* 366 */                 .getParamName() + "'");
/* 367 */             if (recordSet.next()) {
/* 368 */               outterSysParamBean.setParamValue(recordSet.getString("paramvalue"));
/*     */             }
/* 370 */           } else if (outterSysParamBean.getParamType().intValue() == 2) {
/* 371 */             outterSysParamBean.setParamValue(user.getUserSubCompany1() + "");
/* 372 */           } else if (outterSysParamBean.getParamType().intValue() == 3) {
/* 373 */             outterSysParamBean.setParamValue(user.getUserDepartment() + "");
/* 374 */           } else if (outterSysParamBean.getParamType().intValue() == 4) {
/* 375 */             OutterUtil outterUtil1 = new OutterUtil();
/* 376 */             outterSysParamBean.setParamValue(outterUtil1.getExpressionValue(outterSysParamBean.getParamValue(), paramOutterSysBean.getSysId(), user, rSA
/* 377 */                   .decrypt(null, user.getPwd(), true), map));
/* 378 */           } else if (outterSysParamBean.getParamType().intValue() == 5) {
/*     */             continue;
/*     */           } 
/*     */         }
/*     */       } else {
/* 383 */         Enumeration<String> enumeration = paramHttpServletRequest.getParameterNames();
/* 384 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 385 */         String str3 = "";
/* 386 */         String str4 = "";
/* 387 */         while (enumeration.hasMoreElements()) {
/* 388 */           String str = Util.null2String(enumeration.nextElement());
/* 389 */           RecordSet recordSet = new RecordSet();
/*     */           
/* 391 */           recordSet.executeSql("select * from outter_sysparam where paramtype = 2 and  sysid = '" + paramOutterSysBean.getSysId() + "' ");
/*     */           
/* 393 */           if (recordSet.next()) {
/* 394 */             String str5 = Util.null2String(recordSet1.getString("paramname"));
/* 395 */             if (str5.equals(str)) {
/* 396 */               str3 = Util.null2String(paramHttpServletRequest.getParameter(str));
/*     */             }
/*     */           } 
/*     */           
/* 400 */           recordSet.executeSql("select * from outter_sysparam where paramtype = 3 and  sysid = '" + paramOutterSysBean.getSysId() + "' ");
/* 401 */           if (recordSet.next()) {
/* 402 */             String str5 = Util.null2String(recordSet1.getString("paramname"));
/* 403 */             if (str5.equals(str)) {
/* 404 */               str4 = Util.null2String(paramHttpServletRequest.getParameter(str));
/*     */             }
/*     */           } 
/* 407 */           if (!"logintype_sysparam".equals(str) && !"id".equals(str) && 
/* 408 */             !"operationType".equals(str) && 
/* 409 */             !"otherPageUrlString".equals(str)) {
/* 410 */             String str5 = Util.null2String(paramHttpServletRequest.getParameter(str));
/* 411 */             hashMap1.put(str, str5);
/*     */           } 
/*     */         } 
/* 414 */         if (outterSysParamBean.getParamType().intValue() != 0) {
/* 415 */           if (outterSysParamBean.getParamType().intValue() == 1) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 422 */             outterSysParamBean.setParamValue(Util.null2String(paramHttpServletRequest.getParameter(outterSysParamBean.getParamName())));
/* 423 */           } else if (outterSysParamBean.getParamType().intValue() == 2) {
/* 424 */             outterSysParamBean.setParamValue(str3);
/* 425 */           } else if (outterSysParamBean.getParamType().intValue() == 3) {
/* 426 */             outterSysParamBean.setParamValue(str4);
/* 427 */           } else if (outterSysParamBean.getParamType().intValue() == 4) {
/* 428 */             OutterUtil outterUtil1 = new OutterUtil();
/* 429 */             outterSysParamBean.setParamValue(outterUtil1.getExpressionValue(outterSysParamBean.getParamValue(), paramOutterSysBean.getSysId(), user, rSA
/* 430 */                   .decrypt(null, user.getPwd(), true), map));
/* 431 */           } else if (outterSysParamBean.getParamType().intValue() == 5) {
/*     */             continue;
/*     */           } 
/*     */         }
/*     */       } 
/* 436 */       arrayList.add(outterSysParamBean);
/*     */     } 
/* 438 */     paramOutterSysBean.setParamBeanList(arrayList);
/*     */   }
/*     */ 
/*     */   
/*     */   public User getUser(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 443 */     return HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setEncryptMethod(OutterSysBean paramOutterSysBean) throws ClassNotFoundException, NoSuchMethodException, IllegalAccessException, InstantiationException {
/* 449 */     if (paramOutterSysBean.getOutterEncryptClassBean() != null && 
/* 450 */       !StringUtils.isBlank(paramOutterSysBean.getOutterEncryptClassBean().getEncryptClass()) && 
/* 451 */       !StringUtils.isBlank(paramOutterSysBean.getOutterEncryptClassBean().getEncryptMethod())) {
/* 452 */       Class<?> clazz = Class.forName(paramOutterSysBean.getOutterEncryptClassBean().getEncryptClass());
/* 453 */       paramOutterSysBean.setObject(clazz.newInstance());
/* 454 */       Class[] arrayOfClass = new Class[1];
/* 455 */       arrayOfClass[0] = String.class;
/* 456 */       paramOutterSysBean.setMethodEncode(clazz.getMethod(paramOutterSysBean.getEncryptMethod(), arrayOfClass));
/* 457 */       paramOutterSysBean.setMethodSetpwd(clazz.getMethod("setPwd", arrayOfClass));
/* 458 */       paramOutterSysBean.setMethodSetiv(clazz.getMethod("setIv", arrayOfClass));
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public OutterLoginBean getLoginBean(OutterSysBean paramOutterSysBean, String paramString, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 465 */     User user = getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 466 */     RSA rSA = new RSA();
/*     */     
/* 468 */     OutterLoginBean outterLoginBean = new OutterLoginBean();
/* 469 */     outterLoginBean.setOutterSysBean(paramOutterSysBean);
/* 470 */     outterLoginBean.setSysId(paramOutterSysBean.getSysId());
/*     */     
/* 472 */     RecordSet recordSet = new RecordSet();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 482 */     if (StringUtils.isBlank(paramString)) {
/*     */ 
/*     */       
/* 485 */       String str1 = TimeUtil.getCurrentDateString();
/* 486 */       String str2 = TimeUtil.getOnlyCurrentTimeString();
/* 487 */       recordSet.executeSql("insert into outter_entrance_log(userid,sysid,createdate,createtime) values(" + user.getUID() + ",'" + paramOutterSysBean
/* 488 */           .getSysId() + "','" + str1 + "','" + str2 + "')");
/* 489 */       recordSet.executeSql("select account,password,logintype from outter_account where sysid='" + paramOutterSysBean.getSysId() + "' and userid=" + user.getUID());
/* 490 */       if (recordSet.next()) {
/* 491 */         outterLoginBean.setAccount(recordSet.getString("account"));
/* 492 */         outterLoginBean.setPassword(recordSet.getString("password"));
/*     */         
/* 494 */         if (!"".equals(outterLoginBean.getPassword())) {
/* 495 */           outterLoginBean.setPassword(SecurityHelper.decryptSimple(outterLoginBean.getPassword()));
/*     */         }
/* 497 */         String str3 = "";
/* 498 */         String str4 = "select password from hrmresource where id=" + user.getUID();
/* 499 */         RecordSet recordSet1 = new RecordSet();
/* 500 */         recordSet1.executeSql(str4);
/* 501 */         if (recordSet1.next()) {
/* 502 */           str3 = recordSet1.getString("password");
/*     */         }
/* 504 */         if (paramOutterSysBean.getBaseType1().intValue() == 1) {
/* 505 */           outterLoginBean.setAccount(user.getLoginid());
/*     */         }
/* 507 */         if (paramOutterSysBean.getBaseType2().intValue() == 1) {
/*     */           
/* 509 */           String str = Util.null2String(rSA.decrypt(null, user.getPwd(), true));
/* 510 */           str = "".equals(str) ? str3 : str;
/* 511 */           outterLoginBean.setPassword(str);
/*     */         } 
/*     */         
/* 514 */         String str5 = paramHttpServletRequest.getRemoteAddr();
/* 515 */         outterLoginBean = getServerUrl(paramOutterSysBean, outterLoginBean, paramString, str5, recordSet.getString("logintype"));
/*     */ 
/*     */       
/*     */       }
/* 519 */       else if ((paramOutterSysBean.getBaseType1().intValue() == 1 && paramOutterSysBean.getBaseType2().intValue() == 1 && !paramOutterSysBean.getBaseParam1().equals("") && 
/* 520 */         !paramOutterSysBean.getBaseParam2().equals("")) || (paramOutterSysBean.getBaseParam1().equals("") && paramOutterSysBean.getBaseParam2().equals("")) || (paramOutterSysBean
/* 521 */         .getBaseParam1().equals("") && paramOutterSysBean.getBaseType2().intValue() == 1 && !paramOutterSysBean.getBaseParam2().equals("")) || (paramOutterSysBean
/* 522 */         .getBaseParam2().equals("") && paramOutterSysBean.getBaseType1().intValue() == 1 && !paramOutterSysBean.getBaseParam1().equals(""))) {
/*     */         
/* 524 */         RecordSet recordSet1 = new RecordSet();
/* 525 */         recordSet1.executeSql("select * from outter_sysparam where paramtype = 1 and sysid='" + paramOutterSysBean.getSysId() + "' order by indexid");
/* 526 */         if (recordSet1.getCounts() == 0) {
/* 527 */           outterLoginBean.setAccount(user.getLoginid());
/*     */ 
/*     */           
/* 530 */           outterLoginBean.setPassword(rSA.decrypt(null, user.getPwd(), true));
/*     */           
/* 532 */           recordSet1.executeSql("insert into outter_account(sysid,userid,logintype,createdate,createtime,modifydate,modifytime) values('" + paramOutterSysBean.getSysId() + "'," + user
/* 533 */               .getUID() + "," + outterLoginBean.getLoginType() + ",'" + str1 + "','" + str2 + "','" + str1 + "','" + str2 + "')");
/*     */ 
/*     */           
/* 536 */           String str = paramHttpServletRequest.getRemoteAddr();
/* 537 */           outterLoginBean = getServerUrl(paramOutterSysBean, outterLoginBean, paramString, str, null);
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 542 */       if ((!paramOutterSysBean.getBaseParam1().equals("") && paramOutterSysBean.getBaseType1().intValue() == 0 && ("".equals(outterLoginBean.getAccount()) || outterLoginBean.getAccount() == null)) || (
/* 543 */         !paramOutterSysBean.getBaseParam2().equals("") && paramOutterSysBean.getBaseType2().intValue() == 0 && ("".equals(outterLoginBean.getPassword()) || outterLoginBean.getPassword() == null))) {
/* 544 */         outterLoginBean.setServerUrl(ConstantsUtil.INTEGRATION_OUTTER_ACCOUNTSET1 + paramOutterSysBean.getSysId());
/*     */       }
/*     */     } else {
/*     */       
/* 548 */       Enumeration<String> enumeration = paramHttpServletRequest.getParameterNames();
/* 549 */       while (enumeration.hasMoreElements()) {
/* 550 */         String str1 = Util.null2String(enumeration.nextElement());
/*     */         
/* 552 */         if (paramOutterSysBean.getBaseParam1().equals(str1)) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 559 */           String str2 = Util.null2String(paramHttpServletRequest.getParameter(str1));
/* 560 */           String str3 = Util.null2String(paramHttpServletRequest.getParameter("_conflictID_"));
/* 561 */           if (str3.length() > 0) {
/* 562 */             str2 = str3;
/*     */           }
/*     */           
/* 565 */           outterLoginBean.setAccount(str2);
/*     */         } 
/*     */         
/* 568 */         if (paramOutterSysBean.getBaseParam2().equals(str1))
/*     */         {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 575 */           outterLoginBean.setPassword(Util.null2String(paramHttpServletRequest.getParameter(str1)));
/*     */         }
/*     */         
/* 578 */         if ("logintype_sysparam".equals(str1)) {
/* 579 */           outterLoginBean.setLoginType(Util.null2String(paramHttpServletRequest.getParameter(str1)));
/*     */         }
/*     */       } 
/*     */       
/* 583 */       String str = paramHttpServletRequest.getRemoteAddr();
/* 584 */       outterLoginBean = getServerUrl(paramOutterSysBean, outterLoginBean, paramString, str, null);
/*     */     } 
/*     */     
/* 587 */     List list = paramOutterSysBean.getParamBeanList();
/*     */     
/* 589 */     for (OutterSysParamBean outterSysParamBean : list) {
/* 590 */       if (outterSysParamBean.getParamType().intValue() == 1 && 
/* 591 */         StringUtils.isBlank(outterSysParamBean.getParamValue())) {
/* 592 */         outterLoginBean.setServerUrl(ConstantsUtil.INTEGRATION_OUTTER_ACCOUNTSET1 + paramOutterSysBean.getSysId());
/*     */       }
/*     */ 
/*     */       
/* 596 */       EncryptService encryptService1 = new EncryptService();
/* 597 */       Map map1 = encryptService1.encryptParamValue(outterLoginBean.getOutterSysBean(), outterSysParamBean);
/* 598 */       if (map1.get("isError") != null && "true".equals(map1.get("isError"))) {
/* 599 */         this.log.error("其他参数加密失败");
/* 600 */         throw new OtherParamsEncryptException(SystemEnv.getHtmlLabelName(508614, Util.getIntValue(user.getLanguage())));
/*     */       } 
/* 602 */       outterLoginBean.getParamValues().put(outterSysParamBean.getParamName(), outterSysParamBean.getParamValue());
/*     */     } 
/*     */ 
/*     */     
/* 606 */     EncryptService encryptService = new EncryptService();
/* 607 */     Map map = encryptService.encryptAccountAndPassword(outterLoginBean.getOutterSysBean(), outterLoginBean);
/* 608 */     if (map.get("isError") != null && "true".equals(map.get("isError"))) {
/* 609 */       this.log.error("账号密码加密失败");
/* 610 */       throw new AccountOrPasswordEncryptException(SystemEnv.getHtmlLabelName(508615, Util.getIntValue(user.getLanguage())));
/*     */     } 
/* 612 */     return outterLoginBean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public OutterLoginBean getLoginBeanNew(OutterSysBean paramOutterSysBean, String paramString, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 625 */     User user = getUser(paramHttpServletRequest, paramHttpServletResponse);
/* 626 */     RSA rSA = new RSA();
/*     */     
/* 628 */     OutterLoginBean outterLoginBean = new OutterLoginBean();
/* 629 */     outterLoginBean.setOutterSysBean(paramOutterSysBean);
/* 630 */     outterLoginBean.setSysId(paramOutterSysBean.getSysId());
/*     */     
/* 632 */     RecordSet recordSet = new RecordSet();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 642 */     if (StringUtils.isBlank(paramString)) {
/*     */ 
/*     */       
/* 645 */       String str1 = TimeUtil.getCurrentDateString();
/* 646 */       String str2 = TimeUtil.getOnlyCurrentTimeString();
/* 647 */       recordSet.executeSql("insert into outter_entrance_log(userid,sysid,createdate,createtime) values(" + user.getUID() + ",'" + paramOutterSysBean
/* 648 */           .getSysId() + "','" + str1 + "','" + str2 + "')");
/* 649 */       recordSet.executeSql("select account,password,logintype from outter_account where sysid='" + paramOutterSysBean.getSysId() + "' and userid=" + user.getUID());
/* 650 */       if (recordSet.next()) {
/* 651 */         outterLoginBean.setAccount(recordSet.getString("account"));
/* 652 */         outterLoginBean.setPassword(recordSet.getString("password"));
/*     */         
/* 654 */         if (!"".equals(outterLoginBean.getPassword())) {
/* 655 */           outterLoginBean.setPassword(SecurityHelper.decryptSimple(outterLoginBean.getPassword()));
/*     */         }
/*     */         
/* 658 */         if (paramOutterSysBean.getBaseType1().intValue() == 1) {
/* 659 */           outterLoginBean.setAccount(user.getLoginid());
/*     */         }
/* 661 */         if (paramOutterSysBean.getBaseType2().intValue() == 1)
/*     */         {
/* 663 */           outterLoginBean.setPassword(rSA.decrypt(null, user.getPwd(), true));
/*     */         }
/*     */         
/* 666 */         String str = paramHttpServletRequest.getRemoteAddr();
/* 667 */         outterLoginBean = getServerUrl(paramOutterSysBean, outterLoginBean, paramString, str, recordSet.getString("logintype"));
/*     */ 
/*     */       
/*     */       }
/* 671 */       else if ((paramOutterSysBean.getBaseType1().intValue() == 1 && paramOutterSysBean.getBaseType2().intValue() == 1 && !paramOutterSysBean.getBaseParam1().equals("") && 
/* 672 */         !paramOutterSysBean.getBaseParam2().equals("")) || (paramOutterSysBean.getBaseParam1().equals("") && paramOutterSysBean.getBaseParam2().equals("")) || (paramOutterSysBean
/* 673 */         .getBaseParam1().equals("") && paramOutterSysBean.getBaseType2().intValue() == 1 && !paramOutterSysBean.getBaseParam2().equals("")) || (paramOutterSysBean
/* 674 */         .getBaseParam2().equals("") && paramOutterSysBean.getBaseType1().intValue() == 1 && !paramOutterSysBean.getBaseParam1().equals(""))) {
/*     */         
/* 676 */         RecordSet recordSet1 = new RecordSet();
/* 677 */         recordSet1.executeSql("select * from outter_sysparam where paramtype = 1 and sysid='" + paramOutterSysBean.getSysId() + "' order by indexid");
/* 678 */         if (recordSet1.getCounts() == 0) {
/* 679 */           outterLoginBean.setAccount(user.getLoginid());
/*     */ 
/*     */           
/* 682 */           outterLoginBean.setPassword(rSA.decrypt(null, user.getPwd(), true));
/*     */           
/* 684 */           recordSet1.executeSql("insert into outter_account(sysid,userid,logintype,createdate,createtime,modifydate,modifytime) values('" + paramOutterSysBean.getSysId() + "'," + user
/* 685 */               .getUID() + "," + outterLoginBean.getLoginType() + ",'" + str1 + "','" + str2 + "','" + str1 + "','" + str2 + "')");
/*     */ 
/*     */           
/* 688 */           String str = paramHttpServletRequest.getRemoteAddr();
/* 689 */           outterLoginBean = getServerUrl(paramOutterSysBean, outterLoginBean, paramString, str, null);
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 694 */       if ((!paramOutterSysBean.getBaseParam1().equals("") && paramOutterSysBean.getBaseType1().intValue() == 0 && ("".equals(outterLoginBean.getAccount()) || outterLoginBean.getAccount() == null)) || (
/* 695 */         !paramOutterSysBean.getBaseParam2().equals("") && paramOutterSysBean.getBaseType2().intValue() == 0 && ("".equals(outterLoginBean.getPassword()) || outterLoginBean.getPassword() == null))) {
/* 696 */         outterLoginBean.setServerUrl(ConstantsUtil.INTEGRATION_OUTTER_ACCOUNTSET1 + paramOutterSysBean.getSysId());
/*     */       }
/*     */     } else {
/*     */       
/* 700 */       Enumeration<String> enumeration = paramHttpServletRequest.getParameterNames();
/* 701 */       while (enumeration.hasMoreElements()) {
/* 702 */         String str1 = Util.null2String(enumeration.nextElement());
/*     */         
/* 704 */         if (paramOutterSysBean.getBaseParam1().equals(str1))
/*     */         {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 711 */           outterLoginBean.setAccount(Util.null2String(paramHttpServletRequest.getParameter(str1)));
/*     */         }
/*     */         
/* 714 */         if (paramOutterSysBean.getBaseParam2().equals(str1))
/*     */         {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 721 */           outterLoginBean.setPassword(Util.null2String(paramHttpServletRequest.getParameter(str1)));
/*     */         }
/*     */         
/* 724 */         if ("logintype_sysparam".equals(str1)) {
/* 725 */           outterLoginBean.setLoginType(Util.null2String(paramHttpServletRequest.getParameter(str1)));
/*     */         }
/*     */       } 
/*     */       
/* 729 */       String str = paramHttpServletRequest.getRemoteAddr();
/* 730 */       outterLoginBean = getServerUrl(paramOutterSysBean, outterLoginBean, paramString, str, null);
/*     */     } 
/*     */     
/* 733 */     List list = paramOutterSysBean.getParamBeanList();
/*     */     
/* 735 */     for (OutterSysParamBean outterSysParamBean : list) {
/* 736 */       if (outterSysParamBean.getParamType().intValue() == 1 && 
/* 737 */         StringUtils.isBlank(outterSysParamBean.getParamValue())) {
/* 738 */         outterLoginBean.setServerUrl(ConstantsUtil.INTEGRATION_OUTTER_ACCOUNTSET1 + paramOutterSysBean.getSysId());
/*     */       }
/*     */ 
/*     */       
/* 742 */       EncryptService encryptService = new EncryptService();
/* 743 */       Map map = encryptService.encryptParamValue(outterLoginBean.getOutterSysBean(), outterSysParamBean);
/* 744 */       if (map.get("isError") != null && "true".equals(map.get("isError"))) {
/* 745 */         this.log.error("其他参数加密失败");
/* 746 */         throw new OtherParamsEncryptException(SystemEnv.getHtmlLabelName(508614, Util.getIntValue(user.getLanguage())));
/*     */       } 
/* 748 */       outterLoginBean.getParamValues().put(outterSysParamBean.getParamName(), outterSysParamBean.getParamValue());
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 758 */     return outterLoginBean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public OutterLoginBean getServerUrl(OutterSysBean paramOutterSysBean, OutterLoginBean paramOutterLoginBean, String paramString1, String paramString2, String paramString3) {
/* 780 */     if (StringUtils.isBlank(paramString1)) {
/* 781 */       if ("0".equals(paramOutterSysBean.getTypeName())) {
/* 782 */         startAutoLoginFlag(paramOutterSysBean, paramOutterLoginBean, paramString2, paramString3);
/*     */       }
/* 784 */       if (("1".equals(paramOutterSysBean.getTypeName()) || "5".equals(paramOutterSysBean.getTypeName())) && 
/* 785 */         paramOutterLoginBean.getAccount() != null && paramOutterLoginBean.getAccount().length() > 0 && paramOutterLoginBean
/* 786 */         .getPassword() != null && paramOutterLoginBean.getPassword().length() > 0) {
/* 787 */         startAutoLoginFlag(paramOutterSysBean, paramOutterLoginBean, paramString2, paramString3);
/*     */       }
/*     */ 
/*     */       
/* 791 */       if (("7".equals(paramOutterSysBean.getTypeName()) || "11".equals(paramOutterSysBean.getTypeName()) || "2".equals(paramOutterSysBean.getTypeName())) && 
/* 792 */         paramOutterLoginBean.getAccount() != null && paramOutterLoginBean.getAccount().length() > 0) {
/* 793 */         startAutoLoginFlag(paramOutterSysBean, paramOutterLoginBean, paramString2, paramString3);
/*     */       }
/*     */       
/* 796 */       if ("14".equals(paramOutterSysBean.getTypeName())) {
/* 797 */         startAutoLoginFlag(paramOutterSysBean, paramOutterLoginBean, paramString2, paramString3);
/*     */       }
/* 799 */       if ("8".equals(paramOutterSysBean.getTypeName()))
/*     */       {
/* 801 */         startAutoLoginFlag(paramOutterSysBean, paramOutterLoginBean, paramString2, paramString3);
/*     */       }
/*     */     } else {
/*     */       
/* 805 */       startAutoLoginFlag(paramOutterSysBean, paramOutterLoginBean, paramString2, paramString3);
/*     */     } 
/*     */     
/* 808 */     return paramOutterLoginBean;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void startAutoLoginFlag(OutterSysBean paramOutterSysBean, OutterLoginBean paramOutterLoginBean, String paramString1, String paramString2) {
/* 814 */     if (paramOutterSysBean.getAutoLogin().equals("1")) {
/* 815 */       CheckIpNetWork checkIpNetWork = new CheckIpNetWork();
/* 816 */       boolean bool = checkIpNetWork.checkIpSeg(paramString1, paramOutterSysBean.getSysId());
/* 817 */       if (bool) {
/* 818 */         paramOutterLoginBean.setServerUrl(paramOutterSysBean.getOurl());
/*     */       } else {
/* 820 */         paramOutterLoginBean.setServerUrl(paramOutterSysBean.getIurl());
/*     */       } 
/*     */     } else {
/* 823 */       if (!StringUtils.isBlank(paramString2)) {
/* 824 */         paramOutterLoginBean.setLoginType(paramString2);
/*     */       }
/* 826 */       if (paramOutterLoginBean.getLoginType().equals("1")) {
/* 827 */         paramOutterLoginBean.setServerUrl(paramOutterSysBean.getIurl());
/*     */       } else {
/* 829 */         paramOutterLoginBean.setServerUrl(paramOutterSysBean.getOurl());
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMap(RecordSet paramRecordSet, Object paramObject) {
/* 843 */     String[] arrayOfString = paramRecordSet.getColumnName();
/* 844 */     ArrayList<String> arrayList = new ArrayList();
/* 845 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 847 */     Class<?> clazz = paramObject.getClass();
/*     */     
/* 849 */     Field[] arrayOfField = clazz.getDeclaredFields();
/* 850 */     for (Field field : arrayOfField) {
/* 851 */       arrayList.add(field.getName().toLowerCase());
/*     */     }
/* 853 */     for (String str : arrayOfString) {
/* 854 */       if (!arrayList.contains(str.toLowerCase().replace("_", "")))
/*     */       {
/* 856 */         hashMap.put(str, Util.null2String(paramRecordSet.getString(str)));
/*     */       }
/*     */     } 
/* 859 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public String encodeStr(String paramString1, String paramString2) {
/* 864 */     if (paramString2.equals("1")) {
/* 865 */       return URLEncoder.encode(paramString1);
/*     */     }
/* 867 */     return paramString1;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private Map<String, Object> goRedirectUrl(OutterLoginBean paramOutterLoginBean, String paramString, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 873 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 875 */     if (OutterConstant.ENTRANCE_QQEMAIL_JSP.equals(paramOutterLoginBean.getOutterSysBean().getEntranceUrl())) {
/* 876 */       QQMailServer qQMailServer = new QQMailServer();
/* 877 */       return qQMailServer.getRedirectUrl(paramOutterLoginBean, paramString, paramHttpServletRequest, paramHttpServletResponse);
/*     */     } 
/*     */     
/* 880 */     if (OutterConstant.ENTRANCE_EMAIL163_JSP.equals(paramOutterLoginBean.getOutterSysBean().getEntranceUrl())) {
/* 881 */       Email163Server email163Server = new Email163Server();
/* 882 */       return email163Server.getRedirectUrl(paramOutterLoginBean, paramString, paramHttpServletRequest, paramHttpServletResponse);
/*     */     } 
/*     */     
/* 885 */     if (OutterConstant.ENTRANCE_EMAIL263_JSP.equals(paramOutterLoginBean.getOutterSysBean().getEntranceUrl())) {
/* 886 */       Email263Server email263Server = new Email263Server();
/* 887 */       return email263Server.getRedirectUrl(paramOutterLoginBean, paramString, paramHttpServletRequest, paramHttpServletResponse);
/*     */     } 
/*     */     
/* 890 */     if (OutterConstant.NCENTRANCE_JSP.equals(paramOutterLoginBean.getOutterSysBean().getEntranceUrl())) {
/* 891 */       NcServer ncServer = new NcServer();
/* 892 */       return ncServer.getRedirectUrl(paramOutterLoginBean, paramString, paramHttpServletRequest, paramHttpServletResponse);
/*     */     } 
/* 894 */     if (OutterConstant.NC6ENTRANCE_JSP.equals(paramOutterLoginBean.getOutterSysBean().getEntranceUrl())) {
/* 895 */       Nc6Server nc6Server = new Nc6Server();
/* 896 */       return nc6Server.getRedirectUrl(paramOutterLoginBean, paramString, paramHttpServletRequest, paramHttpServletResponse);
/*     */     } 
/* 898 */     if (OutterConstant.K3CLOUD_ENTRANCE_JSP.equals(paramOutterLoginBean.getOutterSysBean().getEntranceUrl())) {
/* 899 */       K3CloudServer k3CloudServer = new K3CloudServer();
/* 900 */       return k3CloudServer.getRedirectUrl(paramOutterLoginBean, paramString, paramHttpServletRequest, paramHttpServletResponse);
/*     */     } 
/* 902 */     if (OutterConstant.K3CLOUD_ENTRANCE_JSPV2.equals(paramOutterLoginBean.getOutterSysBean().getEntranceUrl())) {
/* 903 */       K3CloudServerV2 k3CloudServerV2 = new K3CloudServerV2();
/* 904 */       return k3CloudServerV2.getRedirectUrl(paramOutterLoginBean, paramString, paramHttpServletRequest, paramHttpServletResponse);
/*     */     } 
/*     */     
/* 907 */     if (OutterConstant.ENTRANCE_COREMAIL_JSP.equals(paramOutterLoginBean.getOutterSysBean().getEntranceUrl())) {
/* 908 */       CoreMailServer coreMailServer = new CoreMailServer();
/* 909 */       return coreMailServer.getRedirectUrl(paramOutterLoginBean, paramString, paramHttpServletRequest, paramHttpServletResponse);
/*     */     } 
/*     */     
/* 912 */     if (OutterConstant.EASENTRANCE_JSP.equals(paramOutterLoginBean.getOutterSysBean().getEntranceUrl())) {
/* 913 */       EASServer eASServer = new EASServer();
/* 914 */       return eASServer.getRedirectUrl(paramOutterLoginBean, paramString, paramHttpServletRequest, paramHttpServletResponse);
/*     */     } 
/*     */     
/* 917 */     if (OutterConstant.ALYENTRANCE_JSP.equals(paramOutterLoginBean.getOutterSysBean().getEntranceUrl())) {
/* 918 */       ALYMailServer aLYMailServer = new ALYMailServer();
/* 919 */       return aLYMailServer.getRedirectUrl(paramOutterLoginBean, paramString, paramHttpServletRequest, paramHttpServletResponse);
/*     */     } 
/* 921 */     if (OutterConstant.ENTRANCE_NEWEMAIL163_JSP.equals(paramOutterLoginBean.getOutterSysBean().getEntranceUrl())) {
/* 922 */       NewEmail263Server newEmail263Server = new NewEmail263Server();
/* 923 */       return newEmail263Server.getRedirectUrl(paramOutterLoginBean, paramString, paramHttpServletRequest, paramHttpServletResponse);
/*     */     } 
/* 925 */     hashMap.put("isRedirect", "true");
/* 926 */     hashMap.put("redirectUrl", paramOutterLoginBean.getOutterSysBean().getEntranceUrl() + "?id=" + paramOutterLoginBean.getOutterSysBean().getSysId());
/* 927 */     hashMap.put("previewUrl", paramOutterLoginBean.getOutterSysBean().getEntranceUrl() + "?id=" + paramOutterLoginBean.getOutterSysBean().getSysId());
/*     */     
/* 929 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setOutterSysParamBeanFromDB(OutterSysBean paramOutterSysBean, User paramUser) {
/* 943 */     RSA rSA = new RSA();
/* 944 */     RecordSet recordSet = new RecordSet();
/* 945 */     ArrayList<OutterSysParamBean> arrayList = new ArrayList();
/* 946 */     recordSet.executeSql("select * from outter_sysparam where sysid='" + paramOutterSysBean.getSysId() + "' order by indexid");
/*     */ 
/*     */     
/* 949 */     while (recordSet.next()) {
/* 950 */       OutterSysParamBean outterSysParamBean = new OutterSysParamBean();
/* 951 */       outterSysParamBean.setParamType(Integer.valueOf(Util.getIntValue(recordSet.getString("paramtype"), 0)));
/* 952 */       outterSysParamBean.setParamName(recordSet.getString("paramname"));
/* 953 */       outterSysParamBean.setParamValue(recordSet.getString("paramvalue"));
/* 954 */       outterSysParamBean.setParaEncrypt(recordSet.getString("paraencrypt"));
/* 955 */       outterSysParamBean.setEncryptCode(recordSet.getString("encryptcode"));
/* 956 */       outterSysParamBean.setEncryptIv(recordSet.getString("encryptiv"));
/*     */ 
/*     */       
/* 959 */       outterSysParamBean.setParamMap(getMap(recordSet, outterSysParamBean));
/*     */       
/* 961 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 962 */       OutterUtil outterUtil = new OutterUtil();
/* 963 */       Map map = outterUtil.expressionValueMap(paramOutterSysBean.getSysId(), paramUser, rSA.decrypt(null, paramUser.getPwd(), true));
/*     */       
/* 965 */       if (outterSysParamBean.getParamType().intValue() != 0)
/*     */       {
/* 967 */         if (outterSysParamBean.getParamType().intValue() == 1) {
/* 968 */           RecordSet recordSet1 = new RecordSet();
/* 969 */           recordSet1.executeSql("select * from outter_params where sysid='" + paramOutterSysBean
/* 970 */               .getSysId() + "' and userid=" + paramUser.getUID() + " and paramname='" + outterSysParamBean
/* 971 */               .getParamName() + "'");
/* 972 */           if (recordSet1.next()) {
/* 973 */             outterSysParamBean.setParamValue(recordSet1.getString("paramvalue"));
/*     */           }
/* 975 */         } else if (outterSysParamBean.getParamType().intValue() == 2) {
/* 976 */           outterSysParamBean.setParamValue(paramUser.getUserSubCompany1() + "");
/* 977 */         } else if (outterSysParamBean.getParamType().intValue() == 3) {
/* 978 */           outterSysParamBean.setParamValue(paramUser.getUserDepartment() + "");
/* 979 */         } else if (outterSysParamBean.getParamType().intValue() == 4) {
/* 980 */           OutterUtil outterUtil1 = new OutterUtil();
/* 981 */           outterSysParamBean.setParamValue(outterUtil1.getExpressionValue(outterSysParamBean.getParamValue(), paramOutterSysBean.getSysId(), paramUser, rSA
/* 982 */                 .decrypt(null, paramUser.getPwd(), true), map));
/* 983 */         } else if (outterSysParamBean.getParamType().intValue() == 5) {
/*     */           continue;
/*     */         } 
/*     */       }
/* 987 */       arrayList.add(outterSysParamBean);
/*     */     } 
/* 989 */     paramOutterSysBean.setParamBeanList(arrayList);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/service/OutterSysServer.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */