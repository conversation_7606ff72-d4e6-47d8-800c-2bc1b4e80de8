package weaver.integration.entrance.service;

import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import weaver.hrm.User;
import weaver.integration.entrance.bean.OutterLoginBean;
import weaver.integration.entrance.bean.OutterSysBean;

public interface IOutterSysServer {
  OutterSysBean getOutterSysBeanFromDB(String paramString1, String paramString2, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws IllegalAccessException;
  
  void setOutterEncryptBeanFromDB(OutterSysBean paramOutterSysBean) throws IllegalAccessException;
  
  void setOutterSysParamBeanFromDB(OutterSysBean paramOutterSysBean, String paramString, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse);
  
  void setEncryptMethod(OutterSysBean paramOutterSysBean) throws ClassNotFoundException, NoSuchMethodException, IllegalAccessException, InstantiationException;
  
  OutterLoginBean getLoginBean(OutterSysBean paramOutterSysBean, String paramString, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse);
  
  OutterLoginBean getServerUrl(OutterSysBean paramOutterSysBean, OutterLoginBean paramOutterLoginBean, String paramString1, String paramString2, String paramString3);
  
  void startAutoLoginFlag(OutterSysBean paramOutterSysBean, OutterLoginBean paramOutterLoginBean, String paramString1, String paramString2);
  
  Map<String, Object> getRedirectUrl(OutterLoginBean paramOutterLoginBean, String paramString, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse);
  
  User getUser(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/service/IOutterSysServer.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */