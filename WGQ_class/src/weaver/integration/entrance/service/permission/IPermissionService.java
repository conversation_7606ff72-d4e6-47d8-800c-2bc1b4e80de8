package weaver.integration.entrance.service.permission;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import weaver.hrm.User;

public interface IPermissionService {
  String getOperationType(HttpServletRequest paramHttpServletRequest);
  
  String isAccess(String paramString, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse);
  
  User getUser(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/service/permission/IPermissionService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */