/*    */ package weaver.integration.entrance.service.permission;
/*    */ 
/*    */ import java.util.Enumeration;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.HrmUserVarify;
/*    */ import weaver.hrm.User;
/*    */ import weaver.outter.OutterDisplayHelper;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class PermissionService
/*    */   implements IPermissionService
/*    */ {
/*    */   public String getOperationType(HttpServletRequest paramHttpServletRequest) {
/* 20 */     String str = "";
/* 21 */     Enumeration<String> enumeration = paramHttpServletRequest.getParameterNames();
/* 22 */     if (enumeration != null) {
/* 23 */       while (enumeration.hasMoreElements()) {
/* 24 */         String str1 = Util.null2String(enumeration.nextElement());
/* 25 */         if ("operationType".equals(str1)) {
/* 26 */           String str2 = Util.null2String(paramHttpServletRequest.getParameter(str1));
/* 27 */           if ("test".equals(str2))
/* 28 */             str = str2;  continue;
/*    */         } 
/* 30 */         if ("operate".equals(str1)) {
/* 31 */           String str2 = Util.null2String(paramHttpServletRequest.getParameter(str1));
/* 32 */           if ("test".equals(str2)) {
/* 33 */             str = str2;
/*    */           }
/*    */         } 
/*    */       } 
/*    */     }
/* 38 */     return str;
/*    */   }
/*    */ 
/*    */   
/*    */   public User getUser(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 43 */     return HrmUserVarify.getUser(paramHttpServletRequest, paramHttpServletResponse);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public String isAccess(String paramString, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 49 */     RecordSet recordSet = new RecordSet();
/*    */ 
/*    */     
/* 52 */     OutterDisplayHelper outterDisplayHelper = new OutterDisplayHelper();
/* 53 */     String str1 = outterDisplayHelper.getShareOutterSql(getUser(paramHttpServletRequest, paramHttpServletResponse));
/* 54 */     String str2 = "select sysid from outter_sys a where sysid='" + paramString + "' and EXISTS (select 1 from (" + str1 + ") b where a.sysid=b.sysid )";
/* 55 */     recordSet.executeSql(str2);
/*    */     
/* 57 */     if (recordSet.getCounts() < 1) {
/* 58 */       return "/notice/noright.jsp";
/*    */     }
/* 60 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/service/permission/PermissionService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */