/*     */ package weaver.integration.entrance.utils;
/*     */ 
/*     */ import java.util.Collection;
/*     */ import java.util.Iterator;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class StringUtils
/*     */ {
/*     */   public static final String EMPTY = "";
/*     */   
/*     */   public static boolean isEmpty(String paramString) {
/*  31 */     return (paramString == null || paramString.length() == 0);
/*     */   }
/*     */ 
/*     */   
/*     */   public static boolean isContainEmpty(String... paramVarArgs) {
/*  36 */     if (paramVarArgs == null) {
/*  37 */       return false;
/*     */     }
/*     */     
/*  40 */     for (String str : paramVarArgs) {
/*  41 */       if (str == null || "".equals(str)) {
/*  42 */         return true;
/*     */       }
/*     */     } 
/*     */     
/*  46 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isBlank(String paramString) {
/*     */     int i;
/*  66 */     if (paramString == null || (i = paramString.length()) == 0) {
/*  67 */       return true;
/*     */     }
/*  69 */     for (byte b = 0; b < i; b++) {
/*  70 */       if (!Character.isWhitespace(paramString.charAt(b))) {
/*  71 */         return false;
/*     */       }
/*     */     } 
/*  74 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String trimToNull(String paramString) {
/* 100 */     String str = trim(paramString);
/* 101 */     return isEmpty(str) ? null : str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String trimToEmpty(String paramString) {
/* 127 */     return (paramString == null) ? "" : paramString.trim();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String trim(String paramString) {
/* 157 */     return (paramString == null) ? null : paramString.trim();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean equals(String paramString1, String paramString2) {
/* 184 */     return (paramString1 == null) ? ((paramString2 == null)) : paramString1.equals(paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean equalsIgnoreCase(String paramString1, String paramString2) {
/* 211 */     return (paramString1 == null) ? ((paramString2 == null)) : paramString1.equalsIgnoreCase(paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean startsWith(String paramString1, String paramString2) {
/* 239 */     return startsWith(paramString1, paramString2, false);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static boolean startsWith(String paramString1, String paramString2, boolean paramBoolean) {
/* 254 */     if (paramString1 == null || paramString2 == null) {
/* 255 */       return (paramString1 == null && paramString2 == null);
/*     */     }
/* 257 */     if (paramString2.length() > paramString1.length()) {
/* 258 */       return false;
/*     */     }
/* 260 */     return paramString1.regionMatches(paramBoolean, 0, paramString2, 0, paramString2.length());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean startsWithIgnoreCase(String paramString1, String paramString2) {
/* 288 */     return startsWith(paramString1, paramString2, true);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isNumeric(String paramString) {
/* 315 */     if (paramString == null) {
/* 316 */       return false;
/*     */     }
/* 318 */     int i = paramString.length();
/* 319 */     for (byte b = 0; b < i; b++) {
/* 320 */       if (!Character.isDigit(paramString.charAt(b))) {
/* 321 */         return false;
/*     */       }
/*     */     } 
/* 324 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static <T> String join(Collection<T> paramCollection, String paramString) {
/* 332 */     return join(paramCollection, paramString, new StringFormatter<T>()
/*     */         {
/*     */           public String format(T param1T) {
/* 335 */             return param1T.toString();
/*     */           }
/*     */         });
/*     */   }
/*     */ 
/*     */   
/*     */   public static <T> String join(Collection<T> paramCollection, String paramString, StringFormatter<T> paramStringFormatter) {
/* 342 */     Iterator<T> iterator = paramCollection.iterator();
/*     */     
/* 344 */     if (iterator == null) {
/* 345 */       return null;
/*     */     }
/* 347 */     if (!iterator.hasNext()) {
/* 348 */       return "";
/*     */     }
/* 350 */     T t = iterator.next();
/* 351 */     if (!iterator.hasNext()) {
/* 352 */       return (t == null) ? "" : paramStringFormatter.format(t);
/*     */     }
/*     */ 
/*     */     
/* 356 */     StringBuilder stringBuilder = new StringBuilder(256);
/* 357 */     if (t != null) {
/* 358 */       stringBuilder.append(paramStringFormatter.format(t));
/*     */     }
/*     */     
/* 361 */     while (iterator.hasNext()) {
/* 362 */       stringBuilder.append(paramString);
/* 363 */       T t1 = iterator.next();
/* 364 */       if (t1 != null) {
/* 365 */         stringBuilder.append(paramStringFormatter.format(t1));
/*     */       }
/*     */     } 
/*     */     
/* 369 */     return stringBuilder.toString();
/*     */   }
/*     */   
/*     */   public static interface StringFormatter<T> {
/*     */     String format(T param1T);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/utils/StringUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */