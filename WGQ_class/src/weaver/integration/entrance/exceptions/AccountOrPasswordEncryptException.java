/*    */ package weaver.integration.entrance.exceptions;
/*    */ 
/*    */ 
/*    */ public class AccountOrPasswordEncryptException
/*    */   extends RuntimeException
/*    */ {
/*  7 */   private int errCode = 1;
/*  8 */   private String message = "";
/*    */   
/*    */   public AccountOrPasswordEncryptException(String paramString) {
/* 11 */     super(paramString);
/*    */   }
/*    */   
/*    */   public AccountOrPasswordEncryptException(String paramString, int paramInt) {
/* 15 */     this.errCode = paramInt;
/* 16 */     this.message = paramString;
/*    */   }
/*    */   
/*    */   public int getErrCode() {
/* 20 */     return this.errCode;
/*    */   }
/*    */   
/*    */   public String getMessage() {
/* 24 */     return this.message;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/exceptions/AccountOrPasswordEncryptException.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */