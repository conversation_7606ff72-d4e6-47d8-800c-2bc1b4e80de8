/*    */ package weaver.integration.entrance.exceptions;
/*    */ 
/*    */ 
/*    */ public class OtherParamsEncryptException
/*    */   extends RuntimeException
/*    */ {
/*  7 */   private int errCode = 1;
/*  8 */   private String message = "";
/*    */   
/*    */   public OtherParamsEncryptException(String paramString) {
/* 11 */     super(paramString);
/*    */   }
/*    */   
/*    */   public OtherParamsEncryptException(String paramString, int paramInt) {
/* 15 */     this.errCode = paramInt;
/* 16 */     this.message = paramString;
/*    */   }
/*    */   
/*    */   public int getErrCode() {
/* 20 */     return this.errCode;
/*    */   }
/*    */   
/*    */   public String getMessage() {
/* 24 */     return this.message;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/exceptions/OtherParamsEncryptException.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */