/*    */ package weaver.integration.entrance.enums;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public enum TypeName
/*    */ {
/* 11 */   COMMON_TYPE(0),
/*    */ 
/*    */ 
/*    */ 
/*    */   
/* 16 */   NC_TYPE(1),
/*    */ 
/*    */ 
/*    */ 
/*    */   
/* 21 */   NC6_TYPE(5),
/*    */ 
/*    */ 
/*    */ 
/*    */   
/* 26 */   QQ_EMAIL_TYPE(6);
/*    */   
/*    */   private final int typename;
/*    */   
/*    */   TypeName(int paramInt1) {
/* 31 */     this.typename = paramInt1;
/*    */   }
/*    */   
/*    */   public int getTypename() {
/* 35 */     return this.typename;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/enums/TypeName.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */