/*     */ package weaver.integration.entrance.bean;
/*     */ 
/*     */ import java.util.Map;
/*     */ import java.util.concurrent.ConcurrentHashMap;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class OutterEncryptClassBean
/*     */ {
/*     */   private static final String tableName1 = "outter_encryptclass";
/*     */   private static final String tableName2 = "outter_encryptclass_sys";
/*     */   private Integer id;
/*     */   private String encryptClass;
/*     */   private String encryptMethod;
/*     */   private Integer dataType;
/*     */   private String decryptMethod;
/*     */   private String isNeedPwd;
/*     */   private String password;
/*     */   private String isNeedIv;
/*     */   private String ivParam;
/*     */   private String encryptName;
/*  30 */   private Map<String, Object> encryptMap = new ConcurrentHashMap<>();
/*     */   
/*     */   public static String getTableName1() {
/*  33 */     return "outter_encryptclass";
/*     */   }
/*     */   
/*     */   public static String getTableName2() {
/*  37 */     return "outter_encryptclass_sys";
/*     */   }
/*     */   
/*     */   public Integer getId() {
/*  41 */     return this.id;
/*     */   }
/*     */   
/*     */   public void setId(Integer paramInteger) {
/*  45 */     this.id = paramInteger;
/*     */   }
/*     */   
/*     */   public String getEncryptClass() {
/*  49 */     return this.encryptClass;
/*     */   }
/*     */   
/*     */   public void setEncryptClass(String paramString) {
/*  53 */     this.encryptClass = paramString;
/*     */   }
/*     */   
/*     */   public String getEncryptMethod() {
/*  57 */     return this.encryptMethod;
/*     */   }
/*     */   
/*     */   public void setEncryptMethod(String paramString) {
/*  61 */     this.encryptMethod = paramString;
/*     */   }
/*     */   
/*     */   public Integer getDataType() {
/*  65 */     return this.dataType;
/*     */   }
/*     */   
/*     */   public void setDataType(Integer paramInteger) {
/*  69 */     this.dataType = paramInteger;
/*     */   }
/*     */   
/*     */   public String getDecryptMethod() {
/*  73 */     return this.decryptMethod;
/*     */   }
/*     */   
/*     */   public void setDecryptMethod(String paramString) {
/*  77 */     this.decryptMethod = paramString;
/*     */   }
/*     */   
/*     */   public String getPassword() {
/*  81 */     return this.password;
/*     */   }
/*     */   
/*     */   public void setPassword(String paramString) {
/*  85 */     this.password = paramString;
/*     */   }
/*     */   
/*     */   public String getIvParam() {
/*  89 */     return this.ivParam;
/*     */   }
/*     */   
/*     */   public void setIvParam(String paramString) {
/*  93 */     this.ivParam = paramString;
/*     */   }
/*     */   
/*     */   public String getEncryptName() {
/*  97 */     return this.encryptName;
/*     */   }
/*     */   
/*     */   public void setEncryptName(String paramString) {
/* 101 */     this.encryptName = paramString;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getEncryptMap() {
/* 105 */     return this.encryptMap;
/*     */   }
/*     */   
/*     */   public void setEncryptMap(Map<String, Object> paramMap) {
/* 109 */     this.encryptMap = paramMap;
/*     */   }
/*     */   
/*     */   public String getIsNeedPwd() {
/* 113 */     return this.isNeedPwd;
/*     */   }
/*     */   
/*     */   public void setIsNeedPwd(String paramString) {
/* 117 */     this.isNeedPwd = paramString;
/*     */   }
/*     */   
/*     */   public String getIsNeedIv() {
/* 121 */     return this.isNeedIv;
/*     */   }
/*     */   
/*     */   public void setIsNeedIv(String paramString) {
/* 125 */     this.isNeedIv = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/bean/OutterEncryptClassBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */