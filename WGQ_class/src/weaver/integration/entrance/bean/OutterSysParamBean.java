/*     */ package weaver.integration.entrance.bean;
/*     */ 
/*     */ import java.util.Map;
/*     */ import java.util.concurrent.ConcurrentHashMap;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class OutterSysParamBean
/*     */ {
/*     */   private static final String tableName = "outter_sysparam";
/*     */   private String sysId;
/*     */   private String paramName;
/*     */   private String paramValue;
/*     */   private String labelName;
/*     */   private Integer paramType;
/*     */   private Integer indexId;
/*     */   private String paraEncrypt;
/*     */   private String encryptCode;
/*     */   private String encryptIv;
/*     */   
/*     */   public static String getTableName() {
/*  26 */     return "outter_sysparam";
/*     */   }
/*     */   
/*  29 */   private Map<String, Object> paramMap = new ConcurrentHashMap<>();
/*     */   
/*     */   public String getSysId() {
/*  32 */     return this.sysId;
/*     */   }
/*     */   
/*     */   public void setSysId(String paramString) {
/*  36 */     this.sysId = paramString;
/*     */   }
/*     */   
/*     */   public String getParamName() {
/*  40 */     return this.paramName;
/*     */   }
/*     */   
/*     */   public void setParamName(String paramString) {
/*  44 */     this.paramName = paramString;
/*     */   }
/*     */   
/*     */   public String getParamValue() {
/*  48 */     return this.paramValue;
/*     */   }
/*     */   
/*     */   public void setParamValue(String paramString) {
/*  52 */     this.paramValue = paramString;
/*     */   }
/*     */   
/*     */   public String getLabelName() {
/*  56 */     return this.labelName;
/*     */   }
/*     */   
/*     */   public void setLabelName(String paramString) {
/*  60 */     this.labelName = paramString;
/*     */   }
/*     */   
/*     */   public Integer getParamType() {
/*  64 */     return this.paramType;
/*     */   }
/*     */   
/*     */   public void setParamType(Integer paramInteger) {
/*  68 */     this.paramType = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getIndexId() {
/*  72 */     return this.indexId;
/*     */   }
/*     */   
/*     */   public void setIndexId(Integer paramInteger) {
/*  76 */     this.indexId = paramInteger;
/*     */   }
/*     */   
/*     */   public String getParaEncrypt() {
/*  80 */     return this.paraEncrypt;
/*     */   }
/*     */   
/*     */   public void setParaEncrypt(String paramString) {
/*  84 */     this.paraEncrypt = paramString;
/*     */   }
/*     */   
/*     */   public String getEncryptCode() {
/*  88 */     return this.encryptCode;
/*     */   }
/*     */   
/*     */   public void setEncryptCode(String paramString) {
/*  92 */     this.encryptCode = paramString;
/*     */   }
/*     */   
/*     */   public String getEncryptIv() {
/*  96 */     return this.encryptIv;
/*     */   }
/*     */   
/*     */   public void setEncryptIv(String paramString) {
/* 100 */     this.encryptIv = paramString;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getParamMap() {
/* 104 */     return this.paramMap;
/*     */   }
/*     */   
/*     */   public void setParamMap(Map<String, Object> paramMap) {
/* 108 */     this.paramMap = paramMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/bean/OutterSysParamBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */