/*     */ package weaver.integration.entrance.bean;
/*     */ 
/*     */ import java.util.Map;
/*     */ import java.util.concurrent.ConcurrentHashMap;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class OutterLoginBean
/*     */ {
/*     */   private static final String tableName = "outter_account";
/*     */   private String account;
/*     */   private String password;
/*  17 */   private String loginType = "1";
/*     */   
/*     */   private Integer userId;
/*     */   
/*     */   private String modifyTime;
/*     */   
/*     */   private String pcUrl;
/*     */   private String sysId;
/*     */   private String createDate;
/*     */   private String createTime;
/*     */   private String modifyDate;
/*     */   private String serverUrl;
/*     */   private OutterSysBean outterSysBean;
/*  30 */   Map<String, String> paramValues = new ConcurrentHashMap<>();
/*     */   
/*     */   public static String getTableName() {
/*  33 */     return "outter_account";
/*     */   }
/*     */   
/*     */   public Map<String, String> getParamValues() {
/*  37 */     return this.paramValues;
/*     */   }
/*     */   
/*     */   public void setParamValues(Map<String, String> paramMap) {
/*  41 */     this.paramValues = paramMap;
/*     */   }
/*     */   
/*     */   public OutterSysBean getOutterSysBean() {
/*  45 */     return this.outterSysBean;
/*     */   }
/*     */   
/*     */   public void setOutterSysBean(OutterSysBean paramOutterSysBean) {
/*  49 */     this.outterSysBean = paramOutterSysBean;
/*     */   }
/*     */   
/*     */   public String getAccount() {
/*  53 */     return this.account;
/*     */   }
/*     */   
/*     */   public void setAccount(String paramString) {
/*  57 */     this.account = paramString;
/*     */   }
/*     */   
/*     */   public String getPassword() {
/*  61 */     return this.password;
/*     */   }
/*     */   
/*     */   public void setPassword(String paramString) {
/*  65 */     this.password = paramString;
/*     */   }
/*     */   
/*     */   public String getLoginType() {
/*  69 */     return this.loginType;
/*     */   }
/*     */   
/*     */   public void setLoginType(String paramString) {
/*  73 */     this.loginType = paramString;
/*     */   }
/*     */   
/*     */   public Integer getUserId() {
/*  77 */     return this.userId;
/*     */   }
/*     */   
/*     */   public void setUserId(Integer paramInteger) {
/*  81 */     this.userId = paramInteger;
/*     */   }
/*     */   
/*     */   public String getModifyTime() {
/*  85 */     return this.modifyTime;
/*     */   }
/*     */   
/*     */   public void setModifyTime(String paramString) {
/*  89 */     this.modifyTime = paramString;
/*     */   }
/*     */   
/*     */   public String getPcUrl() {
/*  93 */     return this.pcUrl;
/*     */   }
/*     */   
/*     */   public void setPcUrl(String paramString) {
/*  97 */     this.pcUrl = paramString;
/*     */   }
/*     */   
/*     */   public String getSysId() {
/* 101 */     return this.sysId;
/*     */   }
/*     */   
/*     */   public void setSysId(String paramString) {
/* 105 */     this.sysId = paramString;
/*     */   }
/*     */   
/*     */   public String getCreateDate() {
/* 109 */     return this.createDate;
/*     */   }
/*     */   
/*     */   public void setCreateDate(String paramString) {
/* 113 */     this.createDate = paramString;
/*     */   }
/*     */   
/*     */   public String getCreateTime() {
/* 117 */     return this.createTime;
/*     */   }
/*     */   
/*     */   public void setCreateTime(String paramString) {
/* 121 */     this.createTime = paramString;
/*     */   }
/*     */   
/*     */   public String getModifyDate() {
/* 125 */     return this.modifyDate;
/*     */   }
/*     */   
/*     */   public void setModifyDate(String paramString) {
/* 129 */     this.modifyDate = paramString;
/*     */   }
/*     */   
/*     */   public String getServerUrl() {
/* 133 */     return this.serverUrl;
/*     */   }
/*     */   
/*     */   public void setServerUrl(String paramString) {
/* 137 */     this.serverUrl = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/bean/OutterLoginBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */