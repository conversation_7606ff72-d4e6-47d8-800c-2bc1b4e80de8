/*     */ package weaver.integration.entrance.bean;
/*     */ 
/*     */ import java.lang.reflect.Method;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.concurrent.ConcurrentHashMap;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class OutterSysBean
/*     */ {
/*     */   private static final String tableName = "outter_sys";
/*     */   private Integer id;
/*     */   private String isAutoSso;
/*     */   private String pcUrl;
/*     */   private String sysId;
/*     */   private String name;
/*     */   private String iurl;
/*     */   private String ourl;
/*     */   private String baseParam1;
/*     */   private String baseParam2;
/*     */   private Integer baseType1;
/*     */   private Integer baseType2;
/*     */   private String coremailType;
/*     */   private String typeName;
/*     */   private String ncAccountCode;
/*     */   private String requestType;
/*     */   private String urlParaEncrypt1;
/*     */   private String encryptCode1;
/*     */   private String urlParaEncrypt2;
/*     */   private String encryptCode2;
/*     */   private String urlParaEncrypt;
/*     */   private String encryptCode;
/*     */   private String encryptType;
/*     */   private String encryptClass;
/*     */   private String encryptMethod;
/*     */   private String actionType;
/*     */   private String urlEncodeFlag;
/*     */   private String urlLinkImagId;
/*     */   private String autoLogin;
/*     */   private String encryptClassId;
/*     */   private String imageWidth;
/*     */   private String imageHeight;
/*     */   private String encodeFlag;
/*     */   private String clientId;
/*     */   private String clientSecret;
/*     */   private String email263Domain;
/*     */   private String email263Cid;
/*     */   private String email263Key;
/*     */   private String email263AuthCorpId;
/*     */   private String email263PartnerId;
/*     */   private String entranceUrl;
/*     */   private String showOrder;
/*     */   private String result;
/*     */   private String encryptIv1;
/*     */   private String encryptIv2;
/*     */   private String createDate;
/*     */   private String createTime;
/*     */   private String modifyDate;
/*     */   private String modifyTime;
/*     */   private String email163Domain;
/*     */   private String email163Key;
/*     */   private String email163PubKey;
/*  85 */   private Method methodEncode = null;
/*  86 */   private Method methodSetpwd = null;
/*  87 */   private Method methodSetiv = null;
/*     */   
/*  89 */   private Map<String, Object> sysMap = new ConcurrentHashMap<>();
/*     */   private OutterEncryptClassBean outterEncryptClassBean;
/*  91 */   List<OutterSysParamBean> paramBeanList = new ArrayList<>();
/*     */ 
/*     */   
/*  94 */   private Object object = null;
/*     */   
/*     */   private String k3server;
/*     */   private String dbid;
/*     */   private String username;
/*     */   private String appid;
/*     */   private String appsecret;
/*     */   private String timestamp;
/*     */   private String lcid;
/* 103 */   Map<String, String> otherParams = new HashMap<>();
/*     */   
/*     */   public String getCoremailType() {
/* 106 */     return this.coremailType;
/*     */   }
/*     */   
/*     */   public void setCoremailType(String paramString) {
/* 110 */     this.coremailType = paramString;
/*     */   }
/*     */   
/*     */   public static String getTableName() {
/* 114 */     return "outter_sys";
/*     */   }
/*     */   
/*     */   public List<OutterSysParamBean> getParamBeanList() {
/* 118 */     return this.paramBeanList;
/*     */   }
/*     */   
/*     */   public void setParamBeanList(List<OutterSysParamBean> paramList) {
/* 122 */     this.paramBeanList = paramList;
/*     */   }
/*     */   
/*     */   public Map<String, String> getOtherParams() {
/* 126 */     return this.otherParams;
/*     */   }
/*     */   
/*     */   public void setOtherParams(Map<String, String> paramMap) {
/* 130 */     this.otherParams = paramMap;
/*     */   }
/*     */   
/*     */   public String getK3server() {
/* 134 */     return this.k3server;
/*     */   }
/*     */   
/*     */   public void setK3server(String paramString) {
/* 138 */     this.k3server = paramString;
/*     */   }
/*     */   
/*     */   public String getDbid() {
/* 142 */     return this.dbid;
/*     */   }
/*     */   
/*     */   public void setDbid(String paramString) {
/* 146 */     this.dbid = paramString;
/*     */   }
/*     */   
/*     */   public String getUsername() {
/* 150 */     return this.username;
/*     */   }
/*     */   
/*     */   public void setUsername(String paramString) {
/* 154 */     this.username = paramString;
/*     */   }
/*     */   
/*     */   public String getAppid() {
/* 158 */     return this.appid;
/*     */   }
/*     */   
/*     */   public void setAppid(String paramString) {
/* 162 */     this.appid = paramString;
/*     */   }
/*     */   
/*     */   public String getAppsecret() {
/* 166 */     return this.appsecret;
/*     */   }
/*     */   
/*     */   public void setAppsecret(String paramString) {
/* 170 */     this.appsecret = paramString;
/*     */   }
/*     */   
/*     */   public String getTimestamp() {
/* 174 */     return this.timestamp;
/*     */   }
/*     */   
/*     */   public void setTimestamp(String paramString) {
/* 178 */     this.timestamp = paramString;
/*     */   }
/*     */   
/*     */   public String getLcid() {
/* 182 */     return this.lcid;
/*     */   }
/*     */   
/*     */   public void setLcid(String paramString) {
/* 186 */     this.lcid = paramString;
/*     */   }
/*     */   
/*     */   public Object getObject() {
/* 190 */     return this.object;
/*     */   }
/*     */   
/*     */   public void setObject(Object paramObject) {
/* 194 */     this.object = paramObject;
/*     */   }
/*     */   
/*     */   public Integer getId() {
/* 198 */     return this.id;
/*     */   }
/*     */   
/*     */   public void setId(Integer paramInteger) {
/* 202 */     this.id = paramInteger;
/*     */   }
/*     */   
/*     */   public String getIsAutoSso() {
/* 206 */     return this.isAutoSso;
/*     */   }
/*     */   
/*     */   public void setIsAutoSso(String paramString) {
/* 210 */     this.isAutoSso = paramString;
/*     */   }
/*     */   
/*     */   public String getPcUrl() {
/* 214 */     return this.pcUrl;
/*     */   }
/*     */   
/*     */   public void setPcUrl(String paramString) {
/* 218 */     this.pcUrl = paramString;
/*     */   }
/*     */   
/*     */   public String getSysId() {
/* 222 */     return this.sysId;
/*     */   }
/*     */   
/*     */   public void setSysId(String paramString) {
/* 226 */     this.sysId = paramString;
/*     */   }
/*     */   
/*     */   public String getName() {
/* 230 */     return this.name;
/*     */   }
/*     */   
/*     */   public void setName(String paramString) {
/* 234 */     this.name = paramString;
/*     */   }
/*     */   
/*     */   public String getIurl() {
/* 238 */     return this.iurl;
/*     */   }
/*     */   
/*     */   public void setIurl(String paramString) {
/* 242 */     this.iurl = paramString;
/*     */   }
/*     */   
/*     */   public String getOurl() {
/* 246 */     return this.ourl;
/*     */   }
/*     */   
/*     */   public void setOurl(String paramString) {
/* 250 */     this.ourl = paramString;
/*     */   }
/*     */   
/*     */   public String getBaseParam1() {
/* 254 */     return this.baseParam1;
/*     */   }
/*     */   
/*     */   public void setBaseParam1(String paramString) {
/* 258 */     this.baseParam1 = paramString;
/*     */   }
/*     */   
/*     */   public String getBaseParam2() {
/* 262 */     return this.baseParam2;
/*     */   }
/*     */   
/*     */   public void setBaseParam2(String paramString) {
/* 266 */     this.baseParam2 = paramString;
/*     */   }
/*     */   
/*     */   public String getTypeName() {
/* 270 */     return this.typeName;
/*     */   }
/*     */   
/*     */   public void setTypeName(String paramString) {
/* 274 */     this.typeName = paramString;
/*     */   }
/*     */   
/*     */   public String getNcAccountCode() {
/* 278 */     return this.ncAccountCode;
/*     */   }
/*     */   
/*     */   public void setNcAccountCode(String paramString) {
/* 282 */     this.ncAccountCode = paramString;
/*     */   }
/*     */   
/*     */   public String getRequestType() {
/* 286 */     return this.requestType;
/*     */   }
/*     */   
/*     */   public void setRequestType(String paramString) {
/* 290 */     this.requestType = paramString;
/*     */   }
/*     */   
/*     */   public String getUrlParaEncrypt1() {
/* 294 */     return this.urlParaEncrypt1;
/*     */   }
/*     */   
/*     */   public void setUrlParaEncrypt1(String paramString) {
/* 298 */     this.urlParaEncrypt1 = paramString;
/*     */   }
/*     */   
/*     */   public String getEncryptCode1() {
/* 302 */     return this.encryptCode1;
/*     */   }
/*     */   
/*     */   public void setEncryptCode1(String paramString) {
/* 306 */     this.encryptCode1 = paramString;
/*     */   }
/*     */   
/*     */   public String getUrlParaEncrypt2() {
/* 310 */     return this.urlParaEncrypt2;
/*     */   }
/*     */   
/*     */   public void setUrlParaEncrypt2(String paramString) {
/* 314 */     this.urlParaEncrypt2 = paramString;
/*     */   }
/*     */   
/*     */   public String getEncryptCode2() {
/* 318 */     return this.encryptCode2;
/*     */   }
/*     */   
/*     */   public void setEncryptCode2(String paramString) {
/* 322 */     this.encryptCode2 = paramString;
/*     */   }
/*     */   
/*     */   public String getUrlParaEncrypt() {
/* 326 */     return this.urlParaEncrypt;
/*     */   }
/*     */   
/*     */   public void setUrlParaEncrypt(String paramString) {
/* 330 */     this.urlParaEncrypt = paramString;
/*     */   }
/*     */   
/*     */   public String getEncryptCode() {
/* 334 */     return this.encryptCode;
/*     */   }
/*     */   
/*     */   public void setEncryptCode(String paramString) {
/* 338 */     this.encryptCode = paramString;
/*     */   }
/*     */   
/*     */   public String getEncryptType() {
/* 342 */     return this.encryptType;
/*     */   }
/*     */   
/*     */   public void setEncryptType(String paramString) {
/* 346 */     this.encryptType = paramString;
/*     */   }
/*     */   
/*     */   public String getEncryptClass() {
/* 350 */     return this.encryptClass;
/*     */   }
/*     */   
/*     */   public void setEncryptClass(String paramString) {
/* 354 */     this.encryptClass = paramString;
/*     */   }
/*     */   
/*     */   public String getEncryptMethod() {
/* 358 */     return this.encryptMethod;
/*     */   }
/*     */   
/*     */   public void setEncryptMethod(String paramString) {
/* 362 */     this.encryptMethod = paramString;
/*     */   }
/*     */   
/*     */   public String getActionType() {
/* 366 */     return this.actionType;
/*     */   }
/*     */   
/*     */   public void setActionType(String paramString) {
/* 370 */     this.actionType = paramString;
/*     */   }
/*     */   
/*     */   public String getUrlEncodeFlag() {
/* 374 */     return this.urlEncodeFlag;
/*     */   }
/*     */   
/*     */   public void setUrlEncodeFlag(String paramString) {
/* 378 */     this.urlEncodeFlag = paramString;
/*     */   }
/*     */   
/*     */   public String getUrlLinkImagId() {
/* 382 */     return this.urlLinkImagId;
/*     */   }
/*     */   
/*     */   public void setUrlLinkImagId(String paramString) {
/* 386 */     this.urlLinkImagId = paramString;
/*     */   }
/*     */   
/*     */   public String getAutoLogin() {
/* 390 */     return this.autoLogin;
/*     */   }
/*     */   
/*     */   public void setAutoLogin(String paramString) {
/* 394 */     this.autoLogin = paramString;
/*     */   }
/*     */   
/*     */   public String getClientId() {
/* 398 */     return this.clientId;
/*     */   }
/*     */   
/*     */   public void setClientId(String paramString) {
/* 402 */     this.clientId = paramString;
/*     */   }
/*     */   
/*     */   public String getClientSecret() {
/* 406 */     return this.clientSecret;
/*     */   }
/*     */   
/*     */   public void setClientSecret(String paramString) {
/* 410 */     this.clientSecret = paramString;
/*     */   }
/*     */   
/*     */   public String getEmail263Domain() {
/* 414 */     return this.email263Domain;
/*     */   }
/*     */   
/*     */   public void setEmail263Domain(String paramString) {
/* 418 */     this.email263Domain = paramString;
/*     */   }
/*     */   
/*     */   public String getEmail263Cid() {
/* 422 */     return this.email263Cid;
/*     */   }
/*     */   
/*     */   public void setEmail263Cid(String paramString) {
/* 426 */     this.email263Cid = paramString;
/*     */   }
/*     */   
/*     */   public String getEmail263Key() {
/* 430 */     return this.email263Key;
/*     */   }
/*     */   
/*     */   public void setEmail263Key(String paramString) {
/* 434 */     this.email263Key = paramString;
/*     */   }
/*     */   
/*     */   public String getEntranceUrl() {
/* 438 */     return this.entranceUrl;
/*     */   }
/*     */   
/*     */   public void setEntranceUrl(String paramString) {
/* 442 */     this.entranceUrl = paramString;
/*     */   }
/*     */   
/*     */   public String getEncryptIv1() {
/* 446 */     return this.encryptIv1;
/*     */   }
/*     */   
/*     */   public void setEncryptIv1(String paramString) {
/* 450 */     this.encryptIv1 = paramString;
/*     */   }
/*     */   
/*     */   public String getEncryptIv2() {
/* 454 */     return this.encryptIv2;
/*     */   }
/*     */   
/*     */   public void setEncryptIv2(String paramString) {
/* 458 */     this.encryptIv2 = paramString;
/*     */   }
/*     */   
/*     */   public String getCreateDate() {
/* 462 */     return this.createDate;
/*     */   }
/*     */   
/*     */   public void setCreateDate(String paramString) {
/* 466 */     this.createDate = paramString;
/*     */   }
/*     */   
/*     */   public String getCreateTime() {
/* 470 */     return this.createTime;
/*     */   }
/*     */   
/*     */   public void setCreateTime(String paramString) {
/* 474 */     this.createTime = paramString;
/*     */   }
/*     */   
/*     */   public String getModifyDate() {
/* 478 */     return this.modifyDate;
/*     */   }
/*     */   
/*     */   public void setModifyDate(String paramString) {
/* 482 */     this.modifyDate = paramString;
/*     */   }
/*     */   
/*     */   public String getModifyTime() {
/* 486 */     return this.modifyTime;
/*     */   }
/*     */   
/*     */   public void setModifyTime(String paramString) {
/* 490 */     this.modifyTime = paramString;
/*     */   }
/*     */   
/*     */   public String getEmail163Domain() {
/* 494 */     return this.email163Domain;
/*     */   }
/*     */   
/*     */   public void setEmail163Domain(String paramString) {
/* 498 */     this.email163Domain = paramString;
/*     */   }
/*     */   
/*     */   public String getEmail163Key() {
/* 502 */     return this.email163Key;
/*     */   }
/*     */   
/*     */   public void setEmail163Key(String paramString) {
/* 506 */     this.email163Key = paramString;
/*     */   }
/*     */   
/*     */   public String getEmail163PubKey() {
/* 510 */     return this.email163PubKey;
/*     */   }
/*     */   
/*     */   public void setEmail163PubKey(String paramString) {
/* 514 */     this.email163PubKey = paramString;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getSysMap() {
/* 518 */     return this.sysMap;
/*     */   }
/*     */   
/*     */   public void setSysMap(Map<String, Object> paramMap) {
/* 522 */     this.sysMap = paramMap;
/*     */   }
/*     */   
/*     */   public OutterEncryptClassBean getOutterEncryptClassBean() {
/* 526 */     return this.outterEncryptClassBean;
/*     */   }
/*     */   
/*     */   public void setOutterEncryptClassBean(OutterEncryptClassBean paramOutterEncryptClassBean) {
/* 530 */     this.outterEncryptClassBean = paramOutterEncryptClassBean;
/*     */   }
/*     */   
/*     */   public Integer getBaseType1() {
/* 534 */     return this.baseType1;
/*     */   }
/*     */   
/*     */   public void setBaseType1(Integer paramInteger) {
/* 538 */     this.baseType1 = paramInteger;
/*     */   }
/*     */   
/*     */   public Integer getBaseType2() {
/* 542 */     return this.baseType2;
/*     */   }
/*     */   
/*     */   public void setBaseType2(Integer paramInteger) {
/* 546 */     this.baseType2 = paramInteger;
/*     */   }
/*     */   
/*     */   public String getEncryptClassId() {
/* 550 */     return this.encryptClassId;
/*     */   }
/*     */   
/*     */   public void setEncryptClassId(String paramString) {
/* 554 */     this.encryptClassId = paramString;
/*     */   }
/*     */   
/*     */   public String getImageWidth() {
/* 558 */     return this.imageWidth;
/*     */   }
/*     */   
/*     */   public void setImageWidth(String paramString) {
/* 562 */     this.imageWidth = paramString;
/*     */   }
/*     */   
/*     */   public String getImageHeight() {
/* 566 */     return this.imageHeight;
/*     */   }
/*     */   
/*     */   public void setImageHeight(String paramString) {
/* 570 */     this.imageHeight = paramString;
/*     */   }
/*     */   
/*     */   public String getEncodeFlag() {
/* 574 */     return this.encodeFlag;
/*     */   }
/*     */   
/*     */   public void setEncodeFlag(String paramString) {
/* 578 */     this.encodeFlag = paramString;
/*     */   }
/*     */   
/*     */   public String getShowOrder() {
/* 582 */     return this.showOrder;
/*     */   }
/*     */   
/*     */   public void setShowOrder(String paramString) {
/* 586 */     this.showOrder = paramString;
/*     */   }
/*     */   
/*     */   public String getResult() {
/* 590 */     return this.result;
/*     */   }
/*     */   
/*     */   public void setResult(String paramString) {
/* 594 */     this.result = paramString;
/*     */   }
/*     */   
/*     */   public Method getMethodEncode() {
/* 598 */     return this.methodEncode;
/*     */   }
/*     */   
/*     */   public void setMethodEncode(Method paramMethod) {
/* 602 */     this.methodEncode = paramMethod;
/*     */   }
/*     */   
/*     */   public Method getMethodSetpwd() {
/* 606 */     return this.methodSetpwd;
/*     */   }
/*     */   
/*     */   public void setMethodSetpwd(Method paramMethod) {
/* 610 */     this.methodSetpwd = paramMethod;
/*     */   }
/*     */   
/*     */   public Method getMethodSetiv() {
/* 614 */     return this.methodSetiv;
/*     */   }
/*     */   
/*     */   public void setMethodSetiv(Method paramMethod) {
/* 618 */     this.methodSetiv = paramMethod;
/*     */   }
/*     */   
/*     */   public String getEmail263AuthCorpId() {
/* 622 */     return this.email263AuthCorpId;
/*     */   }
/*     */   
/*     */   public void setEmail263AuthCorpId(String paramString) {
/* 626 */     this.email263AuthCorpId = paramString;
/*     */   }
/*     */   
/*     */   public String getEmail263PartnerId() {
/* 630 */     return this.email263PartnerId;
/*     */   }
/*     */   
/*     */   public void setEmail263PartnerId(String paramString) {
/* 634 */     this.email263PartnerId = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/entrance/bean/OutterSysBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */