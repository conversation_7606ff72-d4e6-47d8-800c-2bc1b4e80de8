/*     */ package weaver.integration.util;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileNotFoundException;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.OutputStreamWriter;
/*     */ import java.io.StringReader;
/*     */ import java.io.StringWriter;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.StringTokenizer;
/*     */ import org.dom4j.Attribute;
/*     */ import org.dom4j.Document;
/*     */ import org.dom4j.DocumentException;
/*     */ import org.dom4j.Element;
/*     */ import org.dom4j.io.OutputFormat;
/*     */ import org.dom4j.io.SAXReader;
/*     */ import org.dom4j.io.XMLWriter;
/*     */ import org.xml.sax.InputSource;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.security.util.SecurityMethodUtil;
/*     */ 
/*     */ public class XMLUtil {
/*  29 */   private static Logger newlog = LoggerFactory.getLogger(XMLUtil.class);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  34 */   private static String XML_UTF8 = "UTF-8";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Document createEmptyXMLFile(String paramString) {
/*  42 */     if (paramString == null || "".equals(paramString)) {
/*  43 */       return null;
/*     */     }
/*  45 */     File file = new File(paramString);
/*  46 */     return createEmptyXMLFile(file, (String)null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Document createEmptyXMLFile(String paramString1, String paramString2) {
/*  56 */     if (paramString1 == null || "".equals(paramString1)) {
/*  57 */       return null;
/*     */     }
/*  59 */     File file = new File(paramString1);
/*  60 */     return createEmptyXMLFile(file, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Document createEmptyXMLFile(File paramFile) {
/*  69 */     return createEmptyXMLFile(paramFile, (String)null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Document createEmptyXMLFile(File paramFile, String paramString) {
/*  79 */     if (paramFile == null) {
/*  80 */       return null;
/*     */     }
/*  82 */     Document document = DocumentHelper.createDocument();
/*  83 */     XMLWriter xMLWriter = null;
/*     */     try {
/*  85 */       OutputFormat outputFormat = OutputFormat.createPrettyPrint();
/*  86 */       if (paramString == null || "".equals(paramString)) {
/*  87 */         paramString = XML_UTF8;
/*     */       }
/*  89 */       outputFormat.setEncoding(paramString);
/*  90 */       xMLWriter = new XMLWriter(new OutputStreamWriter(new FileOutputStream(paramFile), paramString), outputFormat);
/*  91 */       xMLWriter.write(document);
/*  92 */       xMLWriter.close();
/*  93 */     } catch (IOException iOException) {
/*  94 */       newlog.error("创建XML文件！", iOException);
/*  95 */       return null;
/*     */     } 
/*  97 */     return document;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Document loadXMLFile(String paramString) {
/* 106 */     if (paramString == null || "".equals(paramString)) {
/* 107 */       return null;
/*     */     }
/* 109 */     File file = new File(paramString);
/* 110 */     return loadXMLFile(file);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Document loadXMLFile(File paramFile) {
/* 119 */     Document document = null;
/* 120 */     if (paramFile.exists() && paramFile.isFile()) {
/* 121 */       SAXReader sAXReader = new SAXReader();
/* 122 */       SecurityMethodUtil.setReaderFeature(sAXReader);
/*     */       
/*     */       try {
/* 125 */         document = sAXReader.read(new FileInputStream(paramFile));
/* 126 */       } catch (DocumentException documentException) {
/* 127 */         newlog.error("加载XML文件失败，XML文件中存在特殊字符或者编码格式错误！", (Throwable)documentException);
/* 128 */         return null;
/* 129 */       } catch (FileNotFoundException fileNotFoundException) {
/* 130 */         newlog.error("加载XML文件失败，XML文件中存在特殊字符或者编码格式错误！", fileNotFoundException);
/* 131 */         return null;
/*     */       } 
/*     */     } 
/* 134 */     return document;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void saveXMLFile(Document paramDocument, String paramString) {
/* 143 */     if (paramString == null || "".equals(paramString)) {
/* 144 */       newlog.error("xmlPath can not be null.");
/* 145 */       throw new NullPointerException("xmlPath can not be null.");
/*     */     } 
/* 147 */     File file = new File(paramString);
/* 148 */     saveXMLFile(paramDocument, file, (String)null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void saveXMLFile(Document paramDocument, String paramString1, String paramString2) {
/* 158 */     if (paramString1 == null || "".equals(paramString1)) {
/* 159 */       newlog.error("xmlPath can not be null.");
/* 160 */       throw new NullPointerException("xmlPath can not be null.");
/*     */     } 
/* 162 */     File file = new File(paramString1);
/* 163 */     saveXMLFile(paramDocument, file, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void saveXMLFile(Document paramDocument, File paramFile) {
/* 172 */     saveXMLFile(paramDocument, paramFile, (String)null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void saveXMLFile(Document paramDocument, File paramFile, String paramString) {
/* 182 */     if (paramDocument == null) {
/* 183 */       newlog.error("document can not be null.");
/* 184 */       throw new NullPointerException("document can not be null.");
/*     */     } 
/* 186 */     if (paramFile.exists() && paramFile.isFile()) {
/* 187 */       XMLWriter xMLWriter = null;
/* 188 */       FileOutputStream fileOutputStream = null;
/* 189 */       OutputStreamWriter outputStreamWriter = null;
/*     */       try {
/* 191 */         OutputFormat outputFormat = OutputFormat.createPrettyPrint();
/* 192 */         if (paramString == null || "".equals(paramString)) {
/* 193 */           paramString = XML_UTF8;
/*     */         }
/* 195 */         outputFormat.setEncoding(paramString);
/* 196 */         fileOutputStream = new FileOutputStream(paramFile);
/* 197 */         outputStreamWriter = new OutputStreamWriter(fileOutputStream, paramString);
/* 198 */         xMLWriter = new XMLWriter(outputStreamWriter, outputFormat);
/* 199 */         xMLWriter.write(paramDocument);
/* 200 */       } catch (IOException iOException) {
/* 201 */         newlog.error("保存XML文件失败！", iOException);
/*     */       } finally {
/*     */         try {
/* 204 */           if (xMLWriter != null) {
/* 205 */             xMLWriter.close();
/*     */           }
/* 207 */           if (outputStreamWriter != null) {
/* 208 */             outputStreamWriter.close();
/*     */           }
/* 210 */           if (fileOutputStream != null) {
/* 211 */             fileOutputStream.close();
/*     */           }
/* 213 */         } catch (Exception exception) {
/* 214 */           newlog.error("IO异常：", exception);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Document parseXMLStr(String paramString) {
/* 226 */     Document document = null;
/*     */     try {
/* 228 */       SAXReader sAXReader = new SAXReader();
/* 229 */       SecurityMethodUtil.setReaderFeature(sAXReader);
/* 230 */       String str = getEncoding(paramString);
/* 231 */       InputSource inputSource = new InputSource(new StringReader(paramString));
/* 232 */       inputSource.setEncoding(str);
/* 233 */       document = sAXReader.read(inputSource);
/* 234 */       if (document.getXMLEncoding() == null) {
/* 235 */         document.setXMLEncoding(str);
/*     */       }
/* 237 */     } catch (DocumentException documentException) {
/* 238 */       newlog.error("解析XML字符串失败！", (Throwable)documentException);
/*     */     } 
/* 240 */     return document;
/*     */   }
/*     */   
/*     */   private static String getEncoding(String paramString) {
/* 244 */     String str1 = null;
/* 245 */     String str2 = paramString.trim();
/* 246 */     if (str2.startsWith("<?xml")) {
/* 247 */       int i = str2.indexOf("?>");
/* 248 */       String str = str2.substring(0, i);
/* 249 */       StringTokenizer stringTokenizer = new StringTokenizer(str, " =\"'");
/*     */       
/* 251 */       while (stringTokenizer.hasMoreTokens()) {
/* 252 */         String str3 = stringTokenizer.nextToken();
/* 253 */         if ("encoding".equals(str3)) {
/* 254 */           if (stringTokenizer.hasMoreTokens()) {
/* 255 */             str1 = stringTokenizer.nextToken();
/*     */           }
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     } 
/* 262 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String xmltoString(Document paramDocument) {
/* 271 */     return xmltoString(paramDocument, (String)null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String xmltoString(Document paramDocument, String paramString) {
/* 281 */     if (paramDocument == null) {
/* 282 */       return "";
/*     */     }
/* 284 */     if (paramString == null || "".equals(paramString))
/*     */     {
/* 286 */       paramString = XML_UTF8;
/*     */     }
/*     */     
/* 289 */     OutputFormat outputFormat = OutputFormat.createPrettyPrint();
/* 290 */     outputFormat.setEncoding(paramString);
/* 291 */     StringWriter stringWriter = new StringWriter();
/* 292 */     XMLWriter xMLWriter = new XMLWriter(stringWriter, outputFormat);
/*     */     try {
/* 294 */       xMLWriter.write(paramDocument);
/* 295 */       xMLWriter.close();
/* 296 */     } catch (IOException iOException) {
/* 297 */       newlog.error("XML转换成字符串失败！", iOException);
/*     */     } 
/* 299 */     return stringWriter.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String xmltoString(String paramString) {
/* 308 */     return xmltoString(paramString, XML_UTF8);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String xmltoString(String paramString1, String paramString2) {
/* 318 */     StringBuilder stringBuilder = new StringBuilder();
/*     */     
/* 320 */     File file = new File(paramString1);
/* 321 */     BufferedReader bufferedReader = null;
/*     */     try {
/* 323 */       FileInputStream fileInputStream = new FileInputStream(file);
/* 324 */       bufferedReader = new BufferedReader(new InputStreamReader(fileInputStream, paramString2));
/* 325 */       String str = null;
/*     */       
/* 327 */       while ((str = bufferedReader.readLine()) != null) {
/* 328 */         stringBuilder.append(str);
/*     */       }
/* 330 */       bufferedReader.close();
/* 331 */     } catch (IOException iOException) {
/* 332 */       newlog.error("XML转换成字符串失败！", iOException);
/*     */     } finally {
/* 334 */       if (bufferedReader != null) {
/*     */         try {
/* 336 */           bufferedReader.close();
/* 337 */         } catch (IOException iOException) {
/* 338 */           newlog.error("XML转换成字符串失败！", iOException);
/*     */         } 
/*     */       }
/*     */     } 
/*     */     
/* 343 */     return stringBuilder.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Element addRootNode(Document paramDocument, String paramString) {
/* 356 */     if (paramDocument == null) {
/* 357 */       return null;
/*     */     }
/* 359 */     if (paramString == null || "".equals(paramString)) {
/* 360 */       return null;
/*     */     }
/* 362 */     return paramDocument.addElement(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Element addNode(Element paramElement, String paramString) {
/* 372 */     if (paramElement == null) {
/* 373 */       return null;
/*     */     }
/* 375 */     if (paramString == null || "".equals(paramString)) {
/* 376 */       return null;
/*     */     }
/* 378 */     return paramElement.addElement(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Element addNode(Element paramElement, String paramString1, String paramString2) {
/* 389 */     if (paramElement == null) {
/* 390 */       return null;
/*     */     }
/* 392 */     if (paramString1 == null || "".equals(paramString1)) {
/* 393 */       return null;
/*     */     }
/* 395 */     if (paramString2 == null) {
/* 396 */       return null;
/*     */     }
/* 398 */     Element element = paramElement.addElement(paramString1);
/* 399 */     element.setText(paramString2);
/* 400 */     return element;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Element addComment(Element paramElement, String paramString) {
/* 410 */     if (paramElement == null) {
/* 411 */       return null;
/*     */     }
/* 413 */     if (paramString == null || "".equals(paramString)) {
/* 414 */       return null;
/*     */     }
/* 416 */     return paramElement.addComment(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Element addText(Element paramElement, String paramString) {
/* 426 */     if (paramElement == null) {
/* 427 */       return null;
/*     */     }
/* 429 */     if (paramString == null || "".equals(paramString)) {
/* 430 */       paramString = "";
/*     */     }
/* 432 */     paramElement.setText(paramString);
/* 433 */     return paramElement;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Element addAttribute(Element paramElement, String paramString1, String paramString2) {
/* 444 */     if (paramElement == null) {
/* 445 */       return null;
/*     */     }
/* 447 */     if (paramString1 == null || "".equals(paramString1)) {
/* 448 */       return null;
/*     */     }
/* 450 */     if (paramString2 == null) {
/* 451 */       paramString2 = "";
/*     */     }
/* 453 */     return paramElement.addAttribute(paramString1, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Element getRootNode(Document paramDocument) {
/* 462 */     if (paramDocument == null) {
/* 463 */       return null;
/*     */     }
/* 465 */     return paramDocument.getRootElement();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<Element> getNodesByName(Document paramDocument, String paramString) {
/* 476 */     if (paramDocument == null) {
/* 477 */       return null;
/*     */     }
/* 479 */     if (paramString == null || "".equals(paramString)) {
/* 480 */       return null;
/*     */     }
/* 482 */     return paramDocument.selectNodes("//" + paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<Element> getChildrenNodesByName(Element paramElement, String paramString) {
/* 493 */     if (paramElement == null) {
/* 494 */       return null;
/*     */     }
/* 496 */     if (paramString == null || "".equals(paramString)) {
/* 497 */       return null;
/*     */     }
/* 499 */     return paramElement.elements(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int getNodeSizeByName(Element paramElement, String paramString) {
/* 510 */     if (paramElement == null) {
/* 511 */       return 0;
/*     */     }
/* 513 */     if (paramString == null || "".equals(paramString)) {
/* 514 */       return 0;
/*     */     }
/* 516 */     return paramElement.elements(paramString).size();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Element getNodeByNameAndOrder(Element paramElement, String paramString, int paramInt) {
/* 527 */     if (paramElement == null) {
/* 528 */       return null;
/*     */     }
/* 530 */     if (paramString == null || "".equals(paramString)) {
/* 531 */       return null;
/*     */     }
/*     */     
/* 534 */     int i = paramElement.elements(paramString).size();
/* 535 */     newlog.error("当前节点下，名称为" + paramString + "的子节点共有" + i + "个！");
/* 536 */     if (paramInt > i - 1 || paramInt < 0) {
/* 537 */       newlog.error("order应该在0到" + (i - 1) + "之间！");
/* 538 */       return null;
/*     */     } 
/* 540 */     return paramElement.elements(paramString).get(paramInt);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Iterator<Element> getIterator(Element paramElement) {
/* 549 */     if (paramElement == null) {
/* 550 */       return null;
/*     */     }
/* 552 */     return paramElement.elementIterator();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getText(Element paramElement) {
/* 562 */     return paramElement.getText();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getNodeName(Element paramElement) {
/* 571 */     return paramElement.getName();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Iterator<Attribute> getAttributeIterator(Element paramElement) {
/* 580 */     if (paramElement == null) {
/* 581 */       return null;
/*     */     }
/* 583 */     return paramElement.attributeIterator();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<Attribute> getAttributeList(Element paramElement) {
/* 593 */     if (paramElement == null) {
/* 594 */       return null;
/*     */     }
/* 596 */     Iterator<Attribute> iterator = getAttributeIterator(paramElement);
/* 597 */     if (iterator == null) {
/* 598 */       return null;
/*     */     }
/* 600 */     ArrayList<Attribute> arrayList = new ArrayList();
/* 601 */     while (iterator.hasNext()) {
/* 602 */       Attribute attribute = iterator.next();
/* 603 */       arrayList.add(attribute);
/*     */     } 
/* 605 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getAttrName(Attribute paramAttribute) {
/* 614 */     if (paramAttribute == null) {
/* 615 */       return null;
/*     */     }
/* 617 */     return paramAttribute.getName();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getAttrValue(Attribute paramAttribute) {
/* 626 */     if (paramAttribute == null) {
/* 627 */       return null;
/*     */     }
/* 629 */     return paramAttribute.getValue();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getAttributeValue(Element paramElement, String paramString) {
/* 639 */     paramString = paramString.trim();
/* 640 */     if (paramElement == null) {
/* 641 */       return null;
/*     */     }
/* 643 */     if (paramString == null || "".equals(paramString)) {
/* 644 */       return null;
/*     */     }
/* 646 */     return paramElement.attributeValue(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Map<String, String> getAttributeMap(Element paramElement) {
/* 655 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 656 */     if (paramElement == null) {
/* 657 */       return null;
/*     */     }
/* 659 */     List<Attribute> list = getAttributeList(paramElement);
/* 660 */     if (list == null) {
/* 661 */       return null;
/*     */     }
/* 663 */     for (Attribute attribute : list) {
/* 664 */       String str = getAttributeValue(paramElement, attribute.getName());
/* 665 */       hashMap.put(attribute.getName(), str);
/*     */     } 
/* 667 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean hasChildren(Element paramElement) {
/* 676 */     if (paramElement == null) {
/* 677 */       return false;
/*     */     }
/* 679 */     return paramElement.hasContent();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean removeNodesByName(Element paramElement, String paramString) {
/* 689 */     if (paramElement == null) {
/* 690 */       return false;
/*     */     }
/* 692 */     if (paramString == null || "".equals(paramString)) {
/* 693 */       return false;
/*     */     }
/* 695 */     Iterator<Element> iterator = paramElement.elementIterator(paramString);
/* 696 */     while (iterator.hasNext()) {
/* 697 */       Element element = iterator.next();
/* 698 */       paramElement.remove(element);
/*     */     } 
/* 700 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean removeNodeByNameAndText(Element paramElement, String paramString1, String paramString2) {
/* 711 */     if (paramElement == null) {
/* 712 */       return false;
/*     */     }
/* 714 */     if (paramString1 == null || "".equals(paramString1)) {
/* 715 */       return false;
/*     */     }
/* 717 */     if (paramString2 == null) {
/* 718 */       return false;
/*     */     }
/* 720 */     Iterator<Element> iterator = paramElement.elementIterator(paramString1);
/* 721 */     while (iterator.hasNext()) {
/* 722 */       Element element = iterator.next();
/* 723 */       if (element.getText().equals(paramString2)) {
/* 724 */         paramElement.remove(element);
/*     */       }
/*     */     } 
/* 727 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean removeAttribute(Element paramElement, String paramString) {
/* 737 */     if (paramElement == null) {
/* 738 */       return false;
/*     */     }
/* 740 */     if (paramString == null || "".equals(paramString)) {
/* 741 */       return false;
/*     */     }
/* 743 */     Attribute attribute = paramElement.attribute(paramString);
/* 744 */     return paramElement.remove(attribute);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<Element> getNodesByXPath(Document paramDocument, String paramString) {
/* 757 */     if (paramDocument == null) {
/* 758 */       newlog.error("Document不能为空！");
/* 759 */       return null;
/*     */     } 
/* 761 */     List<Element> list = null;
/*     */     try {
/* 763 */       list = paramDocument.selectNodes(paramString);
/* 764 */     } catch (Exception exception) {
/* 765 */       newlog.error("xpath获取节点失败！", exception);
/*     */     } 
/* 767 */     return list;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<Element> getNodesByXPath(Element paramElement, String paramString) {
/* 777 */     if (paramElement == null) {
/* 778 */       newlog.error("Element不能为空！");
/* 779 */       return null;
/*     */     } 
/* 781 */     List<Element> list = null;
/*     */     try {
/* 783 */       list = paramElement.selectNodes(paramString);
/* 784 */     } catch (Exception exception) {
/* 785 */       newlog.error("xpath获取节点失败！", exception);
/*     */     } 
/* 787 */     return list;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Element getFirstNodeByXPath(Document paramDocument, String paramString) {
/* 797 */     if (paramDocument == null) {
/* 798 */       newlog.error("Document不能为空！");
/* 799 */       return null;
/*     */     } 
/* 801 */     Element element = null;
/*     */     try {
/* 803 */       element = (Element)paramDocument.selectSingleNode(paramString);
/* 804 */     } catch (Exception exception) {
/* 805 */       newlog.error("xpath获取节点失败！", exception);
/*     */     } 
/* 807 */     return element;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Element getFirstNodeByXPath(Element paramElement, String paramString) {
/* 817 */     if (paramElement == null) {
/* 818 */       newlog.error("Element不能为空！");
/* 819 */       return null;
/*     */     } 
/* 821 */     Element element = null;
/*     */     try {
/* 823 */       element = (Element)paramElement.selectSingleNode(paramString);
/* 824 */     } catch (Exception exception) {
/* 825 */       newlog.error("xpath获取节点失败！", exception);
/*     */     } 
/* 827 */     return element;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<Attribute> getAttributesByXPath(Document paramDocument, String paramString) {
/* 838 */     if (paramDocument == null) {
/* 839 */       newlog.error("Document不能为空！");
/* 840 */       return null;
/*     */     } 
/* 842 */     List<Attribute> list = null;
/*     */     try {
/* 844 */       list = paramDocument.selectNodes(paramString);
/* 845 */     } catch (Exception exception) {
/* 846 */       newlog.error("xpath获取属性失败！", exception);
/*     */     } 
/* 848 */     return list;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<Attribute> getAttributesByXPath(Element paramElement, String paramString) {
/* 859 */     if (paramElement == null) {
/* 860 */       newlog.error("Element不能为空！");
/* 861 */       return null;
/*     */     } 
/* 863 */     List<Attribute> list = null;
/*     */     try {
/* 865 */       list = paramElement.selectNodes(paramString);
/* 866 */     } catch (Exception exception) {
/* 867 */       newlog.error("xpath获取属性失败！", exception);
/*     */     } 
/* 869 */     return list;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/util/XMLUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */