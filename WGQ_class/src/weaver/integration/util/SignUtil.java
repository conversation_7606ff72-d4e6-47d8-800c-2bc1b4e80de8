/*     */ package weaver.integration.util;
/*     */ 
/*     */ import java.io.UnsupportedEncodingException;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collections;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.codec.digest.DigestUtils;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SignUtil
/*     */ {
/*     */   private static final String CHARSET = "UTF-8";
/*     */   
/*     */   public static String sign(String paramString, Map<String, String> paramMap) {
/*  20 */     Map<String, String> map = filterParameters(paramMap);
/*  21 */     String str = linkParameters(map);
/*  22 */     System.out.println(str);
/*  23 */     return MD5.sign(str, paramString, "UTF-8");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static Map<String, String> filterParameters(Map<String, String> paramMap) {
/*  34 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  35 */     if (paramMap == null || paramMap.size() <= 0) {
/*  36 */       return (Map)hashMap;
/*     */     }
/*  38 */     for (String str1 : paramMap.keySet()) {
/*  39 */       String str2 = paramMap.get(str1);
/*  40 */       if (str2 == null || "".equals(str2) || "sign"
/*  41 */         .equalsIgnoreCase(str1)) {
/*     */         continue;
/*     */       }
/*  44 */       hashMap.put(str1, str2);
/*     */     } 
/*  46 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String linkParameters(Map<String, String> paramMap) {
/*  55 */     ArrayList<Comparable> arrayList = new ArrayList(paramMap.keySet());
/*     */     
/*  57 */     Collections.sort(arrayList);
/*  58 */     String str = "";
/*  59 */     int i = arrayList.size();
/*  60 */     for (byte b = 0; b < i; b++) {
/*  61 */       String str1 = (String)arrayList.get(b);
/*  62 */       String str2 = paramMap.get(str1);
/*  63 */       if (b == arrayList.size() - 1) {
/*  64 */         str = str + str1 + "=" + str2;
/*     */       } else {
/*  66 */         str = str + str1 + "=" + str2 + "&";
/*     */       } 
/*     */     } 
/*  69 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static class MD5
/*     */   {
/*     */     public static String sign(String param1String1, String param1String2, String param1String3) {
/*  81 */       param1String1 = param1String1 + param1String2;
/*     */       
/*     */       try {
/*  84 */         return DigestUtils.md5Hex(getBytes(param1String1, param1String3));
/*  85 */       } catch (UnsupportedEncodingException unsupportedEncodingException) {
/*  86 */         unsupportedEncodingException.printStackTrace();
/*     */         
/*  88 */         return null;
/*     */       } 
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     private static byte[] getBytes(String param1String1, String param1String2) throws UnsupportedEncodingException {
/* 100 */       if (param1String2 == null || "".equals(param1String2)) {
/* 101 */         return param1String1.getBytes();
/*     */       }
/* 103 */       return param1String1.getBytes(param1String2);
/*     */     }
/*     */   }
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/* 108 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 109 */     hashMap.put("account_name", "1");
/* 110 */     hashMap.put("addr_right", "2");
/* 111 */     hashMap.put("addr_visible", "3");
/* 112 */     hashMap.put("domain", "4");
/* 113 */     hashMap.put("gender", "5");
/* 114 */     hashMap.put("mobile", "6");
/* 115 */     hashMap.put("nickname", "7");
/* 116 */     hashMap.put("pass_type", "8");
/* 117 */     hashMap.put("passchange_req", "9");
/* 118 */     hashMap.put("password", "10");
/* 119 */     hashMap.put("product", "11");
/* 120 */     hashMap.put("tel", "12");
/* 121 */     hashMap.put("time", "13");
/* 122 */     hashMap.put("unit_id", "14");
/*     */ 
/*     */     
/* 125 */     String str = linkParameters((Map)hashMap);
/* 126 */     System.out.println(str);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/util/SignUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */