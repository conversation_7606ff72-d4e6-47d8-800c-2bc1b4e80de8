/*    */ package weaver.integration.util;
/*    */ 
/*    */ import java.util.Map;
/*    */ import java.util.concurrent.ConcurrentHashMap;
/*    */ import weaver.general.TimeUtil;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SystemVariableUtils
/*    */ {
/*    */   public static Map<String, Object> getTimestampVariables() {
/* 14 */     ConcurrentHashMap<Object, Object> concurrentHashMap = new ConcurrentHashMap<>();
/* 15 */     concurrentHashMap.put("timestamp", Long.valueOf(System.currentTimeMillis()));
/* 16 */     concurrentHashMap.put("para_timestamp", Long.valueOf(System.currentTimeMillis()));
/* 17 */     concurrentHashMap.put("date", TimeUtil.getCurrentDateString());
/* 18 */     concurrentHashMap.put("time", TimeUtil.getOnlyCurrentTimeString());
/* 19 */     concurrentHashMap.put("short_timestamp", Long.valueOf(System.currentTimeMillis() / 1000L));
/*    */     
/* 21 */     return (Map)concurrentHashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/util/SystemVariableUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */