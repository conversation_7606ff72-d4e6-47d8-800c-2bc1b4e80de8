/*    */ package weaver.integration.util;
/*    */ 
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ import weaver.interfaces.cache.impl.IntegrationCache4DataSource;
/*    */ import weaver.interfaces.datasource.DataSource;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DataSourceUtil
/*    */ {
/*    */   public static DataSource getDataSource(String paramString) {
/* 20 */     if (paramString.startsWith("datasource.")) {
/* 21 */       paramString = paramString.replace("datasource.", "");
/*    */     }
/*    */     
/* 24 */     IntegrationCache4DataSource integrationCache4DataSource = new IntegrationCache4DataSource();
/* 25 */     Object object = integrationCache4DataSource.getCacheByKey(paramString);
/* 26 */     if (object != null) {
/* 27 */       return (DataSource)object;
/*    */     }
/*    */     
/* 30 */     return (DataSource)integrationCache4DataSource.getObjectFromDB(paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/* 35 */   private static Logger log = LoggerFactory.getLogger(DataSourceUtil.class);
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/util/DataSourceUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */