/*     */ package weaver.integration.util;
/*     */ 
/*     */ import java.io.BufferedInputStream;
/*     */ import java.io.BufferedOutputStream;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileNotFoundException;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.net.SocketException;
/*     */ import java.util.Arrays;
/*     */ import java.util.List;
/*     */ import java.util.StringTokenizer;
/*     */ import org.apache.commons.net.ftp.FTPClient;
/*     */ import org.apache.commons.net.ftp.FTPFile;
/*     */ import org.apache.commons.net.ftp.FTPReply;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FTPUtil
/*     */ {
/*  37 */   private static Logger newlog = LoggerFactory.getLogger(FTPUtil.class);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static final String FTP_UTF8 = "UTF-8";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static final String FTP_GBK = "GBK";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static final String FTP_ISO_8859_1 = "ISO-8859-1";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String server;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private int port;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String userName;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String userPassword;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String charset;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  82 */   private FTPClient ftpClient = null;
/*     */   
/*     */   public FTPClient getFtpClient() {
/*  85 */     return this.ftpClient;
/*     */   }
/*     */   private boolean is_connected;
/*     */   private void setFtpClient(FTPClient paramFTPClient) {
/*  89 */     this.ftpClient = paramFTPClient;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public FTPUtil(String paramString1, int paramInt, String paramString2, String paramString3) {
/* 105 */     this(paramString1, paramInt, paramString2, paramString3, "GBK");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public FTPUtil(String paramString1, int paramInt, String paramString2, String paramString3, String paramString4) {
/* 117 */     this.server = paramString1;
/* 118 */     this.port = paramInt;
/* 119 */     this.userName = paramString2;
/* 120 */     this.userPassword = paramString3;
/* 121 */     this.charset = paramString4;
/* 122 */     this.is_connected = false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean login() {
/* 130 */     boolean bool = false;
/*     */     try {
/* 132 */       this.ftpClient = new FTPClient();
/* 133 */       this.ftpClient.connect(this.server, this.port);
/* 134 */       this.ftpClient.login(this.userName, this.userPassword);
/* 135 */       this.ftpClient.setFileType(2);
/* 136 */       this.ftpClient.setControlEncoding(this.charset);
/* 137 */       this.ftpClient.setConnectTimeout(5000);
/* 138 */       this.ftpClient.setDataTimeout(120000);
/*     */       
/* 140 */       int i = this.ftpClient.getReplyCode();
/* 141 */       if (!FTPReply.isPositiveCompletion(i)) {
/* 142 */         this.ftpClient.disconnect();
/* 143 */         this.is_connected = false;
/* 144 */         return bool;
/*     */       } 
/* 146 */       this.is_connected = true;
/* 147 */       bool = true;
/* 148 */     } catch (SocketException socketException) {
/* 149 */       this.is_connected = false;
/* 150 */       bool = false;
/* 151 */       newlog.error("FTP服务器连接超时，请检查FTP服务器地址及端口配置是否正确：FTPServer=" + this.server + "，Port=" + this.port, socketException);
/* 152 */     } catch (IOException iOException) {
/* 153 */       this.is_connected = false;
/* 154 */       bool = false;
/* 155 */       newlog.error("FTP服务器连接超时，请检查FTP服务器地址及端口配置是否正确：FTPServer=" + this.server + "，Port=" + this.port, iOException);
/*     */     } 
/*     */     
/* 158 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean logout() {
/*     */     try {
/* 167 */       if (this.ftpClient != null) {
/* 168 */         this.ftpClient.logout();
/* 169 */         this.ftpClient.disconnect();
/* 170 */         this.is_connected = false;
/*     */       } 
/* 172 */     } catch (IOException iOException) {
/* 173 */       newlog.error("关闭FTP服务失败！", iOException);
/* 174 */       return false;
/*     */     } 
/* 176 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isConnected() {
/* 184 */     return this.is_connected;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isDirExist(String paramString) {
/* 193 */     if (!this.is_connected) {
/* 194 */       return false;
/*     */     }
/*     */     
/*     */     try {
/* 198 */       String str = new String(paramString.getBytes(this.charset), "ISO-8859-1");
/* 199 */       return this.ftpClient.changeWorkingDirectory(str);
/* 200 */     } catch (IOException iOException) {
/* 201 */       newlog.error("检查目录 " + paramString + " 是否存在失败！", iOException);
/* 202 */       return false;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean createDir(String paramString) {
/* 212 */     if (!this.is_connected) {
/* 213 */       return false;
/*     */     }
/*     */     
/*     */     try {
/* 217 */       String str = new String(paramString.getBytes(this.charset), "ISO-8859-1");
/* 218 */       return this.ftpClient.makeDirectory(str);
/* 219 */     } catch (Exception exception) {
/* 220 */       newlog.error("创建目录 " + paramString + " 失败！", exception);
/* 221 */       return false;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int createDirs(String paramString) {
/* 231 */     if (!this.is_connected) {
/* 232 */       return -1;
/*     */     }
/*     */     
/*     */     try {
/* 236 */       String str1 = new String(paramString.getBytes(this.charset), "ISO-8859-1");
/*     */       
/* 238 */       StringTokenizer stringTokenizer = new StringTokenizer(str1, "/");
/* 239 */       stringTokenizer.countTokens();
/* 240 */       String str2 = "";
/* 241 */       while (stringTokenizer.hasMoreElements()) {
/* 242 */         str2 = str2 + "/" + (String)stringTokenizer.nextElement();
/* 243 */         this.ftpClient.mkd(str2);
/*     */       } 
/* 245 */       return 1;
/* 246 */     } catch (Exception exception) {
/* 247 */       newlog.error("创建目录 " + paramString + " 失败！", exception);
/* 248 */       return -1;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWorkingDirectory() {
/* 257 */     if (!this.is_connected) {
/* 258 */       return "";
/*     */     }
/*     */     try {
/* 261 */       String str = this.ftpClient.printWorkingDirectory();
/* 262 */       return new String(str.getBytes("ISO-8859-1"), this.charset);
/* 263 */     } catch (IOException iOException) {
/* 264 */       newlog.error("获取当前目录失败！", iOException);
/* 265 */       return "";
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean changeWorkingDirectory(String paramString) {
/* 275 */     if (!this.is_connected) {
/* 276 */       return false;
/*     */     }
/*     */     
/*     */     try {
/* 280 */       String str = new String(paramString.getBytes(this.charset), "ISO-8859-1");
/* 281 */       return this.ftpClient.changeWorkingDirectory(str);
/* 282 */     } catch (IOException iOException) {
/* 283 */       newlog.error("进入 " + paramString + " 目录失败！", iOException);
/* 284 */       return false;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean changeToParentDirectory() {
/* 293 */     if (!this.is_connected) {
/* 294 */       return false;
/*     */     }
/*     */     try {
/* 297 */       return this.ftpClient.changeToParentDirectory();
/* 298 */     } catch (IOException iOException) {
/* 299 */       newlog.error("返回到上一层目录失败！", iOException);
/* 300 */       return false;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<FTPFile> getFileList(String paramString) {
/* 310 */     List<FTPFile> list = null;
/*     */     try {
/* 312 */       this.ftpClient.enterLocalPassiveMode();
/*     */       
/* 314 */       String str = new String(paramString.getBytes(this.charset), "ISO-8859-1");
/* 315 */       this.ftpClient.changeWorkingDirectory(str);
/* 316 */       list = Arrays.asList(this.ftpClient.listFiles());
/* 317 */     } catch (IOException iOException) {
/* 318 */       newlog.error("获取FTP服务器上 " + paramString + " 下的文件列表失败！", iOException);
/*     */     } 
/* 320 */     return list;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<FTPFile> getFileList() {
/* 328 */     List<FTPFile> list = null;
/*     */     try {
/* 330 */       this.ftpClient.enterLocalPassiveMode();
/* 331 */       list = Arrays.asList(this.ftpClient.listFiles());
/* 332 */     } catch (IOException iOException) {
/* 333 */       newlog.error("获取FTP服务器当前工作路径下的文件列表失败！", iOException);
/*     */     } 
/* 335 */     return list;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean removeDir(String paramString) {
/* 344 */     if (!this.is_connected) {
/* 345 */       return false;
/*     */     }
/*     */     
/*     */     try {
/* 349 */       String str = new String(paramString.getBytes(this.charset), "ISO-8859-1");
/* 350 */       return this.ftpClient.removeDirectory(str);
/* 351 */     } catch (IOException iOException) {
/* 352 */       newlog.error("删除FTP上的目录 " + paramString + " 失败！", iOException);
/* 353 */       return false;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean removeFile(String paramString) {
/* 363 */     if (!this.is_connected) {
/* 364 */       return false;
/*     */     }
/*     */     
/*     */     try {
/* 368 */       String str = new String(paramString.getBytes(this.charset), "ISO-8859-1");
/* 369 */       return this.ftpClient.deleteFile(str);
/* 370 */     } catch (IOException iOException) {
/* 371 */       newlog.error("删除FTP上的文件 " + paramString + " 失败！", iOException);
/* 372 */       return false;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean removeDirAndFile(String paramString) {
/* 382 */     boolean bool = true;
/*     */     
/* 384 */     if (!paramString.endsWith("/")) {
/* 385 */       paramString = paramString + "/";
/*     */     }
/*     */     
/* 388 */     List<FTPFile> list = getFileList(paramString);
/* 389 */     if (list == null || list.size() == 0) {
/* 390 */       return removeDir(paramString);
/*     */     }
/*     */     
/* 393 */     for (FTPFile fTPFile : list) {
/* 394 */       String str = fTPFile.getName();
/* 395 */       if (".".equals(str) || "..".equals(str)) {
/*     */         continue;
/*     */       }
/* 398 */       if (fTPFile.isDirectory()) {
/* 399 */         bool = removeDirAndFile(paramString + str + "/");
/* 400 */         if (!bool)
/*     */           break; 
/*     */         continue;
/*     */       } 
/* 404 */       bool = removeFile(paramString + str);
/* 405 */       if (!bool) {
/*     */         break;
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 411 */     if (!bool) {
/* 412 */       return false;
/*     */     }
/* 414 */     bool = removeDir(paramString);
/* 415 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean uploadFile(String paramString1, String paramString2) {
/* 425 */     return uploadFile(paramString1, (String)null, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean uploadFile(String paramString1, String paramString2, String paramString3) {
/* 436 */     boolean bool = false;
/* 437 */     BufferedInputStream bufferedInputStream = null;
/*     */     
/*     */     try {
/* 440 */       File file = new File(paramString1);
/* 441 */       if (!file.exists()) {
/* 442 */         newlog.error("本地文件 " + paramString1 + " 不存在，请检查！");
/* 443 */         return false;
/*     */       } 
/*     */ 
/*     */       
/* 447 */       if (!isDirExist(paramString3)) {
/* 448 */         if (createDirs(paramString3) != 1) {
/* 449 */           newlog.error("在FTP上创建目录 " + paramString3 + " 失败！");
/* 450 */           return false;
/*     */         } 
/* 452 */         changeWorkingDirectory(paramString3);
/*     */       } 
/*     */ 
/*     */       
/* 456 */       if (paramString2 == null || "".equals(paramString2)) {
/* 457 */         paramString2 = file.getName();
/*     */       }
/* 459 */       bufferedInputStream = new BufferedInputStream(new FileInputStream(file));
/* 460 */       newlog.error(paramString2 + "开始上传......");
/* 461 */       this.ftpClient.enterLocalPassiveMode();
/* 462 */       bool = this.ftpClient.storeFile(new String(paramString2.getBytes(this.charset), "ISO-8859-1"), bufferedInputStream);
/* 463 */       if (!bool) {
/* 464 */         this.ftpClient.enterLocalPassiveMode();
/* 465 */         bool = this.ftpClient.storeFile(new String(paramString2.getBytes(this.charset), "ISO-8859-1"), bufferedInputStream);
/*     */       } 
/* 467 */       if (bool) {
/* 468 */         newlog.error("文件上传成功！");
/*     */       } else {
/* 470 */         newlog.error("文件上传失败！");
/*     */       } 
/* 472 */       bufferedInputStream.close();
/* 473 */     } catch (FileNotFoundException fileNotFoundException) {
/* 474 */       bool = false;
/* 475 */       newlog.error("文件上传失败！", fileNotFoundException);
/* 476 */     } catch (IOException iOException) {
/* 477 */       bool = false;
/* 478 */       newlog.error("文件上传失败！", iOException);
/*     */     } 
/*     */     
/* 481 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean uploadFile(File paramFile, String paramString) {
/* 491 */     return uploadFile(paramFile, (String)null, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean uploadFile(File paramFile, String paramString1, String paramString2) {
/* 502 */     boolean bool = false;
/* 503 */     BufferedInputStream bufferedInputStream = null;
/*     */ 
/*     */     
/*     */     try {
/* 507 */       if (!isDirExist(paramString2)) {
/* 508 */         if (createDirs(paramString2) != 1) {
/* 509 */           newlog.error("在FTP上创建目录 " + paramString2 + " 失败！");
/* 510 */           return false;
/*     */         } 
/* 512 */         changeWorkingDirectory(paramString2);
/*     */       } 
/*     */ 
/*     */       
/* 516 */       if (paramString1 == null || "".equals(paramString1)) {
/* 517 */         paramString1 = paramFile.getName();
/*     */       }
/* 519 */       bufferedInputStream = new BufferedInputStream(new FileInputStream(paramFile));
/* 520 */       newlog.error(paramString1 + "开始上传......");
/* 521 */       this.ftpClient.enterLocalPassiveMode();
/* 522 */       bool = this.ftpClient.storeFile(new String(paramString1.getBytes(this.charset), "ISO-8859-1"), bufferedInputStream);
/* 523 */       if (!bool) {
/* 524 */         this.ftpClient.enterLocalPassiveMode();
/* 525 */         bool = this.ftpClient.storeFile(new String(paramString1.getBytes(this.charset), "ISO-8859-1"), bufferedInputStream);
/*     */       } 
/* 527 */       if (bool) {
/* 528 */         newlog.error("文件上传成功！");
/*     */       } else {
/* 530 */         newlog.error("文件上传失败！");
/*     */       } 
/* 532 */       bufferedInputStream.close();
/* 533 */     } catch (FileNotFoundException fileNotFoundException) {
/* 534 */       bool = false;
/* 535 */       newlog.error("文件上传失败！", fileNotFoundException);
/* 536 */     } catch (IOException iOException) {
/* 537 */       bool = false;
/* 538 */       newlog.error("文件上传失败！", iOException);
/*     */     } 
/*     */     
/* 541 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean uploadFile(InputStream paramInputStream, String paramString1, String paramString2) {
/* 552 */     boolean bool = false;
/*     */ 
/*     */     
/*     */     try {
/* 556 */       if (!isDirExist(paramString2)) {
/* 557 */         if (createDirs(paramString2) != 1) {
/* 558 */           newlog.error("在FTP上创建目录 " + paramString2 + " 失败！");
/* 559 */           return false;
/*     */         } 
/* 561 */         changeWorkingDirectory(paramString2);
/*     */       } 
/*     */ 
/*     */       
/* 565 */       newlog.error(paramString1 + "开始上传......");
/* 566 */       this.ftpClient.enterLocalPassiveMode();
/*     */       
/* 568 */       bool = this.ftpClient.storeFile(new String(paramString1.getBytes(this.charset), "ISO-8859-1"), paramInputStream);
/* 569 */       if (!bool) {
/* 570 */         this.ftpClient.enterLocalPassiveMode();
/* 571 */         bool = this.ftpClient.storeFile(new String(paramString1.getBytes(this.charset), "ISO-8859-1"), paramInputStream);
/*     */       } 
/* 573 */       if (bool) {
/* 574 */         newlog.error("文件上传成功！");
/*     */       } else {
/* 576 */         newlog.error("文件上传失败！");
/*     */       } 
/* 578 */       paramInputStream.close();
/* 579 */     } catch (FileNotFoundException fileNotFoundException) {
/* 580 */       bool = false;
/* 581 */       newlog.error("文件上传失败！", fileNotFoundException);
/* 582 */     } catch (IOException iOException) {
/* 583 */       bool = false;
/* 584 */       newlog.error("文件上传失败！", iOException);
/*     */     } 
/*     */     
/* 587 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean downloadFile(String paramString1, String paramString2, String paramString3) {
/* 598 */     boolean bool = false;
/* 599 */     BufferedOutputStream bufferedOutputStream = null;
/*     */     
/*     */     try {
/* 602 */       this.ftpClient.enterLocalPassiveMode();
/*     */       
/* 604 */       File file = new File(paramString3);
/* 605 */       if (!file.exists() && 
/* 606 */         !file.mkdirs()) {
/* 607 */         newlog.error("本地路径 " + paramString3 + " 创建失败！");
/* 608 */         return false;
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 613 */       if (!changeWorkingDirectory(paramString1)) {
/* 614 */         newlog.error("FTP服务器 " + paramString1 + " 目录不存在，请检查！");
/* 615 */         return false;
/*     */       } 
/*     */ 
/*     */       
/* 619 */       List<FTPFile> list = getFileList();
/* 620 */       if (list == null || list.size() == 0) {
/* 621 */         newlog.error("FTP服务器当前路径下不存在文件！");
/* 622 */         return bool;
/*     */       } 
/*     */       
/* 625 */       for (FTPFile fTPFile : list) {
/* 626 */         if (fTPFile.getName().equals(paramString2)) {
/* 627 */           newlog.error(paramString2 + "开始下载......");
/* 628 */           File file1 = new File(paramString3 + File.separator + fTPFile.getName());
/* 629 */           bufferedOutputStream = new BufferedOutputStream(new FileOutputStream(file1));
/* 630 */           bool = this.ftpClient.retrieveFile(new String(fTPFile.getName().getBytes(this.charset), "ISO-8859-1"), bufferedOutputStream);
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/* 635 */       if (bool) {
/* 636 */         newlog.error("文件下载成功！");
/*     */       } else {
/* 638 */         newlog.error("文件下载失败！");
/*     */       } 
/* 640 */       bufferedOutputStream.close();
/* 641 */     } catch (FileNotFoundException fileNotFoundException) {
/* 642 */       newlog.error("文件下载失败！");
/* 643 */       bool = false;
/* 644 */     } catch (IOException iOException) {
/* 645 */       newlog.error("文件下载失败！");
/* 646 */       bool = false;
/*     */     } 
/*     */     
/* 649 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean uploadDir(String paramString1, String paramString2) {
/* 659 */     File file = new File(paramString1);
/* 660 */     if (!file.exists()) {
/* 661 */       newlog.error("本地目录 " + paramString1 + " 不存在，请检查！");
/* 662 */       return false;
/*     */     } 
/*     */ 
/*     */     
/* 666 */     if (!isDirExist(paramString2)) {
/* 667 */       if (createDirs(paramString2) != 1) {
/* 668 */         newlog.error("在FTP上创建目录 " + paramString2 + " 失败！");
/* 669 */         return false;
/*     */       } 
/* 671 */       changeWorkingDirectory(paramString2);
/*     */     } 
/*     */ 
/*     */     
/* 675 */     if (!paramString2.endsWith("/")) {
/* 676 */       paramString2 = paramString2 + "/";
/*     */     }
/*     */     
/*     */     try {
/* 680 */       changeWorkingDirectory(paramString2);
/* 681 */       createDir(file.getName());
/* 682 */       paramString2 = paramString2 + file.getName() + "/";
/*     */       
/* 684 */       File[] arrayOfFile = file.listFiles();
/* 685 */       for (File file1 : arrayOfFile) {
/* 686 */         if (file1.isDirectory()) {
/*     */           
/* 688 */           uploadDir(file1.getAbsolutePath().toString(), paramString2);
/*     */         } else {
/* 690 */           uploadFile(file1.getAbsolutePath().toString(), paramString2);
/*     */         } 
/*     */       } 
/* 693 */     } catch (Exception exception) {
/* 694 */       newlog.error("上传文件夹失败！", exception);
/* 695 */       return false;
/*     */     } 
/*     */     
/* 698 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean downloadDir(String paramString1, String paramString2) {
/* 709 */     if (!isDirExist(paramString2)) {
/* 710 */       newlog.error("FTP服务器上的目录 " + paramString2 + " 不存在，请检查！");
/* 711 */       return false;
/*     */     } 
/*     */     
/* 714 */     if (!paramString2.endsWith("/")) {
/* 715 */       paramString2 = paramString2 + "/";
/*     */     }
/*     */     
/*     */     try {
/* 719 */       paramString1 = paramString1 + File.separator + (new File(paramString2)).getName();
/* 720 */       (new File(paramString1)).mkdirs();
/*     */       
/* 722 */       List<FTPFile> list = getFileList(paramString2);
/* 723 */       for (FTPFile fTPFile : list) {
/* 724 */         String str = fTPFile.getName();
/* 725 */         if (".".equals(str) || "..".equals(str)) {
/*     */           continue;
/*     */         }
/* 728 */         if (fTPFile.isDirectory()) {
/*     */           
/* 730 */           downloadDir(paramString1, paramString2 + str + "/"); continue;
/*     */         } 
/* 732 */         downloadFile(paramString2, str, paramString1);
/*     */       }
/*     */     
/* 735 */     } catch (Exception exception) {
/* 736 */       newlog.error("下载文件夹失败！", exception);
/* 737 */       return false;
/*     */     } 
/*     */     
/* 740 */     return true;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/util/FTPUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */