/*    */ package weaver.integration.util;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ConstantsMethodUtil
/*    */ {
/* 12 */   public static String address_sql = "select oaaddress from SystemSet";
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static String getOaAddress() {
/* 19 */     String str = "";
/*    */     
/* 21 */     RecordSet recordSet = new RecordSet();
/* 22 */     recordSet.executeSql(address_sql);
/* 23 */     if (recordSet.next()) {
/* 24 */       str = Util.null2String(recordSet.getString(1));
/*    */     }
/* 26 */     if (str.equals("")) {
/* 27 */       str = "http://127.0.0.1";
/*    */     }
/* 29 */     if (!"".equals(str) && str.endsWith("/")) {
/* 30 */       str = str.substring(0, str.length() - 1);
/*    */     }
/* 32 */     return str;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/util/ConstantsMethodUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */