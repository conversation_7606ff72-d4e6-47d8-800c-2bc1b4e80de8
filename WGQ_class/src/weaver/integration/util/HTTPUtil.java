/*     */ package weaver.integration.util;
/*     */ 
/*     */ import java.io.BufferedReader;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileNotFoundException;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.InputStreamReader;
/*     */ import java.io.OutputStreamWriter;
/*     */ import java.net.MalformedURLException;
/*     */ import java.net.URL;
/*     */ import java.net.URLConnection;
/*     */ import java.util.Iterator;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.httpclient.HttpClient;
/*     */ import org.apache.commons.httpclient.HttpMethod;
/*     */ import org.apache.commons.httpclient.methods.GetMethod;
/*     */ import org.apache.commons.httpclient.methods.InputStreamRequestEntity;
/*     */ import org.apache.commons.httpclient.methods.PostMethod;
/*     */ import org.apache.commons.httpclient.methods.RequestEntity;
/*     */ import org.apache.commons.io.IOUtils;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.general.FWHttpConnectionManager;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HTTPUtil
/*     */ {
/*  45 */   private static Logger newlog = LoggerFactory.getLogger(HTTPUtil.class);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  50 */   private static String HTTP_UTF8 = "UTF-8";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String doGet(String paramString) {
/*  58 */     return doGet(paramString, null, null, 0, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String doGet(String paramString1, String paramString2) {
/*  68 */     return doGet(paramString1, null, null, 0, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String doGet(String paramString, Map paramMap) {
/*  78 */     return doGet(paramString, paramMap, null, 0, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String doGet(String paramString1, Map paramMap, String paramString2) {
/*  89 */     return doGet(paramString1, paramMap, null, 0, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String doGet(String paramString1, String paramString2, int paramInt) {
/* 100 */     return doGet(paramString1, null, paramString2, paramInt, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String doGet(String paramString1, Map paramMap, String paramString2, int paramInt, String paramString3) {
/* 114 */     String str = null;
/*     */     
/* 116 */     HttpClient httpClient = FWHttpConnectionManager.getHttpClient();
/* 117 */     newlog.error("GET请求地址=" + paramString1);
/* 118 */     GetMethod getMethod = new GetMethod(paramString1);
/*     */ 
/*     */     
/* 121 */     if (paramMap != null) {
/* 122 */       Iterator<Map.Entry> iterator = paramMap.entrySet().iterator();
/* 123 */       while (iterator.hasNext()) {
/* 124 */         Map.Entry entry = iterator.next();
/* 125 */         String str1 = (String)entry.getKey();
/* 126 */         String str2 = (String)entry.getValue();
/* 127 */         newlog.error("头部请求信息：headerName=" + str1 + "，headerValue=" + str2);
/* 128 */         getMethod.addRequestHeader(str1, str2);
/*     */       } 
/*     */     } 
/*     */     
/* 132 */     if (StringUtils.isNotBlank(paramString2)) {
/* 133 */       newlog.error("代理服务器地址=" + paramString2 + "，代理服务器端口=" + paramInt);
/* 134 */       httpClient.getHostConfiguration().setProxy(paramString2, paramInt);
/*     */     } 
/*     */     
/* 137 */     if (paramString3 == null || "".equals(paramString3)) {
/* 138 */       paramString3 = HTTP_UTF8;
/*     */     }
/*     */     
/* 141 */     httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(10000);
/* 142 */     httpClient.getHttpConnectionManager().getParams().setSoTimeout(10000);
/*     */     
/* 144 */     InputStream inputStream = null;
/*     */     try {
/* 146 */       int i = httpClient.executeMethod((HttpMethod)getMethod);
/* 147 */       newlog.error("status=" + i);
/* 148 */       if (i == 200) {
/* 149 */         inputStream = getMethod.getResponseBodyAsStream();
/*     */         
/* 151 */         str = IOUtils.toString(inputStream, paramString3);
/*     */       } else {
/* 153 */         newlog.error("Method failed : " + getMethod.getStatusLine());
/*     */       } 
/* 155 */     } catch (IOException iOException) {
/* 156 */       newlog.error("GET请求异常：", iOException);
/*     */     } finally {
/* 158 */       IOUtils.closeQuietly(inputStream);
/* 159 */       getMethod.releaseConnection();
/*     */     } 
/*     */     
/* 162 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String doPost(String paramString, Map paramMap) {
/* 174 */     return doPost(paramString, null, paramMap, null, null, 0, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String doPost(String paramString1, Map paramMap, String paramString2) {
/* 186 */     return doPost(paramString1, null, paramMap, paramString2, null, 0, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String doPost(String paramString1, Map paramMap, String paramString2, String paramString3) {
/* 199 */     return doPost(paramString1, null, paramMap, paramString2, null, 0, paramString3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String doPost(String paramString1, Map paramMap1, Map paramMap2, String paramString2) {
/* 212 */     return doPost(paramString1, paramMap1, paramMap2, paramString2, null, 0, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String doPost(String paramString1, Map paramMap1, Map paramMap2, String paramString2, String paramString3) {
/* 226 */     return doPost(paramString1, paramMap1, paramMap2, paramString2, null, 0, paramString3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String doPost(String paramString1, Map paramMap, String paramString2, String paramString3, int paramInt) {
/* 240 */     return doPost(paramString1, null, paramMap, paramString2, paramString3, paramInt, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String doPost(String paramString1, Map paramMap, String paramString2, String paramString3, int paramInt, String paramString4) {
/* 255 */     return doPost(paramString1, null, paramMap, paramString2, paramString3, paramInt, paramString4);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String doPost(String paramString1, Map paramMap1, Map paramMap2, String paramString2, String paramString3, int paramInt, String paramString4) {
/* 272 */     String str = null;
/*     */     
/* 274 */     HttpClient httpClient = FWHttpConnectionManager.getHttpClient();
/* 275 */     newlog.error("POST请求地址=" + paramString1);
/* 276 */     PostMethod postMethod = new PostMethod(paramString1);
/*     */     
/* 278 */     if (StringUtils.isNotBlank(paramString2)) {
/* 279 */       postMethod.getParams().setContentCharset(paramString2);
/* 280 */       postMethod.getParams().setHttpElementCharset(paramString2);
/*     */     } 
/*     */ 
/*     */     
/* 284 */     if (paramMap1 != null) {
/* 285 */       Iterator<Map.Entry> iterator1 = paramMap1.entrySet().iterator();
/* 286 */       while (iterator1.hasNext()) {
/* 287 */         Map.Entry entry = iterator1.next();
/* 288 */         String str1 = (String)entry.getKey();
/* 289 */         String str2 = (String)entry.getValue();
/* 290 */         newlog.error("头部请求信息：headerName=" + str1 + "，headerValue=" + str2);
/* 291 */         postMethod.addRequestHeader(str1, str2);
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 296 */     Iterator<String> iterator = paramMap2.keySet().iterator();
/* 297 */     while (iterator.hasNext()) {
/* 298 */       String str1 = iterator.next();
/* 299 */       String str2 = (String)paramMap2.get(str1);
/* 300 */       postMethod.addParameter(str1, str2);
/*     */     } 
/*     */     
/* 303 */     if (StringUtils.isNotBlank(paramString3)) {
/* 304 */       newlog.error("代理服务器地址=" + paramString3 + "，代理服务器端口=" + paramInt);
/* 305 */       httpClient.getHostConfiguration().setProxy(paramString3, paramInt);
/*     */     } 
/*     */     
/* 308 */     if (paramString4 == null || "".equals(paramString4)) {
/* 309 */       paramString4 = HTTP_UTF8;
/*     */     }
/*     */     
/* 312 */     httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(10000);
/* 313 */     httpClient.getHttpConnectionManager().getParams().setSoTimeout(10000);
/*     */     
/* 315 */     InputStream inputStream = null;
/*     */     try {
/* 317 */       int i = httpClient.executeMethod((HttpMethod)postMethod);
/* 318 */       newlog.error("status=" + i);
/* 319 */       if (i == 200) {
/* 320 */         inputStream = postMethod.getResponseBodyAsStream();
/*     */         
/* 322 */         str = IOUtils.toString(inputStream, paramString4);
/*     */       } else {
/* 324 */         newlog.error("Method failed : " + postMethod.getStatusLine());
/*     */       } 
/* 326 */     } catch (IOException iOException) {
/* 327 */       newlog.error("POST请求异常：", iOException);
/*     */     } finally {
/* 329 */       IOUtils.closeQuietly(inputStream);
/* 330 */       postMethod.releaseConnection();
/*     */     } 
/*     */     
/* 333 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String doPostFile(String paramString1, String paramString2, String paramString3) {
/* 344 */     return doPostFile(paramString1, paramString2, paramString3, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String doPostFile(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 356 */     StringBuffer stringBuffer = new StringBuffer();
/* 357 */     String str = "";
/*     */     
/* 359 */     HttpClient httpClient = FWHttpConnectionManager.getHttpClient();
/* 360 */     httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(10000);
/* 361 */     httpClient.getHttpConnectionManager().getParams().setSoTimeout(10000);
/*     */     
/* 363 */     newlog.error("POST请求地址=" + paramString1);
/* 364 */     PostMethod postMethod = new PostMethod(paramString1);
/* 365 */     postMethod.setRequestHeader("Content-Type", paramString2);
/*     */     
/* 367 */     if (paramString4 == null || "".equals(paramString4)) {
/* 368 */       paramString4 = HTTP_UTF8;
/*     */     }
/*     */     
/*     */     try {
/* 372 */       File file = new File(paramString3);
/* 373 */       FileInputStream fileInputStream = new FileInputStream(file);
/* 374 */       InputStreamRequestEntity inputStreamRequestEntity = new InputStreamRequestEntity(fileInputStream);
/* 375 */       postMethod.setRequestEntity((RequestEntity)inputStreamRequestEntity);
/*     */       
/* 377 */       InputStream inputStream = null;
/* 378 */       int i = httpClient.executeMethod((HttpMethod)postMethod);
/* 379 */       newlog.error("status=" + i);
/* 380 */       if (i == 200) {
/* 381 */         inputStream = postMethod.getResponseBodyAsStream();
/* 382 */         BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream, paramString4));
/* 383 */         while ((str = bufferedReader.readLine()) != null) {
/* 384 */           stringBuffer.append(str);
/*     */         }
/* 386 */         bufferedReader.close();
/*     */       } else {
/* 388 */         newlog.error("Method failed : " + postMethod.getStatusLine());
/*     */       } 
/* 390 */     } catch (FileNotFoundException fileNotFoundException) {
/* 391 */       newlog.error("FileNotFoundException:", fileNotFoundException);
/* 392 */     } catch (IOException iOException) {
/* 393 */       newlog.error("IOException:", iOException);
/*     */     } 
/*     */     
/* 396 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String doPostData(String paramString1, String paramString2, String paramString3) {
/* 407 */     return doPostData(paramString1, paramString2, paramString3, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String doPostData(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 419 */     StringBuffer stringBuffer = new StringBuffer();
/* 420 */     String str = "";
/*     */     try {
/* 422 */       URL uRL = new URL(paramString1);
/* 423 */       URLConnection uRLConnection = uRL.openConnection();
/* 424 */       uRLConnection.setDoInput(true);
/* 425 */       uRLConnection.setDoOutput(true);
/* 426 */       uRLConnection.setConnectTimeout(5000);
/* 427 */       uRLConnection.setRequestProperty("Pragma:", "no-cache");
/* 428 */       uRLConnection.setRequestProperty("Cache-Control", "no-cache");
/* 429 */       uRLConnection.setRequestProperty("Content-Type", paramString2);
/*     */       
/* 431 */       if (paramString4 == null || "".equals(paramString4)) {
/* 432 */         paramString4 = HTTP_UTF8;
/*     */       }
/*     */       
/* 435 */       OutputStreamWriter outputStreamWriter = new OutputStreamWriter(uRLConnection.getOutputStream(), paramString4);
/* 436 */       String str1 = paramString3;
/* 437 */       newlog.error("post data=" + str1);
/* 438 */       outputStreamWriter.write(str1);
/* 439 */       outputStreamWriter.flush();
/* 440 */       outputStreamWriter.close();
/*     */       
/* 442 */       BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(uRLConnection.getInputStream(), paramString4));
/* 443 */       while ((str = bufferedReader.readLine()) != null) {
/* 444 */         stringBuffer.append(str);
/*     */       }
/* 446 */     } catch (MalformedURLException malformedURLException) {
/* 447 */       newlog.error("MalformedURLException:", malformedURLException);
/* 448 */     } catch (IOException iOException) {
/* 449 */       newlog.error("IOException:", iOException);
/*     */     } 
/*     */     
/* 452 */     return stringBuffer.toString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/util/HTTPUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */