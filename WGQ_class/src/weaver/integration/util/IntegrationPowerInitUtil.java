/*    */ package weaver.integration.util;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class IntegrationPowerInitUtil
/*    */ {
/* 14 */   private Logger newlog = LoggerFactory.getLogger(IntegrationPowerInitUtil.class);
/* 15 */   private String datashwoset_sql = "update datashowset set subcompanyid=?  where  subcompanyid is null  or subcompanyid=0  ";
/* 16 */   private String schedule_sql = "update schedulesetting set  subcompanyid=?  where  subcompanyid is null  or subcompanyid=0  ";
/* 17 */   private String webservice_sql = "update wsregiste set  subcompanyid=?  where  subcompanyid is null  or subcompanyid=0  ";
/* 18 */   private String automaticsetting_sql = "update outerdatawfset set  subcompanyid=?  where  subcompanyid is null  or subcompanyid=0  ";
/* 19 */   private String datashwosetcacheset_sql = "update datashowcacheset  set  subcompanyid=?  where  subcompanyid is null   or subcompanyid=0  ";
/*    */   
/* 21 */   private String datasourcesetting_sql = "update datasourcesetting set  subcompanyid=?  where  subcompanyid is null or subcompanyid=0 ";
/*    */   
/* 23 */   private String formactionset_sql = "update formactionset set  subcompanyid=?  where  subcompanyid is null  or subcompanyid=0 ";
/* 24 */   private String wsformactionset_sql = "update wsformactionset set  subcompanyid=?  where  subcompanyid is null  or subcompanyid=0 ";
/* 25 */   private String esbformactionset_sql = "update esbformactionset set  subcompanyid=?  where  subcompanyid is null  or subcompanyid=0";
/* 26 */   private String esb_actionset_sql = "update esb_actionset set  subcompanyid=?  where  subcompanyid is null  or subcompanyid=0 ";
/* 27 */   private String actionsetting_sql = "update actionsetting set  subcompanyid=?  where  subcompanyid is null  or subcompanyid=0 ";
/*    */   
/* 29 */   private String datasource_sql = "update datasourcesetting set subcompanyid=?  where  subcompanyid is null  or subcompanyid=0 ";
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean init(String paramString) {
/* 34 */     this.newlog.info(" ===================开始初始化集成模块分权默认机构==========================");
/* 35 */     boolean bool = false;
/* 36 */     RecordSet recordSet = new RecordSet();
/*    */     try {
/* 38 */       recordSet.executeUpdate(this.datashwoset_sql, new Object[] { paramString });
/* 39 */       recordSet.executeUpdate(this.datashwosetcacheset_sql, new Object[] { paramString + "" });
/* 40 */       recordSet.executeUpdate(this.schedule_sql, new Object[] { paramString });
/* 41 */       recordSet.executeUpdate(this.webservice_sql, new Object[] { paramString });
/* 42 */       recordSet.executeUpdate(this.automaticsetting_sql, new Object[] { paramString });
/* 43 */       recordSet.executeUpdate(this.datasourcesetting_sql, new Object[] { paramString });
/* 44 */       recordSet.executeUpdate(this.formactionset_sql, new Object[] { paramString });
/* 45 */       recordSet.executeUpdate(this.wsformactionset_sql, new Object[] { paramString });
/* 46 */       recordSet.executeUpdate(this.esbformactionset_sql, new Object[] { paramString });
/* 47 */       recordSet.executeUpdate(this.esb_actionset_sql, new Object[] { paramString });
/* 48 */       recordSet.executeUpdate(this.actionsetting_sql, new Object[] { paramString });
/*    */       
/* 50 */       recordSet.executeUpdate(this.datasource_sql, new Object[] { paramString });
/*    */ 
/*    */       
/* 53 */       bool = true;
/* 54 */     } catch (Exception exception) {
/* 55 */       exception.printStackTrace();
/* 56 */       this.newlog.error(exception);
/*    */     } 
/* 58 */     return bool;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/util/IntegrationPowerInitUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */