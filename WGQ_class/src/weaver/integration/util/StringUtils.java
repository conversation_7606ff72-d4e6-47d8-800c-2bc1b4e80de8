/*    */ package weaver.integration.util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class StringUtils
/*    */ {
/*    */   public static String getTimeSqlWhere(String paramString1, String paramString2, String paramString3) {
/* 23 */     String str = "";
/* 24 */     if (paramString1.equalsIgnoreCase("oracle")) {
/* 25 */       str = "(" + paramString2 + "||' '||" + paramString3 + ")";
/* 26 */     } else if (paramString1.equalsIgnoreCase("mysql")) {
/* 27 */       str = "(CONCAT(" + paramString2 + ",' '," + paramString3 + "))";
/* 28 */     } else if (paramString1.equalsIgnoreCase("sqlserver")) {
/* 29 */       str = "(" + paramString2 + "+' '+" + paramString3 + ")";
/*    */     }
/* 31 */     else if (paramString1.equalsIgnoreCase("postgresql")) {
/* 32 */       str = "(" + paramString2 + "||' '||" + paramString3 + ")";
/*    */     } 
/* 34 */     return str;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/util/StringUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */