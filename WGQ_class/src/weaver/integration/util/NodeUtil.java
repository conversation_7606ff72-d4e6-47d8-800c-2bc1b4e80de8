/*     */ package weaver.integration.util;
/*     */ 
/*     */ import java.io.IOException;
/*     */ import java.io.InputStreamReader;
/*     */ import java.net.InetAddress;
/*     */ import java.net.NetworkInterface;
/*     */ import java.net.SocketException;
/*     */ import java.nio.charset.Charset;
/*     */ import java.security.KeyManagementException;
/*     */ import java.security.NoSuchAlgorithmException;
/*     */ import java.security.cert.CertificateException;
/*     */ import java.security.cert.X509Certificate;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Enumeration;
/*     */ import java.util.UUID;
/*     */ import javax.net.ssl.SSLContext;
/*     */ import javax.net.ssl.TrustManager;
/*     */ import javax.net.ssl.X509TrustManager;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import org.apache.http.Header;
/*     */ import org.apache.http.HttpEntity;
/*     */ import org.apache.http.HttpResponse;
/*     */ import org.apache.http.client.CookieStore;
/*     */ import org.apache.http.client.methods.HttpPost;
/*     */ import org.apache.http.client.methods.HttpUriRequest;
/*     */ import org.apache.http.config.Registry;
/*     */ import org.apache.http.config.RegistryBuilder;
/*     */ import org.apache.http.conn.HttpClientConnectionManager;
/*     */ import org.apache.http.conn.socket.PlainConnectionSocketFactory;
/*     */ import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
/*     */ import org.apache.http.entity.StringEntity;
/*     */ import org.apache.http.impl.client.BasicCookieStore;
/*     */ import org.apache.http.impl.client.CloseableHttpClient;
/*     */ import org.apache.http.impl.client.HttpClients;
/*     */ import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
/*     */ import org.apache.http.util.CharArrayBuffer;
/*     */ import org.apache.http.util.EntityUtils;
/*     */ import weaver.file.Prop;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class NodeUtil
/*     */ {
/*  47 */   public static String SCHEDULE_REST_URL = "";
/*     */   
/*     */   public String postJSON(String paramString1, String paramString2) {
/*  50 */     logger.info("=========post url:" + paramString1 + ",params:" + paramString2);
/*  51 */     HttpPost httpPost = null;
/*  52 */     String str = "";
/*     */     try {
/*  54 */       CloseableHttpClient closeableHttpClient = getHttpClientSSL();
/*     */       
/*  56 */       String str1 = paramString1;
/*     */       
/*  58 */       httpPost = new HttpPost(str1);
/*     */       
/*  60 */       httpPost.setHeader("Content-type", "application/json; charset=utf-8");
/*  61 */       httpPost.setHeader("Connection", "Close");
/*  62 */       String str2 = getSessionId();
/*  63 */       httpPost.setHeader("SessionId", str2);
/*     */       
/*  65 */       StringEntity stringEntity = new StringEntity(paramString2, Charset.forName("UTF-8"));
/*  66 */       stringEntity.setContentEncoding("UTF-8");
/*  67 */       stringEntity.setContentType("application/json");
/*  68 */       httpPost.setEntity((HttpEntity)stringEntity);
/*     */       
/*  70 */       HttpResponse httpResponse = closeableHttpClient.execute((HttpUriRequest)httpPost);
/*  71 */       str = entityToString(httpResponse.getEntity());
/*  72 */       logger.info("===============resp:\n" + str);
/*     */ 
/*     */ 
/*     */       
/*  76 */       int i = httpResponse.getStatusLine().getStatusCode();
/*  77 */       if (i != 200) {
/*  78 */         logger.info("====================请求出错: " + i);
/*     */       } else {
/*  80 */         int j = 0;
/*  81 */         String str3 = "";
/*     */         
/*  83 */         for (Header header : httpResponse.getAllHeaders()) {
/*  84 */           if (header.getName().equals("retcode")) {
/*  85 */             j = Integer.parseInt(header.getValue());
/*     */           }
/*  87 */           if (header.getName().equals("SessionId")) {
/*  88 */             str3 = header.getValue();
/*     */           }
/*     */         } 
/*     */         
/*  92 */         if (200 != j) {
/*  93 */           logger.info("==============================error return code,  sessionId: " + str3 + "\tretCode: " + j);
/*     */         }
/*     */       }
/*     */     
/*  97 */     } catch (Exception exception) {
/*  98 */       exception.printStackTrace();
/*  99 */       logger.error("post error occured!!" + exception.getMessage());
/*     */     } finally {
/* 101 */       if (httpPost != null) {
/*     */         try {
/* 103 */           httpPost.releaseConnection();
/*     */         }
/* 105 */         catch (Exception exception) {
/* 106 */           exception.printStackTrace();
/* 107 */           logger.error(exception.getMessage());
/*     */         } 
/*     */       }
/*     */     } 
/* 111 */     return str;
/*     */   }
/*     */ 
/*     */   
/*     */   public static String getMasterNodeScheduleRestApiUrl(HttpServletRequest paramHttpServletRequest) {
/* 116 */     String str1 = "";
/* 117 */     String str2 = "";
/* 118 */     Prop prop = Prop.getInstance();
/* 119 */     str2 = Prop.getPropValue(GCONST.getConfigFile(), "MainControlIP");
/* 120 */     if (!"".equals(str2)) {
/* 121 */       str1 = paramHttpServletRequest.getScheme() + "://" + str2 + ":" + paramHttpServletRequest.getServerPort() + "/schedule/rest";
/*     */     }
/* 123 */     logger.info("=============getMasterNodeScheduleRestApiUrl:" + str1);
/* 124 */     SCHEDULE_REST_URL = str1;
/* 125 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isMasterNode() {
/* 134 */     String str = "";
/* 135 */     ArrayList<String> arrayList = getRealIp();
/* 136 */     Prop prop = Prop.getInstance();
/* 137 */     str = Prop.getPropValue(GCONST.getConfigFile(), "MainControlIP");
/* 138 */     if (arrayList == null || arrayList.size() == 0) {
/* 139 */       logger.info("System Init Error:Cannot get local Ip address,This may cause scripts or Timed task  not run! ");
/*     */     } else {
/* 141 */       logger.info("System Init Message:mainControlIp=" + str + " localIp:" + arrayList.toString());
/*     */     } 
/* 143 */     if ((!"".equals(str) && arrayList.contains(str)) || "".equals(str)) {
/* 144 */       return true;
/*     */     }
/* 146 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static ArrayList<String> getRealIp() {
/* 156 */     String str1 = null;
/* 157 */     String str2 = null;
/* 158 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/* 160 */     Enumeration<NetworkInterface> enumeration = null;
/*     */     try {
/* 162 */       enumeration = NetworkInterface.getNetworkInterfaces();
/* 163 */     } catch (SocketException socketException) {
/* 164 */       socketException.printStackTrace();
/*     */     } 
/* 166 */     InetAddress inetAddress = null;
/* 167 */     while (enumeration.hasMoreElements()) {
/* 168 */       NetworkInterface networkInterface = enumeration.nextElement();
/* 169 */       Enumeration<InetAddress> enumeration1 = networkInterface.getInetAddresses();
/* 170 */       while (enumeration1.hasMoreElements()) {
/* 171 */         inetAddress = enumeration1.nextElement();
/* 172 */         if (!inetAddress.isSiteLocalAddress() && !inetAddress.isLoopbackAddress() && inetAddress.getHostAddress().indexOf(":") == -1) {
/* 173 */           str2 = inetAddress.getHostAddress();
/* 174 */           if (str2 != null && !"".equals(str2))
/* 175 */             arrayList.add(str2);  continue;
/*     */         } 
/* 177 */         if (inetAddress.isSiteLocalAddress() && !inetAddress.isLoopbackAddress() && inetAddress.getHostAddress().indexOf(":") == -1) {
/* 178 */           str1 = inetAddress.getHostAddress();
/* 179 */           arrayList.add(str1);
/*     */         } 
/*     */       } 
/*     */     } 
/* 183 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public SSLContext createIgnoreVerifySSL() throws NoSuchAlgorithmException, KeyManagementException {
/* 194 */     SSLContext sSLContext = SSLContext.getInstance("SSLv3");
/*     */ 
/*     */     
/* 197 */     X509TrustManager x509TrustManager = new X509TrustManager()
/*     */       {
/*     */         public void checkClientTrusted(X509Certificate[] param1ArrayOfX509Certificate, String param1String) throws CertificateException {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/*     */         public void checkServerTrusted(X509Certificate[] param1ArrayOfX509Certificate, String param1String) throws CertificateException {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/*     */         public X509Certificate[] getAcceptedIssuers() {
/* 212 */           return null;
/*     */         }
/*     */       };
/*     */     
/* 216 */     sSLContext.init(null, new TrustManager[] { x509TrustManager }, null);
/* 217 */     return sSLContext;
/*     */   }
/*     */ 
/*     */   
/* 221 */   public CookieStore cookieStore = (CookieStore)new BasicCookieStore();
/*     */   
/*     */   private CloseableHttpClient getHttpClientSSL() throws KeyManagementException, NoSuchAlgorithmException {
/* 224 */     SSLContext sSLContext = createIgnoreVerifySSL();
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 229 */     Registry registry = RegistryBuilder.create().register("http", PlainConnectionSocketFactory.INSTANCE).register("https", new SSLConnectionSocketFactory(sSLContext)).build();
/* 230 */     PoolingHttpClientConnectionManager poolingHttpClientConnectionManager = new PoolingHttpClientConnectionManager(registry);
/* 231 */     HttpClients.custom().setConnectionManager((HttpClientConnectionManager)poolingHttpClientConnectionManager);
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 236 */     return HttpClients.custom().setConnectionManager((HttpClientConnectionManager)poolingHttpClientConnectionManager).setDefaultCookieStore(this.cookieStore).build();
/*     */   }
/*     */ 
/*     */   
/*     */   private String entityToString(HttpEntity paramHttpEntity) throws IOException {
/* 241 */     String str = null;
/* 242 */     if (paramHttpEntity != null) {
/* 243 */       long l = paramHttpEntity.getContentLength();
/* 244 */       if (l != -1L && l < 2048L) {
/* 245 */         str = EntityUtils.toString(paramHttpEntity, "UTF-8");
/*     */       } else {
/* 247 */         InputStreamReader inputStreamReader = new InputStreamReader(paramHttpEntity.getContent(), "UTF-8");
/* 248 */         CharArrayBuffer charArrayBuffer = new CharArrayBuffer(2048);
/* 249 */         char[] arrayOfChar = new char[1024];
/*     */         int i;
/* 251 */         while ((i = inputStreamReader.read(arrayOfChar)) != -1) {
/* 252 */           charArrayBuffer.append(arrayOfChar, 0, i);
/*     */         }
/* 254 */         str = charArrayBuffer.toString();
/*     */       } 
/*     */     } 
/* 257 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getSessionId() {
/* 266 */     UUID uUID = UUID.randomUUID();
/* 267 */     String str = uUID.toString();
/* 268 */     return str.substring(0, 8) + str.substring(9, 13) + str.substring(14, 18) + str.substring(19, 23) + str.substring(24);
/*     */   }
/*     */   
/* 271 */   private static Logger logger = LoggerFactory.getLogger(NodeUtil.class);
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/util/NodeUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */