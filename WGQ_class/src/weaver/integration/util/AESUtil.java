/*    */ package weaver.integration.util;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.file.Prop;
/*    */ import weaver.general.AES;
/*    */ import weaver.general.Util;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class AESUtil
/*    */ {
/* 20 */   private static Logger log = LoggerFactory.getLogger(AESUtil.class);
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static String encrypt(String paramString1, String paramString2) {
/* 36 */     return AES.encrypt(paramString1, paramString2);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static String decrypt(String paramString1, String paramString2) {
/* 46 */     return AES.decrypt(paramString1, paramString2);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static String encrypt(String paramString) {
/* 55 */     return AES.encrypt(paramString, getpwd());
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static String decrypt(String paramString) {
/* 65 */     return AES.decrypt(paramString, getpwd());
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static String getpwd() {
/* 73 */     String str = "123456";
/* 74 */     RecordSet recordSet = new RecordSet();
/* 75 */     recordSet.execute("select * from integration_AESINFO");
/* 76 */     if (recordSet.next()) {
/* 77 */       str = Util.null2String(recordSet.getString("pwd"));
/*    */     } else {
/* 79 */       String str1 = Prop.getPropValue("AESpassword", "pwd");
/* 80 */       recordSet.executeUpdate("insert into integration_AESINFO(pwd) values(?)", new Object[] { str1 });
/* 81 */       str = str1;
/*    */     } 
/* 83 */     return str;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/util/AESUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */