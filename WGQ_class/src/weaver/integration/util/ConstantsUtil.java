/*    */ package weaver.integration.util;
/*    */ 
/*    */ import weaver.general.GCONST;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ConstantsUtil
/*    */ {
/* 13 */   public static String INTEGRATION_OFS_RESTURL = "/integration/ofs/OfsRestServices.jsp";
/*    */   
/* 15 */   public static String INTEGRATION_OFS_EXAMPLE = GCONST.getContextPath() + "/integration/ofssend/OfsExample.jsp";
/*    */   
/* 17 */   public static String INTEGRATION_OFS_APPURL = GCONST.getContextPath() + "/mobile/plugin/1/view.jsp";
/*    */   
/* 19 */   public static String INTEGRATION_OFS_APPURLE9 = GCONST.getContextPath() + "/spa/workflow/static4mobileform/index.html";
/*    */   
/* 21 */   public static String INTEGRATION_OFS_SSOLOGIN = GCONST.getContextPath() + "/login/VerifySSoLogin.jsp";
/*    */   
/* 23 */   public static String INTEGRATION_OCS_LOGIN = "/login/VerifyEimLogin.jsp?gopage=/wui/main.jsp&logintype=1&loginid=EIM_USERNAME&userpassword=EIM_PASSWORD";
/*    */   
/* 25 */   public static String INTEGRATION_RTX_LOGIN = "/login/RTXClientLoginOA.jsp?gopage=/wui/main.jsp";
/*    */   
/* 27 */   public static String INTEGRATION_RTX_LOGIN1 = "/login/VerifyRtxLogin.jsp";
/*    */   
/* 29 */   public static String INTEGRATION_SAP_LIB1 = GCONST.getContextPath() + "/integration/sapjar/sapjco-2-3/Linux amd64.zip";
/*    */   
/* 31 */   public static String INTEGRATION_SAP_LIB2 = GCONST.getContextPath() + "/integration/sapjar/sapjco-2-3/Linux ia64.zip";
/*    */   
/* 33 */   public static String INTEGRATION_SAP_LIB3 = GCONST.getContextPath() + "/integration/sapjar/sapjco21P_10-20007302.zip";
/*    */   
/* 35 */   public static String INTEGRATION_SAP_LIB4 = GCONST.getContextPath() + "/integration/sapjar/sapjco-2-3/Linux 32.zip";
/*    */   
/* 37 */   public static String INTEGRATION_SAP_LIB5 = GCONST.getContextPath() + "/integration/sapjar/compat-libstdc++-33-3.2.3-69.el6.x86_64.rpm";
/*    */   
/* 39 */   public static String INTEGRATION_SAP_LIB6 = GCONST.getContextPath() + "/integration/sapjar/sapjco-2-3/window amd64.zip";
/*    */   
/* 41 */   public static String INTEGRATION_SAP_LIB7 = GCONST.getContextPath() + "/integration/sapjar/sapjco21P_10-20007306.zip";
/*    */   
/* 43 */   public static String INTEGRATION_SAP_LIB8 = GCONST.getContextPath() + "/integration/sapjar/sapjco-2-3/windows 32bit.zip";
/*    */   
/* 45 */   public static String INTEGRATION_OUTTER_LOGIN = GCONST.getContextPath() + "/interface/Entrance.jsp";
/*    */   
/* 47 */   public static String INTEGRATION_OUTTER_LOGIN_GBK = GCONST.getContextPath() + "/interface/Entrance_gbk.jsp";
/*    */   
/* 49 */   public static String INTEGRATION_OUTTER_IMAGE = GCONST.getContextPath() + "/page/element/outterSys/resource/image/outterdefaultimag.png";
/*    */   
/* 51 */   public static String INTEGRATION_OUTTER_ACCOUNTSET = "/integration/accountSetting/";
/*    */   
/* 53 */   public static String INTEGRATION_OUTTER_ACCOUNTSET1 = GCONST.getContextPath() + "/spa/integration/static4engine/engine.html#/main/integration/accountSetting/";
/*    */   
/* 55 */   public static String INTEGRATION_DATASOURCE_UPLOADFILE = GCONST.getContextPath() + "/api/doc/upload/uploadFile";
/*    */   
/* 57 */   public static String INTEGRATION_WORKFLOW_URL = GCONST.getContextPath() + "/workflow/request/ViewRequest.jsp";
/*    */   
/* 59 */   public static String INTEGRATION_WORKFLOW_URL_NONE = "/workflow/request/ViewRequest.jsp";
/*    */   
/* 61 */   public static String INTEGRATION_WORKFLOW_PICTUREURL = "/workflow/request/WorkflowRequestPictureInner.jsp";
/*    */   
/* 63 */   public static String INTEGRATION_EXPDOC_EXPORT = GCONST.getContextPath() + "/workflow/request/ExportRequest.jsp";
/*    */   
/* 65 */   public static String INTEGRATION_COMMON_DOWNFILE = GCONST.getContextPath() + "/weaver/weaver.file.FileDownload";
/*    */   
/* 67 */   public static String INTEGRATION_COMMON_MAIN = GCONST.getContextPath() + "/wui/index.html#/main";
/*    */   
/* 69 */   public static String INTEGRATION_COMMON_LOGIN_OA = GCONST.getContextPath() + "/login/OALogin.jsp";
/*    */   
/* 71 */   public static String INTEGRATION_LDAP_DOWNLOAD = GCONST.getContextPath() + "/login/LdapAttachmentDownload.jsp";
/*    */   
/* 73 */   public static String INTEGRATION_SCHEDULE_RESTURL = GCONST.getContextPath() + "/schedule/restnew";
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/util/ConstantsUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */