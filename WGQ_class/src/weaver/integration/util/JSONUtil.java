/*     */ package weaver.integration.util;
/*     */ 
/*     */ import com.jayway.jsonpath.JsonPath;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import net.sf.json.JSONArray;
/*     */ import net.sf.json.JSONObject;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class JSONUtil
/*     */ {
/*  31 */   private static Logger newlog = LoggerFactory.getLogger(JSONUtil.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject toJSONObject(Object paramObject) {
/*  39 */     return JSONObject.fromObject(paramObject);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONArray toJSONArray(Object paramObject) {
/*  48 */     return JSONArray.fromObject(paramObject);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String toJSONString(JSONArray paramJSONArray) {
/*  57 */     return paramJSONArray.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String toJSONString(JSONObject paramJSONObject) {
/*  66 */     return paramJSONObject.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static <T> String toJSONString(List<T> paramList) {
/*  76 */     JSONArray jSONArray = JSONArray.fromObject(paramList);
/*  77 */     return jSONArray.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String toJSONString(Object paramObject) {
/*  86 */     JSONArray jSONArray = JSONArray.fromObject(paramObject);
/*  87 */     return jSONArray.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Collection toCollection(Object paramObject) {
/*  96 */     JSONArray jSONArray = JSONArray.fromObject(paramObject);
/*  97 */     return JSONArray.toCollection(jSONArray);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List toArrayList(Object paramObject) {
/* 106 */     ArrayList<Object> arrayList = new ArrayList();
/*     */     
/* 108 */     JSONArray jSONArray = JSONArray.fromObject(paramObject);
/* 109 */     Iterator<JSONObject> iterator = jSONArray.iterator();
/*     */     
/* 111 */     while (iterator.hasNext()) {
/* 112 */       JSONObject jSONObject = iterator.next();
/* 113 */       Iterator<Object> iterator1 = jSONObject.keys();
/*     */       
/* 115 */       while (iterator1.hasNext()) {
/* 116 */         Object object = iterator1.next();
/* 117 */         Object object1 = jSONObject.get(object);
/* 118 */         arrayList.add(object1);
/*     */       } 
/*     */     } 
/*     */     
/* 122 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static HashMap toHashMap(Object paramObject) {
/* 131 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 133 */     JSONObject jSONObject = toJSONObject(paramObject);
/* 134 */     Iterator iterator = jSONObject.keys();
/*     */     
/* 136 */     while (iterator.hasNext()) {
/* 137 */       String str = String.valueOf(iterator.next());
/* 138 */       Object object = jSONObject.get(str);
/* 139 */       hashMap.put(str, object);
/*     */     } 
/*     */     
/* 142 */     return hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<Map<String, Object>> toList(Object paramObject) {
/* 151 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 152 */     JSONArray jSONArray = JSONArray.fromObject(paramObject);
/*     */     
/* 154 */     for (JSONObject jSONObject1 : jSONArray) {
/* 155 */       JSONObject jSONObject2 = jSONObject1;
/* 156 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 157 */       Iterator<String> iterator = jSONObject2.keys();
/*     */       
/* 159 */       while (iterator.hasNext()) {
/* 160 */         String str = iterator.next();
/* 161 */         Object object = jSONObject2.get(str);
/* 162 */         hashMap.put(str, object);
/*     */       } 
/* 164 */       arrayList.add(hashMap);
/*     */     } 
/*     */     
/* 167 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static <T> List<T> toList(Object paramObject, Class<T> paramClass) {
/* 178 */     JSONArray jSONArray = JSONArray.fromObject(paramObject);
/* 179 */     return JSONArray.toList(jSONArray, paramClass);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static <T> List<T> toList(JSONArray paramJSONArray, Class<T> paramClass) {
/* 191 */     return JSONArray.toList(paramJSONArray, paramClass);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static <T> T toBean(Object paramObject, Class<T> paramClass) {
/* 202 */     JSONObject jSONObject = JSONObject.fromObject(paramObject);
/* 203 */     return (T)JSONObject.toBean(jSONObject, paramClass);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static <T> T toBean(JSONObject paramJSONObject, Class<T> paramClass) {
/* 214 */     return (T)JSONObject.toBean(paramJSONObject, paramClass);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Object parseJSON2Object(String paramString1, String paramString2) {
/* 226 */     return JsonPath.read(paramString1, paramString2, new com.jayway.jsonpath.Predicate[0]);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Object parseJSON2Object(JSONObject paramJSONObject, String paramString) {
/* 237 */     return JsonPath.parse(paramJSONObject).read(paramString, new com.jayway.jsonpath.Predicate[0]);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<Object> parseJSON2List(String paramString1, String paramString2) {
/* 248 */     return (List)JsonPath.read(paramString1, paramString2, new com.jayway.jsonpath.Predicate[0]);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<Object> parseJSON2List(JSONObject paramJSONObject, String paramString) {
/* 259 */     return (List)JsonPath.parse(paramJSONObject).read(paramString, new com.jayway.jsonpath.Predicate[0]);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static <T> T parseJSON2Clazz(JSONObject paramJSONObject, String paramString, Class<T> paramClass) {
/* 271 */     return (T)JsonPath.parse(paramJSONObject).read(paramString, paramClass, new com.jayway.jsonpath.Predicate[0]);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/util/JSONUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */