/*    */ package weaver.integration.util;
/*    */ 
/*    */ import com.alibaba.fastjson.JSONObject;
/*    */ import com.alibaba.fastjson.JSONPath;
/*    */ import java.util.Map;
/*    */ import java.util.concurrent.ConcurrentHashMap;
/*    */ import java.util.regex.Matcher;
/*    */ import java.util.regex.Pattern;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class AccountTypeUtils
/*    */ {
/* 17 */   public static Map<String, String> accountTypes = new ConcurrentHashMap<>();
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   static {
/* 27 */     accountTypes.put("1", "loginid");
/* 28 */     accountTypes.put("2", "certificatenum");
/* 29 */     accountTypes.put("3", "id");
/* 30 */     accountTypes.put("4", "workcode");
/* 31 */     accountTypes.put("5", "email");
/* 32 */     accountTypes.put("6", "mobile");
/* 33 */     accountTypes.put("7", "customsql");
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static String getValueByJSONPath(JSONObject paramJSONObject, String paramString) {
/* 43 */     return Util.null2String(JSONPath.eval(paramJSONObject, "$." + paramString));
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static String getFixedCustomSQLByJSONObject(JSONObject paramJSONObject, String paramString) {
/* 54 */     StringBuffer stringBuffer = new StringBuffer();
/* 55 */     Pattern pattern = Pattern.compile("\\$\\{.{1,}?\\}");
/* 56 */     Matcher matcher = pattern.matcher(paramString);
/* 57 */     while (matcher.find()) {
/* 58 */       String str1 = matcher.group();
/* 59 */       if (str1 != null && str1.length() > 3) {
/* 60 */         str1 = str1.replace("${", "").replace("}", "");
/*    */       }
/* 62 */       String str2 = String.valueOf(JSONPath.eval(paramJSONObject, "$." + str1));
/* 63 */       str2 = str2.replace("\\", "\\\\").replace("$", "\\$");
/* 64 */       matcher.appendReplacement(stringBuffer, str2);
/*    */     } 
/* 66 */     matcher.appendTail(stringBuffer);
/*    */     
/* 68 */     return stringBuffer.toString();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static String getFixedCustomSQLByMap(Map<String, Object> paramMap, String paramString) {
/* 79 */     StringBuffer stringBuffer = new StringBuffer();
/* 80 */     Pattern pattern = Pattern.compile("\\$\\{.{1,}?\\}");
/* 81 */     Matcher matcher = pattern.matcher(paramString);
/* 82 */     while (matcher.find()) {
/* 83 */       String str1 = matcher.group();
/* 84 */       if (str1 != null && str1.length() > 3) {
/* 85 */         str1 = str1.replace("${", "").replace("}", "");
/*    */       }
/* 87 */       String str2 = String.valueOf(paramMap.get(str1));
/* 88 */       str2 = str2.replace("\\", "\\\\").replace("$", "\\$");
/* 89 */       matcher.appendReplacement(stringBuffer, str2);
/*    */     } 
/* 91 */     matcher.appendTail(stringBuffer);
/*    */     
/* 93 */     return stringBuffer.toString();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/util/AccountTypeUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */