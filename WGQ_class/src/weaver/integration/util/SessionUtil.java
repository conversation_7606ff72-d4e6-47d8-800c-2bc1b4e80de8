/*     */ package weaver.integration.util;
/*     */ 
/*     */ import com.api.common.service.LoginCommonService;
/*     */ import com.api.common.service.impl.LoginCommonServiceImpl;
/*     */ import com.engine.common.service.impl.ThemeServiceImpl;
/*     */ import com.engine.common.util.ServiceUtil;
/*     */ import java.io.UnsupportedEncodingException;
/*     */ import java.net.URLEncoder;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import java.util.concurrent.ConcurrentHashMap;
/*     */ import javax.servlet.ServletRequest;
/*     */ import javax.servlet.ServletResponse;
/*     */ import javax.servlet.http.Cookie;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.AES;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SessionUtil
/*     */ {
/*     */   public static String getUserIdByRule(Map<String, Object> paramMap) {
/*  39 */     String str1 = Util.null2String(paramMap.get("accountType"));
/*  40 */     String str2 = Util.null2String(paramMap.get("loginType"));
/*  41 */     String str3 = Util.null2String(paramMap.get("principalName"));
/*  42 */     String str4 = Util.null2String(paramMap.get("customSQL"));
/*     */     
/*  44 */     String str5 = "";
/*  45 */     if ("customsql".equals(str1)) {
/*     */       
/*  47 */       str5 = str4;
/*  48 */       RecordSet recordSet = new RecordSet();
/*  49 */       recordSet.executeQuery(str5, new Object[0]);
/*  50 */       if (recordSet.next()) {
/*  51 */         return recordSet.getString("id");
/*     */       }
/*  53 */       logger.info("============getUserIdByRule customSQL:" + str5);
/*     */     } else {
/*     */       
/*  56 */       String str = (new RecordSet()).getDBType();
/*     */ 
/*     */       
/*  59 */       if ("1".equals(str2)) {
/*  60 */         str5 = "select * from HrmResource where " + str1 + " ='" + str3 + "' and status<4 and (accounttype !=1 or accounttype is null)";
/*  61 */       } else if ("2".equals(str2)) {
/*  62 */         str5 = "select * from CRM_CustomerInfo where portalloginid ='" + str3 + "' and deleted=0 and PortalStatus=2";
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  71 */       logger.info("============getUserIdByRule SearchSQL1:" + str5);
/*  72 */       RecordSet recordSet = new RecordSet();
/*  73 */       recordSet.executeQuery(str5, new Object[0]);
/*  74 */       if (recordSet.next()) {
/*  75 */         return recordSet.getString("id");
/*     */       }
/*     */       
/*  78 */       str5 = "select * from hrmresourcemanager where loginid='" + str3 + "' ";
/*  79 */       recordSet.executeQuery(str5, new Object[0]);
/*  80 */       if (recordSet.next()) {
/*  81 */         return recordSet.getString("id");
/*     */       }
/*  83 */       logger.info("============getUserIdByRule SearchSQL2:" + str5);
/*     */     } 
/*  85 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getUserIdByRuleForToken(Map<String, Object> paramMap) {
/*  96 */     String str1 = Util.null2String(paramMap.get("accountType"));
/*  97 */     String str2 = Util.null2String(paramMap.get("loginType"));
/*  98 */     String str3 = Util.null2String(paramMap.get("principalName"));
/*  99 */     String str4 = Util.null2String(paramMap.get("customSQL"));
/*     */     
/* 101 */     String str5 = "";
/* 102 */     if ("customsql".equals(str1)) {
/*     */       
/* 104 */       str5 = str4;
/* 105 */       RecordSet recordSet = new RecordSet();
/* 106 */       recordSet.executeQuery(str5, new Object[0]);
/* 107 */       if (recordSet.next()) {
/* 108 */         return recordSet.getString("id");
/*     */       }
/* 110 */       logger.info("============getUserIdByRule customSQL:" + str5);
/*     */     } else {
/*     */       
/* 113 */       String str = (new RecordSet()).getDBType();
/*     */ 
/*     */       
/* 116 */       if ("1".equals(str2)) {
/* 117 */         str5 = "select * from HrmResource where " + str1 + " ='" + str3 + "' and status<4 ";
/* 118 */       } else if ("2".equals(str2)) {
/* 119 */         str5 = "select * from CRM_CustomerInfo where portalloginid ='" + str3 + "' and deleted=0 and PortalStatus=2";
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 128 */       logger.info("============getUserIdByRule SearchSQL1:" + str5);
/* 129 */       RecordSet recordSet = new RecordSet();
/* 130 */       recordSet.executeQuery(str5, new Object[0]);
/* 131 */       if (recordSet.next()) {
/* 132 */         return recordSet.getString("id");
/*     */       }
/*     */       
/* 135 */       str5 = "select * from hrmresourcemanager where loginid='" + str3 + "' ";
/* 136 */       recordSet.executeQuery(str5, new Object[0]);
/* 137 */       if (recordSet.next()) {
/* 138 */         return recordSet.getString("id");
/*     */       }
/* 140 */       logger.info("============getUserIdByRule SearchSQL2:" + str5);
/*     */     } 
/* 142 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Object createSession(String paramString, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 155 */     logger.info("==============createSession start, userId:" + paramString);
/* 156 */     LoginCommonService loginCommonService = (LoginCommonService)ServiceUtil.getService(LoginCommonServiceImpl.class, null);
/* 157 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 158 */     hashMap.put("userid", paramString);
/* 159 */     return loginCommonService.doUserSession(paramHttpServletRequest, paramHttpServletResponse, hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getHomePageAfterLogin(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 170 */     ThemeServiceImpl themeServiceImpl = new ThemeServiceImpl();
/* 171 */     String str1 = paramHttpServletRequest.getServletPath().toLowerCase();
/*     */ 
/*     */     
/* 174 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("gopage"));
/* 175 */     String str3 = "3";
/* 176 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 177 */     hashMap.put("gopage", str2);
/* 178 */     hashMap.put("target", str3);
/* 179 */     String str4 = themeServiceImpl.getMainUrl(hashMap);
/* 180 */     logger.info("========================getHomePageAfterLogin url:" + str4);
/*     */     
/* 182 */     return str4;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getLoginType(HttpServletRequest paramHttpServletRequest) {
/* 192 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("logintype"));
/* 193 */     String str2 = "";
/* 194 */     User user = (User)paramHttpServletRequest.getSession(true).getAttribute("weaver_user@bean");
/* 195 */     if (user != null) {
/* 196 */       str2 = Util.null2String(user.getLogintype());
/* 197 */       str1 = str2;
/*     */     } 
/*     */     
/* 200 */     if (str1.equals("")) {
/* 201 */       str1 = "1";
/*     */     }
/* 203 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void setCookie(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 214 */     String str1 = paramHttpServletRequest.getRequestURI();
/* 215 */     String str2 = paramHttpServletRequest.getParameter("isRememberAccount_");
/* 216 */     String str3 = paramHttpServletRequest.getParameter("isRememberPassword_");
/* 217 */     String str4 = paramHttpServletRequest.getParameter("loginid_");
/* 218 */     String str5 = paramHttpServletRequest.getParameter("up_");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 226 */     Cookie[] arrayOfCookie = paramHttpServletRequest.getCookies();
/*     */     
/* 228 */     String str6 = paramHttpServletRequest.getHeader("user-agent");
/* 229 */     if (str6 != null && str6.toLowerCase().indexOf("e-mobile") >= 0) {
/* 230 */       logger.error("============user-agent is e-mobile,now set the CASTGC cookie maxAge with 365 days.");
/* 231 */       if (arrayOfCookie != null && arrayOfCookie.length > 0) {
/* 232 */         for (Cookie cookie : arrayOfCookie) {
/* 233 */           if ("CASTGC".equals(cookie.getName())) {
/* 234 */             Cookie cookie1 = new Cookie("CASTGC", cookie.getValue());
/* 235 */             cookie1.setPath("/");
/* 236 */             cookie1.setMaxAge(********);
/* 237 */             paramHttpServletResponse.addCookie(cookie1);
/*     */             
/*     */             break;
/*     */           } 
/*     */         } 
/*     */       }
/*     */     } 
/*     */     
/* 245 */     if (str2 != null) {
/* 246 */       if ("1".equals(str2)) {
/* 247 */         if (str4 != null) {
/* 248 */           str4 = AES.decrypt(str4, "loginid_");
/*     */         }
/*     */ 
/*     */         
/* 252 */         Cookie cookie = null;
/*     */         try {
/* 254 */           cookie = new Cookie("cacheAccount", URLEncoder.encode(str4, "UTF-8"));
/* 255 */         } catch (UnsupportedEncodingException unsupportedEncodingException) {
/* 256 */           unsupportedEncodingException.printStackTrace();
/*     */         } 
/*     */         
/* 259 */         cookie.setPath("/");
/* 260 */         cookie.setMaxAge(********);
/* 261 */         paramHttpServletResponse.addCookie(cookie);
/*     */         
/* 263 */         cookie = new Cookie("isRememberAccount", "true");
/* 264 */         cookie.setPath("/");
/* 265 */         cookie.setMaxAge(********);
/* 266 */         paramHttpServletResponse.addCookie(cookie);
/*     */       }
/*     */       else {
/*     */         
/* 270 */         for (Cookie cookie1 : arrayOfCookie) {
/* 271 */           if ("cacheAccount".equals(cookie1.getName())) {
/* 272 */             logger.info("=============remove cookie cacheAccount!");
/* 273 */             cookie1.setValue(null);
/* 274 */             cookie1.setMaxAge(0);
/* 275 */             cookie1.setPath("/");
/* 276 */             paramHttpServletResponse.addCookie(cookie1);
/*     */             
/*     */             break;
/*     */           } 
/*     */         } 
/* 281 */         Cookie cookie = new Cookie("isRememberAccount", "false");
/* 282 */         cookie.setPath("/");
/* 283 */         cookie.setMaxAge(********);
/* 284 */         paramHttpServletResponse.addCookie(cookie);
/*     */       } 
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 291 */     if (str3 != null) {
/* 292 */       if ("1".equals(str3)) {
/* 293 */         Cookie cookie = new Cookie("cachePassword", str5);
/* 294 */         cookie.setPath("/");
/* 295 */         cookie.setMaxAge(********);
/* 296 */         paramHttpServletResponse.addCookie(cookie);
/*     */         
/* 298 */         cookie = new Cookie("isRememberPassword", "true");
/* 299 */         cookie.setPath("/");
/* 300 */         cookie.setMaxAge(********);
/* 301 */         paramHttpServletResponse.addCookie(cookie);
/*     */       } else {
/*     */         
/* 304 */         for (Cookie cookie1 : arrayOfCookie) {
/* 305 */           if ("cachePassword".equals(cookie1.getName())) {
/* 306 */             logger.info("=============remove cookie cachePassword!");
/* 307 */             cookie1.setValue(null);
/* 308 */             cookie1.setMaxAge(0);
/* 309 */             cookie1.setPath("/");
/* 310 */             paramHttpServletResponse.addCookie(cookie1);
/*     */             
/*     */             break;
/*     */           } 
/*     */         } 
/* 315 */         Cookie cookie = new Cookie("isRememberPassword", "false");
/* 316 */         cookie.setPath("/");
/* 317 */         cookie.setMaxAge(********);
/* 318 */         paramHttpServletResponse.addCookie(cookie);
/*     */       } 
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Map<String, Object> getSSOCookieKeys(ServletRequest paramServletRequest, ServletResponse paramServletResponse) {
/* 334 */     return (Map)new ConcurrentHashMap<>();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/* 339 */   private static Logger logger = LoggerFactory.getLogger(SessionUtil.class);
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/util/SessionUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */