/*    */ package weaver.integration.framework.mapping.impl;
/*    */ 
/*    */ import java.util.List;
/*    */ import weaver.integration.framework.mapping.IMapping;
/*    */ 
/*    */ public class TableDataMapping
/*    */   implements IMapping {
/*    */   private String sourceTableName;
/*    */   private String targetTableName;
/*    */   private String tableType;
/*    */   private int tableOrder;
/*    */   private List<RecordDataMapping> recordDataMappingList;
/*    */   private List<FieldDataMapping> templateFieldDataMappingList;
/*    */   
/*    */   public String getSourceTableName() {
/* 16 */     return this.sourceTableName;
/*    */   }
/*    */ 
/*    */   
/*    */   public void setSourceTableName(String paramString) {
/* 21 */     this.sourceTableName = paramString;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getTargetTableName() {
/* 26 */     return this.targetTableName;
/*    */   }
/*    */ 
/*    */   
/*    */   public void setTargetTableName(String paramString) {
/* 31 */     this.targetTableName = paramString;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getTableType() {
/* 36 */     return this.tableType;
/*    */   }
/*    */ 
/*    */   
/*    */   public void setTableType(String paramString) {
/* 41 */     this.tableType = paramString;
/*    */   }
/*    */ 
/*    */   
/*    */   public int getTableOrder() {
/* 46 */     return this.tableOrder;
/*    */   }
/*    */ 
/*    */   
/*    */   public void setTableOrder(int paramInt) {
/* 51 */     this.tableOrder = paramInt;
/*    */   }
/*    */ 
/*    */   
/*    */   public List<RecordDataMapping> getRecordDataMappingList() {
/* 56 */     return this.recordDataMappingList;
/*    */   }
/*    */ 
/*    */   
/*    */   public void setRecordDataMappingList(List<RecordDataMapping> paramList) {
/* 61 */     this.recordDataMappingList = paramList;
/*    */   }
/*    */ 
/*    */   
/*    */   public List<FieldDataMapping> getTemplateFieldDataMappingList() {
/* 66 */     return this.templateFieldDataMappingList;
/*    */   }
/*    */ 
/*    */   
/*    */   public void setTemplateFieldDataMappingList(List<FieldDataMapping> paramList) {
/* 71 */     this.templateFieldDataMappingList = paramList;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean mapping() {
/* 76 */     boolean bool = true;
/* 77 */     for (RecordDataMapping recordDataMapping : this.recordDataMappingList) {
/* 78 */       if (!recordDataMapping.mapping()) {
/* 79 */         bool = false;
/*    */         break;
/*    */       } 
/*    */     } 
/* 83 */     return bool;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/mapping/impl/TableDataMapping.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */