/*    */ package weaver.integration.framework.mapping.impl;
/*    */ 
/*    */ import java.util.List;
/*    */ import weaver.integration.framework.mapping.IMapping;
/*    */ 
/*    */ public class GeneralDataMapping
/*    */   implements IMapping
/*    */ {
/*    */   private List<TableDataMapping> tableDataMappingList;
/*    */   
/*    */   public List<TableDataMapping> getTableDataMappingList() {
/* 12 */     return this.tableDataMappingList;
/*    */   }
/*    */ 
/*    */   
/*    */   public void setTableDataMappingList(List<TableDataMapping> paramList) {
/* 17 */     this.tableDataMappingList = paramList;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean mapping() {
/* 23 */     boolean bool = true;
/* 24 */     for (TableDataMapping tableDataMapping : this.tableDataMappingList) {
/* 25 */       if (!tableDataMapping.mapping()) {
/* 26 */         bool = false;
/*    */         break;
/*    */       } 
/*    */     } 
/* 30 */     return bool;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/mapping/impl/GeneralDataMapping.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */