/*    */ package weaver.integration.framework.mapping.impl;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import weaver.integration.framework.mapping.IMapping;
/*    */ 
/*    */ public class FieldDataListMapping
/*    */   implements IMapping {
/*    */   private List<FieldDataMapping> fieldDataMappingList;
/*    */   
/*    */   public List<FieldDataMapping> getFieldDataMappingList() {
/* 12 */     return this.fieldDataMappingList;
/*    */   }
/*    */   
/*    */   public void setFieldDataMappingList(List<FieldDataMapping> paramList) {
/* 16 */     this.fieldDataMappingList = paramList;
/*    */   }
/*    */   
/*    */   public void addFieldDataMapping(FieldDataMapping paramFieldDataMapping) {
/* 20 */     if (this.fieldDataMappingList == null) {
/* 21 */       this.fieldDataMappingList = new ArrayList<>();
/*    */     }
/* 23 */     this.fieldDataMappingList.add(paramFieldDataMapping);
/*    */   }
/*    */   
/*    */   public boolean mapping() {
/* 27 */     boolean bool = true;
/* 28 */     for (FieldDataMapping fieldDataMapping : this.fieldDataMappingList) {
/* 29 */       if (!fieldDataMapping.mapping()) {
/* 30 */         bool = false;
/*    */         break;
/*    */       } 
/*    */     } 
/* 34 */     return bool;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/mapping/impl/FieldDataListMapping.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */