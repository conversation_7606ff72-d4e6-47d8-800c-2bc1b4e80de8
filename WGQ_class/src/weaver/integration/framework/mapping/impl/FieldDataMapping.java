/*    */ package weaver.integration.framework.mapping.impl;
/*    */ 
/*    */ import weaver.integration.framework.Context;
/*    */ import weaver.integration.framework.converter.IConvert;
/*    */ import weaver.integration.framework.converter.common.CommonConvert;
/*    */ import weaver.integration.framework.data.field.FieldData;
/*    */ import weaver.integration.framework.mapping.IMapping;
/*    */ 
/*    */ public class FieldDataMapping implements IMapping {
/*    */   private FieldData sourceField;
/*    */   private FieldData targetField;
/*    */   private Context context;
/*    */   private IConvert convert;
/*    */   
/*    */   public Context getContext() {
/* 16 */     return this.context;
/*    */   }
/*    */   
/*    */   public void setContext(Context paramContext) {
/* 20 */     this.context = paramContext;
/*    */   }
/*    */ 
/*    */   
/*    */   protected Object clone() throws CloneNotSupportedException {
/* 25 */     return super.clone();
/*    */   }
/*    */ 
/*    */   
/*    */   public FieldDataMapping() {}
/*    */ 
/*    */   
/*    */   public FieldDataMapping(FieldData paramFieldData1, FieldData paramFieldData2, IConvert paramIConvert) {
/* 33 */     setSourceField(paramFieldData1);
/* 34 */     setTargetField(paramFieldData2);
/* 35 */     setConvert(paramIConvert);
/*    */   }
/*    */   public FieldData getSourceField() {
/* 38 */     return this.sourceField;
/*    */   }
/*    */   
/*    */   public void setSourceField(FieldData paramFieldData) {
/* 42 */     this.sourceField = paramFieldData;
/*    */   }
/*    */   
/*    */   public FieldData getTargetField() {
/* 46 */     return this.targetField;
/*    */   }
/*    */   
/*    */   public void setTargetField(FieldData paramFieldData) {
/* 50 */     this.targetField = paramFieldData;
/*    */   }
/*    */   
/*    */   public IConvert getConvert() {
/* 54 */     return this.convert;
/*    */   }
/*    */   
/*    */   public void setConvert(IConvert paramIConvert) {
/* 58 */     this.convert = paramIConvert;
/*    */   }
/*    */   
/*    */   public boolean mapping() {
/* 62 */     boolean bool = false;
/* 63 */     if (this.convert != null && this.context != null) {
/* 64 */       Object object = this.convert.convert(this);
/* 65 */       this.targetField.setFieldValue(object);
/* 66 */       bool = true;
/* 67 */     } else if (this.convert != null) {
/* 68 */       Object object1 = this.sourceField.getFieldValue();
/* 69 */       Object object2 = this.convert.convert(object1);
/* 70 */       this.targetField.setFieldValue(object2);
/* 71 */       bool = true;
/*    */     } else {
/* 73 */       Object object1 = this.sourceField.getFieldValue();
/* 74 */       Object object2 = (new CommonConvert()).convert(object1);
/* 75 */       this.targetField.setFieldValue(object2);
/* 76 */       bool = true;
/*    */     } 
/*    */     
/* 79 */     return bool;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/mapping/impl/FieldDataMapping.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */