/*    */ package weaver.integration.framework.mapping.impl;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import weaver.integration.framework.data.GeneralData;
/*    */ import weaver.integration.framework.data.TableData;
/*    */ import weaver.integration.framework.mapping.IMapping;
/*    */ 
/*    */ public class RecordDataMapping
/*    */   implements IMapping {
/*    */   private List<FieldDataMapping> fieldDataMappingList;
/*    */   private List<GeneralData> generalDataList;
/*    */   private TableData tableData;
/*    */   
/*    */   public TableData getTableData() {
/* 16 */     return this.tableData;
/*    */   }
/*    */   
/*    */   public void setTableData(TableData paramTableData) {
/* 20 */     this.tableData = paramTableData;
/*    */   }
/*    */   
/*    */   public List<GeneralData> getGeneralDataList() {
/* 24 */     return this.generalDataList;
/*    */   }
/*    */   
/*    */   public void setGeneralDataList(List<GeneralData> paramList) {
/* 28 */     this.generalDataList = paramList;
/*    */   }
/*    */   
/*    */   public List<FieldDataMapping> getFieldDataMappingList() {
/* 32 */     return this.fieldDataMappingList;
/*    */   }
/*    */   
/*    */   public void setFieldDataMappingList(List<FieldDataMapping> paramList) {
/* 36 */     this.fieldDataMappingList = paramList;
/*    */   }
/*    */   
/*    */   public void addFieldDataMapping(FieldDataMapping paramFieldDataMapping) {
/* 40 */     if (this.fieldDataMappingList == null) {
/* 41 */       this.fieldDataMappingList = new ArrayList<>();
/*    */     }
/* 43 */     this.fieldDataMappingList.add(paramFieldDataMapping);
/*    */   }
/*    */   
/*    */   public boolean mapping() {
/* 47 */     boolean bool = true;
/* 48 */     for (FieldDataMapping fieldDataMapping : this.fieldDataMappingList) {
/* 49 */       if (!fieldDataMapping.mapping()) {
/* 50 */         bool = false;
/*    */         break;
/*    */       } 
/*    */     } 
/* 54 */     return bool;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/mapping/impl/RecordDataMapping.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */