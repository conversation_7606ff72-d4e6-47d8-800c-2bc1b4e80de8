/*    */ package weaver.integration.framework.mappingdefine.impl;
/*    */ 
/*    */ import weaver.integration.framework.Context;
/*    */ import weaver.integration.framework.converter.IConvert;
/*    */ import weaver.integration.framework.data.field.FieldData;
/*    */ import weaver.integration.framework.mapping.IMapping;
/*    */ import weaver.integration.framework.mapping.impl.FieldDataMapping;
/*    */ import weaver.integration.framework.mappingdefine.IMappingDefine;
/*    */ 
/*    */ public class FieldMappingDefine implements IMappingDefine {
/*    */   private FieldData sourceField;
/*    */   private FieldData targetField;
/*    */   
/*    */   public Context getContext() {
/* 15 */     return this.context;
/*    */   }
/*    */   private Context context; private IConvert convert;
/*    */   public void setContext(Context paramContext) {
/* 19 */     this.context = paramContext;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public FieldData getSourceField() {
/* 25 */     return this.sourceField;
/*    */   }
/*    */ 
/*    */   
/*    */   public void setSourceField(FieldData paramFieldData) {
/* 30 */     this.sourceField = paramFieldData;
/*    */   }
/*    */ 
/*    */   
/*    */   public FieldData getTargetField() {
/* 35 */     return this.targetField;
/*    */   }
/*    */ 
/*    */   
/*    */   public void setTargetField(FieldData paramFieldData) {
/* 40 */     this.targetField = paramFieldData;
/*    */   }
/*    */ 
/*    */   
/*    */   public IConvert getConvert() {
/* 45 */     return this.convert;
/*    */   }
/*    */ 
/*    */   
/*    */   public void setConvert(IConvert paramIConvert) {
/* 50 */     this.convert = paramIConvert;
/*    */   }
/*    */ 
/*    */   
/*    */   public FieldDataMapping toMapping() {
/* 55 */     FieldDataMapping fieldDataMapping = new FieldDataMapping();
/* 56 */     fieldDataMapping.setSourceField(this.sourceField);
/* 57 */     fieldDataMapping.setTargetField(this.targetField);
/* 58 */     fieldDataMapping.setConvert(this.convert);
/* 59 */     fieldDataMapping.setContext(this.context);
/*    */     
/* 61 */     return fieldDataMapping;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/mappingdefine/impl/FieldMappingDefine.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */