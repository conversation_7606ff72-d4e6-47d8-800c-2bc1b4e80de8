/*    */ package weaver.integration.framework.mappingdefine.impl;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import weaver.integration.framework.mapping.IMapping;
/*    */ import weaver.integration.framework.mapping.impl.GeneralDataMapping;
/*    */ import weaver.integration.framework.mapping.impl.TableDataMapping;
/*    */ import weaver.integration.framework.mappingdefine.IMappingDefine;
/*    */ 
/*    */ public class GeneralMappingDefine
/*    */   implements IMappingDefine
/*    */ {
/*    */   private List<TableMappingDefine> tableMappingDefineList;
/*    */   
/*    */   public List<TableMappingDefine> getTableMappingDefineList() {
/* 16 */     return this.tableMappingDefineList;
/*    */   }
/*    */ 
/*    */   
/*    */   public void setTableMappingDefineList(List<TableMappingDefine> paramList) {
/* 21 */     this.tableMappingDefineList = paramList;
/*    */   }
/*    */ 
/*    */   
/*    */   public IMapping toMapping() {
/* 26 */     GeneralDataMapping generalDataMapping = new GeneralDataMapping();
/*    */     
/* 28 */     ArrayList<TableDataMapping> arrayList = new ArrayList();
/* 29 */     for (TableMappingDefine tableMappingDefine : this.tableMappingDefineList) {
/* 30 */       arrayList.add((TableDataMapping)tableMappingDefine.toMapping());
/*    */     }
/* 32 */     generalDataMapping.setTableDataMappingList(arrayList);
/*    */     
/* 34 */     return (IMapping)generalDataMapping;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/mappingdefine/impl/GeneralMappingDefine.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */