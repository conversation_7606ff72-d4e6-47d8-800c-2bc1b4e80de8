/*    */ package weaver.integration.framework.mappingdefine.impl;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import weaver.integration.framework.mapping.IMapping;
/*    */ import weaver.integration.framework.mapping.impl.FieldDataMapping;
/*    */ import weaver.integration.framework.mapping.impl.TableDataMapping;
/*    */ import weaver.integration.framework.mappingdefine.IMappingDefine;
/*    */ 
/*    */ 
/*    */ public class TableMappingDefine
/*    */   implements IMappingDefine
/*    */ {
/*    */   private String sourceTableName;
/*    */   private String targetTableName;
/*    */   private String tableType;
/*    */   private int tableOrder;
/*    */   private List<FieldMappingDefine> fieldMappingDefineList;
/*    */   
/*    */   public String getSourceTableName() {
/* 21 */     return this.sourceTableName;
/*    */   }
/*    */ 
/*    */   
/*    */   public void setSourceTableName(String paramString) {
/* 26 */     this.sourceTableName = paramString;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getTargetTableName() {
/* 31 */     return this.targetTableName;
/*    */   }
/*    */ 
/*    */   
/*    */   public void setTargetTableName(String paramString) {
/* 36 */     this.targetTableName = paramString;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getTableType() {
/* 41 */     return this.tableType;
/*    */   }
/*    */ 
/*    */   
/*    */   public void setTableType(String paramString) {
/* 46 */     this.tableType = paramString;
/*    */   }
/*    */ 
/*    */   
/*    */   public int getTableOrder() {
/* 51 */     return this.tableOrder;
/*    */   }
/*    */ 
/*    */   
/*    */   public void setTableOrder(int paramInt) {
/* 56 */     this.tableOrder = paramInt;
/*    */   }
/*    */ 
/*    */   
/*    */   public List<FieldMappingDefine> getFieldMappingDefineList() {
/* 61 */     return this.fieldMappingDefineList;
/*    */   }
/*    */ 
/*    */   
/*    */   public void setFieldMappingDefineList(List<FieldMappingDefine> paramList) {
/* 66 */     this.fieldMappingDefineList = paramList;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public IMapping toMapping() {
/* 72 */     TableDataMapping tableDataMapping = new TableDataMapping();
/* 73 */     tableDataMapping.setSourceTableName(this.sourceTableName);
/* 74 */     tableDataMapping.setTargetTableName(this.targetTableName);
/* 75 */     tableDataMapping.setTableType(this.tableType);
/* 76 */     tableDataMapping.setTableOrder(this.tableOrder);
/*    */     
/* 78 */     ArrayList<FieldDataMapping> arrayList = new ArrayList();
/* 79 */     for (FieldMappingDefine fieldMappingDefine : this.fieldMappingDefineList) {
/* 80 */       arrayList.add(fieldMappingDefine.toMapping());
/*    */     }
/*    */     
/* 83 */     tableDataMapping.setTemplateFieldDataMappingList(arrayList);
/*    */     
/* 85 */     ArrayList arrayList1 = new ArrayList();
/* 86 */     tableDataMapping.setRecordDataMappingList(arrayList1);
/*    */     
/* 88 */     return (IMapping)tableDataMapping;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/mappingdefine/impl/TableMappingDefine.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */