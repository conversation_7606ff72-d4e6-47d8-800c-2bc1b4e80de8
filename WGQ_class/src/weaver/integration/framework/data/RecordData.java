/*    */ package weaver.integration.framework.data;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RecordData
/*    */ {
/* 10 */   private Map<String, Object> fieldData = new HashMap<>();
/*    */ 
/*    */   
/*    */   public Map<String, Object> getFieldData() {
/* 14 */     return this.fieldData;
/*    */   }
/*    */   
/*    */   public void setFieldData(Map<String, Object> paramMap) {
/* 18 */     this.fieldData = paramMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/data/RecordData.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */