/*    */ package weaver.integration.framework.data.record;
/*    */ 
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.integration.framework.data.field.FieldData;
/*    */ 
/*    */ 
/*    */ public class ComplexRecordData
/*    */   extends SimpleRecordData
/*    */ {
/*    */   private List<CommonRecordData> detailRecordList;
/*    */   
/*    */   public ComplexRecordData() {}
/*    */   
/*    */   public ComplexRecordData(String paramString, CommonRecordData paramCommonRecordData, List<CommonRecordData> paramList) {
/* 16 */     super(paramString, paramCommonRecordData);
/* 17 */     this.detailRecordList = paramList;
/*    */   }
/*    */   
/*    */   public List<CommonRecordData> getDetailRecordList() {
/* 21 */     return this.detailRecordList;
/*    */   }
/*    */   
/*    */   public void setDetailRecordList(List<CommonRecordData> paramList) {
/* 25 */     this.detailRecordList = paramList;
/*    */   }
/*    */   
/*    */   public String toString() {
/* 29 */     StringBuilder stringBuilder = new StringBuilder();
/* 30 */     for (Map.Entry<String, FieldData> entry : getRecordData().getFieldDataMap().entrySet()) {
/* 31 */       stringBuilder.append(entry.getValue()).append(",");
/*    */     }
/* 33 */     stringBuilder = new StringBuilder(stringBuilder.substring(0, stringBuilder.length() - 1));
/*    */     
/* 35 */     return "{" + 
/* 36 */       "\"action\":\"" + 
/* 37 */       getAction() + "\"," + "\"data\":" + 
/* 38 */       stringBuilder + "\"," + "\"detailRecordList\":[" + 
/* 39 */       "]" + 
/* 40 */       "}";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/data/record/ComplexRecordData.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */