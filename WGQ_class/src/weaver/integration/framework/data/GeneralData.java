/*    */ package weaver.integration.framework.data;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GeneralData
/*    */ {
/* 10 */   private List<TableData> tableDataList = new ArrayList<>();
/*    */   
/*    */   public List<TableData> getTableDataList() {
/* 13 */     return this.tableDataList;
/*    */   }
/*    */   
/*    */   public void setTableDataList(List<TableData> paramList) {
/* 17 */     this.tableDataList = paramList;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/data/GeneralData.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */