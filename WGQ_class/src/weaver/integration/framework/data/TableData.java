/*    */ package weaver.integration.framework.data;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class TableData
/*    */ {
/*    */   private String tableName;
/* 11 */   private List<RecordData> recordDataList = new ArrayList<>();
/*    */ 
/*    */   
/*    */   public String getTableName() {
/* 15 */     return this.tableName;
/*    */   }
/*    */   
/*    */   public void setTableName(String paramString) {
/* 19 */     this.tableName = paramString;
/*    */   }
/*    */   
/*    */   public List<RecordData> getRecordDataList() {
/* 23 */     return this.recordDataList;
/*    */   }
/*    */   
/*    */   public void setRecordDataList(List<RecordData> paramList) {
/* 27 */     this.recordDataList = paramList;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/data/TableData.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */