/*    */ package weaver.integration.framework.converter.common;
/*    */ 
/*    */ import java.util.Map;
/*    */ import weaver.integration.framework.converter.IConvert;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CommonConvert
/*    */   implements IConvert
/*    */ {
/* 17 */   private static Logger newlog = LoggerFactory.getLogger(CommonConvert.class);
/*    */   public Object convert(Object paramObject) {
/* 19 */     if (paramObject instanceof Map) {
/* 20 */       String str1 = (new StringBuilder()).append(((Map)paramObject).get("wffileddbtype")).append("").toString();
/* 21 */       String str2 = (String)((Map)paramObject).get("dbtype");
/*    */ 
/*    */       
/* 24 */       if (((Map)paramObject).get("sourcevalue") != null && str1 != null && (str1.toLowerCase().indexOf("time") > -1 || str1.toLowerCase().indexOf("date") > -1)) {
/* 25 */         String str = (new StringBuilder()).append(((Map)paramObject).get("sourcevalue")).append("").toString();
/* 26 */         return TimeFormatUtil.getTime(str, str1, str2);
/*    */       } 
/* 28 */       return ((Map)paramObject).get("sourcevalue");
/*    */     } 
/*    */ 
/*    */     
/* 32 */     return paramObject;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/common/CommonConvert.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */