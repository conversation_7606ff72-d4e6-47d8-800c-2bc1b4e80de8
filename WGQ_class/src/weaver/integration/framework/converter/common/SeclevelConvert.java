/*    */ package weaver.integration.framework.converter.common;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.integration.framework.converter.IConvert;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SeclevelConvert
/*    */   implements IConvert
/*    */ {
/* 19 */   private static Logger newlog = LoggerFactory.getLogger(SeclevelConvert.class);
/*    */   
/*    */   public Object convert(Object paramObject) {
/* 22 */     String str1 = Util.null2String(((Map)paramObject).get("datasourceid"));
/* 23 */     String str2 = Util.null2String(((Map)paramObject).get("customsql"));
/* 24 */     String str3 = Util.null2String(((Map)paramObject).get("sourcevalue"));
/* 25 */     String str4 = Util.null2String(((Map)paramObject).get("changetype"));
/* 26 */     String str5 = "";
/* 27 */     if ("6".equals(str4)) {
/* 28 */       SqlConvert sqlConvert = new SqlConvert();
/* 29 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 30 */       hashMap.put("datasourceid", str1);
/* 31 */       hashMap.put("transsql", str2);
/* 32 */       hashMap.put("sourcevalue", str3);
/* 33 */       str5 = Util.null2String(sqlConvert.convert(hashMap));
/*    */     } else {
/* 35 */       str5 = str2;
/*    */     } 
/* 37 */     if (str5 == null || "".equals(str5)) {
/* 38 */       str5 = "4";
/*    */     }
/* 40 */     if (!"1".equals(str5) && !"2".equals(str5) && !"3".equals(str5) && !"4".equals(str5)) {
/* 41 */       str5 = "4";
/*    */     }
/*    */ 
/*    */     
/* 45 */     return str5;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/common/SeclevelConvert.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */