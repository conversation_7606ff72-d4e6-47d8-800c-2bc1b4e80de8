/*    */ package weaver.integration.framework.converter.common;
/*    */ 
/*    */ import java.util.Map;
/*    */ import weaver.integration.framework.converter.IConvert;
/*    */ import weaver.integration.framework.converter.service.TransInterface;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CustomClassConvert
/*    */   implements IConvert
/*    */ {
/* 16 */   private static Logger newlog = LoggerFactory.getLogger(CustomClassConvert.class);
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Object convert(Object paramObject) {
/* 22 */     String str = (new StringBuilder()).append(((Map)paramObject).get("customsql")).append("").toString();
/*    */     try {
/* 24 */       TransInterface transInterface = (TransInterface)Class.forName(str).newInstance();
/* 25 */       str = transInterface.getTransdata((Map)paramObject);
/* 26 */     } catch (InstantiationException instantiationException) {
/* 27 */       instantiationException.printStackTrace();
/* 28 */       newlog.error(instantiationException);
/* 29 */     } catch (IllegalAccessException illegalAccessException) {
/* 30 */       illegalAccessException.printStackTrace();
/* 31 */       newlog.error(illegalAccessException);
/* 32 */     } catch (ClassNotFoundException classNotFoundException) {
/* 33 */       classNotFoundException.printStackTrace();
/* 34 */       newlog.error("自定义转换类(" + str + ")在OA环境未正确部署");
/*    */     } 
/* 36 */     return str;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/common/CustomClassConvert.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */