/*    */ package weaver.integration.framework.converter.common;
/*    */ 
/*    */ import java.util.Map;
/*    */ import weaver.integration.framework.converter.IConvert;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ import weaver.interfaces.workflow.browser.util.hrmBaseInfoUtil;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FixedValueConvert
/*    */   implements IConvert
/*    */ {
/* 16 */   private static Logger newlog = LoggerFactory.getLogger(FixedValueConvert.class);
/*    */ 
/*    */   
/*    */   public Object convert(Object paramObject) {
/* 20 */     String str1 = (new StringBuilder()).append(((Map)paramObject).get("createrid")).append("").toString();
/* 21 */     String str2 = (new StringBuilder()).append(((Map)paramObject).get("customsql")).append("").toString();
/* 22 */     str2 = hrmBaseInfoUtil.replaceDefaultValue(str1, str2);
/*    */     
/* 24 */     return str2;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/common/FixedValueConvert.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */