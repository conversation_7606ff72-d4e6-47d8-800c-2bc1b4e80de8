/*    */ package weaver.integration.framework.converter.common;
/*    */ 
/*    */ import java.util.Calendar;
/*    */ import java.util.Map;
/*    */ import weaver.general.DateUtil;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.resource.ResourceComInfo;
/*    */ import weaver.integration.framework.converter.IConvert;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ import weaver.workflow.workflow.WorkflowComInfo;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RequestNameConvert
/*    */   implements IConvert
/*    */ {
/* 25 */   private static Logger newlog = LoggerFactory.getLogger(RequestNameConvert.class);
/*    */   public Object convert(Object paramObject) {
/* 27 */     String str1 = Util.null2String(((Map)paramObject).get("workflowid"));
/* 28 */     String str2 = Util.null2String(((Map)paramObject).get("createrid"));
/* 29 */     String str3 = Util.null2String(((Map)paramObject).get("sourcevalue"));
/* 30 */     String str4 = Util.null2String(((Map)paramObject).get("sourceFieldName"));
/* 31 */     WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/* 32 */     ResourceComInfo resourceComInfo = null;
/*    */     try {
/* 34 */       resourceComInfo = new ResourceComInfo();
/* 35 */     } catch (Exception exception) {
/* 36 */       exception.printStackTrace();
/*    */     } 
/*    */     
/* 39 */     String str5 = "";
/* 40 */     String str6 = Util.null2String(workflowComInfo.getWorkflowname(str1));
/* 41 */     String str7 = Util.toScreen(resourceComInfo.getResourcename(str2), 7);
/* 42 */     String str8 = "";
/* 43 */     Calendar calendar = Calendar.getInstance();
/*    */ 
/*    */     
/* 46 */     str8 = Util.add0(calendar.get(1), 4) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/* 47 */     DateUtil dateUtil = new DateUtil();
/*    */     
/* 49 */     if (str4.equals("")) {
/*    */ 
/*    */       
/* 52 */       if (str5.equals("")) {
/* 53 */         str5 = str6 + "-" + str7 + "-" + str8;
/*    */       }
/*    */     } else {
/* 56 */       String str = str3;
/* 57 */       if (!str.equals("")) {
/* 58 */         str5 = str;
/*    */       
/*    */       }
/* 61 */       else if (str5.equals("")) {
/* 62 */         str5 = str6 + "-" + str7 + "-" + str8;
/*    */       } 
/*    */     } 
/*    */ 
/*    */     
/* 67 */     return str5;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/common/RequestNameConvert.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */