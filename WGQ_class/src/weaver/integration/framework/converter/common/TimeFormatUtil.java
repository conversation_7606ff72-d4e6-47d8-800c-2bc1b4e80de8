/*    */ package weaver.integration.framework.converter.common;
/*    */ 
/*    */ import java.text.DateFormat;
/*    */ import java.text.SimpleDateFormat;
/*    */ import java.util.Date;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class TimeFormatUtil
/*    */ {
/* 13 */   static SimpleDateFormat datetime2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSSSSSS");
/* 14 */   static SimpleDateFormat timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSSSSS");
/* 15 */   static SimpleDateFormat datetime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
/* 16 */   static SimpleDateFormat simple = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/*    */   
/* 18 */   static SimpleDateFormat date = new SimpleDateFormat("yyyy-MM-dd");
/* 19 */   static DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 20 */   static DateFormat df_date = new SimpleDateFormat("yyyy-MM-dd");
/*    */   
/*    */   public static String getTime(String paramString1, String paramString2, String paramString3) {
/* 23 */     if (paramString1 == null || paramString1.equals("")) {
/* 24 */       return paramString1;
/*    */     }
/*    */     try {
/* 27 */       if (paramString3.toLowerCase().indexOf("sqlserver") > -1) {
/* 28 */         if (paramString2.toLowerCase().indexOf("datetime2") > -1) {
/*    */           
/* 30 */           Date date2 = df.parse(paramString1);
/* 31 */           return datetime2.format(date2);
/*    */         } 
/* 33 */         if (paramString2.toLowerCase().indexOf("smalldatetime") <= -1 && paramString2.toLowerCase().indexOf("datetime") > -1) {
/*    */           
/* 35 */           Date date2 = df.parse(paramString1);
/* 36 */           return datetime.format(date2);
/* 37 */         }  if (paramString2.toLowerCase().indexOf("smalldatetime") <= -1 && paramString2.toLowerCase().indexOf("date") > -1) {
/*    */           
/* 39 */           Date date2 = df_date.parse(paramString1);
/* 40 */           return date.format(date2);
/*    */         } 
/*    */         
/* 43 */         Date date1 = df.parse(paramString1);
/* 44 */         return simple.format(date1);
/*    */       } 
/* 46 */       if (paramString3.toLowerCase().indexOf("oracle") > -1) {
/* 47 */         if (paramString2.toLowerCase().indexOf("timestamp") > -1) {
/*    */           
/* 49 */           Date date2 = df.parse(paramString1);
/* 50 */           return timestamp.format(date2);
/*    */         } 
/*    */         
/* 53 */         Date date1 = df.parse(paramString1);
/* 54 */         return simple.format(date1);
/*    */       } 
/* 56 */       if (paramString3.toLowerCase().indexOf("mysql") > -1) {
/* 57 */         if (paramString2.toLowerCase().indexOf("datetime") <= -1 && paramString2.toLowerCase().indexOf("date") > -1) {
/*    */           
/* 59 */           Date date2 = df_date.parse(paramString1);
/* 60 */           return date.format(date2);
/*    */         } 
/*    */         
/* 63 */         Date date1 = df.parse(paramString1);
/* 64 */         return simple.format(date1);
/*    */       } 
/* 66 */       if (paramString3.toLowerCase().indexOf("dm") > -1) {
/*    */         
/* 68 */         Date date1 = df.parse(paramString1);
/* 69 */         return timestamp.format(date1);
/* 70 */       }  if (paramString3.toLowerCase().indexOf("st") > -1) {
/* 71 */         if (paramString2.toLowerCase().indexOf("time(") > -1)
/* 72 */           return paramString1; 
/* 73 */         if (paramString2.toLowerCase().indexOf("timestamp") > -1)
/* 74 */           return paramString1; 
/* 75 */         if (paramString2.toLowerCase().indexOf("date") > -1) {
/* 76 */           return paramString1;
/*    */         }
/*    */         
/* 79 */         Date date1 = df.parse(paramString1);
/* 80 */         return simple.format(date1);
/*    */       } 
/*    */ 
/*    */       
/* 84 */       Date date = df.parse(paramString1);
/* 85 */       return simple.format(date);
/*    */     }
/* 87 */     catch (Exception exception) {
/* 88 */       exception.printStackTrace();
/* 89 */       return null;
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/common/TimeFormatUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */