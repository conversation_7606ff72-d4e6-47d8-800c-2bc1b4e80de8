/*    */ package weaver.integration.framework.converter.common;
/*    */ 
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.conn.RecordSetDataSource;
/*    */ import weaver.general.Util;
/*    */ import weaver.integration.framework.converter.IConvert;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ import weaver.workflow.dmlaction.DBTypeUtil;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SqlConvert
/*    */   implements IConvert
/*    */ {
/* 17 */   private static Logger newlog = LoggerFactory.getLogger(SqlConvert.class);
/*    */   public Object convert(Object paramObject) {
/* 19 */     if (paramObject instanceof Map) {
/* 20 */       String str1 = Util.null2String(((Map)paramObject).get("datasourceid"));
/* 21 */       String str2 = Util.null2String(((Map)paramObject).get("customsql"));
/* 22 */       String str3 = Util.null2String(((Map)paramObject).get("sourcevalue"));
/* 23 */       String str4 = DBTypeUtil.replaceString(str2, "{?currentvalue}", str3);
/*    */       
/* 25 */       if (str1.equals("")) {
/* 26 */         RecordSet recordSet = new RecordSet();
/* 27 */         recordSet.executeSql(str4);
/* 28 */         if (recordSet.next()) {
/* 29 */           str3 = recordSet.getString(1);
/*    */         } else {
/* 31 */           str3 = "";
/*    */         } 
/*    */       } else {
/* 34 */         RecordSetDataSource recordSetDataSource = new RecordSetDataSource(str1);
/* 35 */         recordSetDataSource.executeSql(str4);
/* 36 */         if (recordSetDataSource.next()) {
/* 37 */           str3 = recordSetDataSource.getString(1);
/*    */         } else {
/* 39 */           str3 = "";
/*    */         } 
/*    */       } 
/*    */ 
/*    */ 
/*    */       
/* 45 */       return str3;
/*    */     } 
/* 47 */     return paramObject;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/common/SqlConvert.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */