/*    */ package weaver.integration.framework.converter.service.impl;
/*    */ 
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.integration.framework.converter.service.TransInterface;
/*    */ 
/*    */ public class TestTransInterfaceImpl
/*    */   implements TransInterface
/*    */ {
/*    */   public String getTransdata(Map paramMap) {
/* 11 */     return Util.null2String(paramMap.get("sourcevalue"));
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/service/impl/TestTransInterfaceImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */