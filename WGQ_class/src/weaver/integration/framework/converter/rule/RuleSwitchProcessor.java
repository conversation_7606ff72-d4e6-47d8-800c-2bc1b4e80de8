/*    */ package weaver.integration.framework.converter.rule;
/*    */ 
/*    */ import com.alibaba.fastjson.JSON;
/*    */ import com.alibaba.fastjson.JSONObject;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RuleSwitchProcessor
/*    */ {
/* 17 */   public static RuleSwitchProcessor ruleSwitchProcessor = new RuleSwitchProcessor();
/* 18 */   public static Map ruleMap = null;
/* 19 */   public static Map ruledtMap = null;
/* 20 */   private Logger logger = LoggerFactory.getLogger(getClass());
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static RuleSwitchProcessor getInstance() {
/* 29 */     if (ruleMap == null || ruledtMap == null) {
/* 30 */       ruleMap = new HashMap<>();
/* 31 */       ruledtMap = new HashMap<>();
/* 32 */       RecordSet recordSet = new RecordSet();
/* 33 */       recordSet.executeSql("select * from  Int_field_type_convert ");
/* 34 */       while (recordSet.next()) {
/* 35 */         String str1 = Util.null2String(recordSet.getString("ConvertClass"));
/* 36 */         String str2 = Util.null2String(recordSet.getString("TransType"));
/* 37 */         String str3 = Util.null2String(recordSet.getString("fieldhtmltype"));
/* 38 */         String str4 = Util.null2String(recordSet.getString("fieldtype"), "*");
/* 39 */         String str5 = Util.null2String(recordSet.getString("ChangeType"));
/* 40 */         ruleMap.put(str2, str1);
/* 41 */         if (!str5.equals("")) {
/* 42 */           ruledtMap.put(str3 + str4 + str5, str2);
/*    */         }
/*    */       } 
/*    */     } 
/* 46 */     return ruleSwitchProcessor;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getRule(Map paramMap) {
/* 56 */     String str1 = "";
/* 57 */     String str2 = "";
/* 58 */     String str3 = Util.null2String(paramMap.get("fieldId"));
/* 59 */     String str4 = Util.null2String(paramMap.get("changetype"));
/* 60 */     String str5 = Util.null2String(paramMap.get("fieldhtmltype"));
/* 61 */     String str6 = Util.null2String(paramMap.get("customsql"));
/* 62 */     String str7 = Util.null2String(paramMap.get("fieldtype")).equals("") ? "*" : Util.null2String(paramMap.get("fieldtype"));
/* 63 */     String str8 = Util.null2String(paramMap.get("attachment_settings"));
/* 64 */     int i = 0;
/* 65 */     if (!"".equals(str8)) {
/* 66 */       JSONObject jSONObject = JSON.parseObject(str8);
/* 67 */       i = Util.getIntValue(jSONObject.getString("attachment_type"), -1);
/*    */     } 
/* 69 */     if (str5.equals("6")) {
/* 70 */       str4 = i + "";
/*    */     }
/* 72 */     String str9 = str5 + str7 + str4;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 79 */     str2 = Util.null2String(ruledtMap.get(str9));
/* 80 */     if (str2.equals("")) {
/* 81 */       if (str3.equals("-1")) {
/* 82 */         str2 = "common_requestName";
/* 83 */       } else if (str3.equals("-3")) {
/* 84 */         str2 = "common_Seclevel";
/* 85 */       } else if ("7".equals(str4)) {
/* 86 */         str2 = "common_fixedValue";
/* 87 */       } else if (!"".equals(str6) && !"7".equals(str4) && "6".equals(str4)) {
/* 88 */         str2 = "common_sql";
/*    */       } else {
/* 90 */         str2 = "common_originalvalue";
/*    */       } 
/*    */     }
/* 93 */     str1 = Util.null2String(ruleMap.get(str2));
/* 94 */     return str1;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/rule/RuleSwitchProcessor.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */