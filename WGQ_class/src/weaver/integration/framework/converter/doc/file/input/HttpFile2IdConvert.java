/*     */ package weaver.integration.framework.converter.doc.file.input;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.InputStream;
/*     */ import java.net.HttpURLConnection;
/*     */ import java.net.URL;
/*     */ import java.net.URLConnection;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.framework.converter.IConvert;
/*     */ import weaver.integration.framework.converter.doc.file.util.DocUtil;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HttpFile2IdConvert
/*     */   implements IConvert
/*     */ {
/*  23 */   private static Logger logger = LoggerFactory.getLogger(HttpFile2IdConvert.class);
/*     */   public Object convert(Object paramObject) {
/*  25 */     String str1 = "";
/*  26 */     String str2 = Util.null2String(((Map)paramObject).get("imgFileName"));
/*  27 */     String str3 = Util.null2String(((Map)paramObject).get("createrid"));
/*  28 */     String str4 = Util.null2String(((Map)paramObject).get("workflowid"));
/*  29 */     String str5 = Util.null2String(((Map)paramObject).get("fieldid"));
/*  30 */     String str6 = Util.null2String(((Map)paramObject).get("attachment_settings"));
/*  31 */     String str7 = Util.null2String(((Map)paramObject).get("fieldpath"));
/*  32 */     JSONObject jSONObject = JSON.parseObject(str6);
/*  33 */     String str8 = Util.null2String(jSONObject.getString("url_prefix"));
/*  34 */     String str9 = Util.null2String(jSONObject.getString("url_split"));
/*     */     
/*  36 */     logger.info(" 文件名称 ：" + str2 + " | 文件路径：" + str7);
/*  37 */     if (str9.equals("")) {
/*  38 */       str1 = getData(str3, str4, str5, str7, str8, str2);
/*     */     } else {
/*  40 */       str9 = str9.replace("|", "\\|");
/*  41 */       String[] arrayOfString1 = str7.split(str9);
/*  42 */       String[] arrayOfString2 = str2.split(str9);
/*  43 */       logger.info(" 路径长度 ：" + arrayOfString1.length + " | 名称长度：" + arrayOfString2.length);
/*  44 */       if (str2.equals("") || arrayOfString2.length == 0 || arrayOfString2.length == arrayOfString1.length) {
/*  45 */         for (byte b = 0; b < arrayOfString1.length; b++) {
/*  46 */           String str = arrayOfString1[b];
/*  47 */           if (str2.equals("")) {
/*  48 */             String str10 = getData(str3, str4, str5, str, str8, "");
/*  49 */             if (str10 != null && !"".equals(str10)) {
/*  50 */               str1 = str1 + str10 + ",";
/*     */             }
/*     */           } else {
/*  53 */             String str10 = arrayOfString2[b];
/*  54 */             String str11 = getData(str3, str4, str5, str, str8, str10);
/*  55 */             if (str11 != null && !"".equals(str11)) {
/*  56 */               str1 = str1 + str11 + ",";
/*     */             }
/*     */           } 
/*     */         } 
/*     */       } else {
/*     */         
/*  62 */         logger.error("文件路径和文件名称长度不一致，不触发");
/*     */       } 
/*     */     } 
/*     */     
/*  66 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getData(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) {
/*  80 */     String str = "";
/*  81 */     byte[] arrayOfByte = null;
/*  82 */     Object object1 = null;
/*  83 */     InputStream inputStream = null;
/*  84 */     Object object2 = null;
/*  85 */     ByteArrayOutputStream byteArrayOutputStream = null;
/*     */     try {
/*  87 */       String str1 = "";
/*  88 */       if (!"".equals(paramString5)) {
/*  89 */         if (!paramString5.endsWith("/") && !paramString4.startsWith("/")) {
/*  90 */           paramString4 = paramString5 + "/" + paramString4;
/*     */         } else {
/*  92 */           paramString4 = paramString5 + paramString4;
/*     */         } 
/*     */       }
/*  95 */       str1 = paramString4;
/*     */       
/*  97 */       logger.info(" URL    文件路径：" + str1 + " | imgFileName :" + paramString6);
/*  98 */       URL uRL = new URL(str1);
/*  99 */       URLConnection uRLConnection = uRL.openConnection();
/* 100 */       uRLConnection.connect();
/* 101 */       HttpURLConnection httpURLConnection = (HttpURLConnection)uRLConnection;
/* 102 */       int i = httpURLConnection.getResponseCode();
/* 103 */       if (i != 200) {
/* 104 */         logger.error("===========getAttachmentId error, the url has no connect!!!!, url is:" + str1);
/*     */       } else {
/* 106 */         int j = uRLConnection.getContentLength();
/* 107 */         inputStream = uRLConnection.getInputStream();
/* 108 */         byteArrayOutputStream = new ByteArrayOutputStream();
/* 109 */         byte[] arrayOfByte1 = new byte[2048];
/* 110 */         int k = 0;
/* 111 */         while ((k = inputStream.read(arrayOfByte1)) != -1) {
/* 112 */           byteArrayOutputStream.write(arrayOfByte1, 0, k);
/*     */         }
/* 114 */         arrayOfByte = byteArrayOutputStream.toByteArray();
/* 115 */         if ("".equals(paramString6)) {
/* 116 */           String str2 = uRL.getFile();
/* 117 */           if (str2 != null) {
/* 118 */             int m = str2.lastIndexOf("/");
/* 119 */             if (m >= 0 && str2.length() > m) {
/* 120 */               str2 = str2.substring(m + 1);
/*     */             }
/* 122 */             paramString6 = str2;
/*     */           } 
/*     */         } 
/* 125 */         DocUtil docUtil = new DocUtil();
/* 126 */         str = docUtil.getDoc(arrayOfByte, paramString6, paramString1, paramString2, paramString3);
/*     */       } 
/* 128 */     } catch (Exception exception) {
/* 129 */       exception.printStackTrace();
/* 130 */       logger.error(exception);
/*     */     } 
/*     */     
/* 133 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/doc/file/input/HttpFile2IdConvert.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */