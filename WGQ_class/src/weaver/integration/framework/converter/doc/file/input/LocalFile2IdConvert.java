/*    */ package weaver.integration.framework.converter.doc.file.input;
/*    */ 
/*    */ import com.alibaba.fastjson.JSON;
/*    */ import com.alibaba.fastjson.JSONObject;
/*    */ import java.io.File;
/*    */ import java.nio.file.Files;
/*    */ import java.nio.file.Paths;
/*    */ import java.util.Map;
/*    */ import weaver.general.Util;
/*    */ import weaver.integration.framework.converter.IConvert;
/*    */ import weaver.integration.framework.converter.doc.file.util.DocUtil;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ 
/*    */ public class LocalFile2IdConvert
/*    */   implements IConvert
/*    */ {
/* 18 */   private static Logger logger = LoggerFactory.getLogger(LocalFile2IdConvert.class);
/*    */   public Object convert(Object paramObject) {
/* 20 */     String str1 = Util.null2String(((Map)paramObject).get("imgFileName"));
/* 21 */     String str2 = Util.null2String(((Map)paramObject).get("createrid"));
/* 22 */     String str3 = Util.null2String(((Map)paramObject).get("workflowid"));
/* 23 */     String str4 = Util.null2String(((Map)paramObject).get("fieldid"));
/* 24 */     String str5 = Util.null2String(((Map)paramObject).get("attachment_settings"));
/*    */     
/* 26 */     JSONObject jSONObject = JSON.parseObject(str5);
/* 27 */     String str6 = Util.null2String(((Map)paramObject).get("fieldpath"));
/* 28 */     String str7 = Util.null2String(jSONObject.getString("local_dir"));
/* 29 */     String str8 = Util.null2String(jSONObject.getString("local_split"));
/* 30 */     logger.info(" 文件名称 ：" + str1 + " | 文件路径：" + str6 + " | 分隔符：" + str8);
/* 31 */     String str9 = "";
/* 32 */     if (str8.equals("")) {
/* 33 */       str9 = getData(str2, str3, str4, str6, str7, str1);
/*    */     } else {
/* 35 */       str8 = str8.replace("|", "\\|");
/* 36 */       String[] arrayOfString1 = str6.split(str8);
/* 37 */       String[] arrayOfString2 = str1.split(str8);
/* 38 */       logger.info(" 路径长度 ：" + arrayOfString1.length + " | 名称长度：" + arrayOfString2.length);
/* 39 */       if (str1.equals("") || arrayOfString2.length == arrayOfString1.length) {
/* 40 */         for (byte b = 0; b < arrayOfString1.length; b++) {
/* 41 */           String str = arrayOfString1[b];
/* 42 */           if (str1.equals("")) {
/* 43 */             String str10 = getData(str2, str3, str4, str, str7, "");
/* 44 */             str9 = str9 + str10 + ",";
/*    */           } else {
/* 46 */             String str10 = arrayOfString2[b];
/* 47 */             String str11 = getData(str2, str3, str4, str, str7, str10);
/* 48 */             str9 = str9 + str11 + ",";
/*    */           } 
/*    */         } 
/*    */       } else {
/*    */         
/* 53 */         logger.error("文件路径和文件名称长度不一致，不触发");
/*    */       } 
/*    */     } 
/* 56 */     return str9;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getData(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) {
/* 61 */     byte[] arrayOfByte = null;
/* 62 */     String str = "";
/*    */     try {
/* 64 */       if (!"".equals(paramString5)) {
/* 65 */         if (!paramString5.endsWith("/") && !paramString4.startsWith("/")) {
/* 66 */           paramString4 = paramString5 + "/" + paramString4;
/*    */         } else {
/* 68 */           paramString4 = paramString5 + paramString4;
/*    */         } 
/*    */       }
/*    */       
/* 72 */       logger.info(" URL    文件路径：" + paramString4 + " | imgFileName :" + paramString6);
/* 73 */       File file = new File(paramString4);
/* 74 */       if (file.exists() && file.isFile()) {
/* 75 */         arrayOfByte = Files.readAllBytes(Paths.get(file.getAbsolutePath(), new String[0]));
/* 76 */         if ("".equals(paramString6)) {
/* 77 */           String str1 = file.getName();
/* 78 */           if (str1 != null) {
/* 79 */             int i = str1.lastIndexOf("/");
/* 80 */             if (i >= 0 && str1.length() > i) {
/* 81 */               str1 = str1.substring(i + 1);
/*    */             }
/* 83 */             paramString6 = str1;
/*    */           } 
/*    */         } 
/*    */       } 
/*    */       
/* 88 */       DocUtil docUtil = new DocUtil();
/* 89 */       str = docUtil.getDoc(arrayOfByte, paramString6, paramString1, paramString2, paramString3);
/*    */     }
/* 91 */     catch (Exception exception) {
/* 92 */       exception.printStackTrace();
/* 93 */       logger.error(exception);
/*    */     } 
/* 95 */     return str;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/doc/file/input/LocalFile2IdConvert.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */