/*     */ package weaver.integration.framework.converter.doc.file.input;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.InputStream;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.framework.converter.IConvert;
/*     */ import weaver.integration.framework.converter.doc.file.util.DocUtil;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.util.FTPUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FtpFile2IdConvert
/*     */   implements IConvert
/*     */ {
/*  24 */   private static Logger logger = LoggerFactory.getLogger(FtpFile2IdConvert.class);
/*     */   public Object convert(Object paramObject) {
/*  26 */     String str1 = Util.null2String(((Map)paramObject).get("imgFileName"));
/*  27 */     String str2 = Util.null2String(((Map)paramObject).get("createrid"));
/*  28 */     String str3 = Util.null2String(((Map)paramObject).get("workflowid"));
/*  29 */     String str4 = Util.null2String(((Map)paramObject).get("fieldid"));
/*  30 */     String str5 = Util.null2String(((Map)paramObject).get("attachment_settings"));
/*  31 */     JSONObject jSONObject = JSON.parseObject(str5);
/*     */     
/*  33 */     String str6 = "";
/*  34 */     String str7 = Util.null2String(((Map)paramObject).get("fieldpath"));
/*  35 */     String str8 = Util.null2String(jSONObject.getString("ftp_addr"));
/*  36 */     String str9 = Util.null2String(jSONObject.getString("ftp_dir"));
/*  37 */     String str10 = Util.null2String(jSONObject.getString("ftp_port"));
/*  38 */     String str11 = Util.null2String(jSONObject.getString("ftp_pwd"));
/*  39 */     String str12 = Util.null2String(jSONObject.getString("ftp_username"));
/*  40 */     String str13 = Util.null2String(jSONObject.getString("ftp_split"));
/*  41 */     if (str13.equals("")) {
/*  42 */       str6 = getData(str2, str3, str4, str9, str7, str8, str10, str11, str12, str1);
/*     */     } else {
/*  44 */       str13 = str13.replace("|", "\\|");
/*  45 */       String[] arrayOfString1 = str7.split(str13);
/*  46 */       String[] arrayOfString2 = str1.split(str13);
/*  47 */       if (str1.equals("") || arrayOfString2.length == 0 || arrayOfString2.length == arrayOfString1.length) {
/*  48 */         for (byte b = 0; b < arrayOfString1.length; b++) {
/*  49 */           String str = arrayOfString1[b];
/*  50 */           if (str1.equals("")) {
/*  51 */             str6 = str6 + getData(str2, str3, str4, str9, str, str8, str10, str11, str12, "") + ",";
/*     */           } else {
/*  53 */             String str14 = arrayOfString2[b];
/*  54 */             str6 = str6 + getData(str2, str3, str4, str9, str, str8, str10, str11, str12, str14) + ",";
/*     */           } 
/*     */         } 
/*     */       } else {
/*  58 */         logger.error("文件路径和文件名称长度不一致，不触发");
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/*  63 */     return str6;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getData(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8, String paramString9, String paramString10) {
/*  81 */     String str1 = "";
/*  82 */     String str2 = "/";
/*  83 */     InputStream inputStream = null;
/*  84 */     ByteArrayOutputStream byteArrayOutputStream = null;
/*  85 */     byte[] arrayOfByte = null;
/*     */     try {
/*  87 */       if (!"".equals(paramString4)) {
/*  88 */         if (!paramString4.endsWith("/") && !paramString5.startsWith("/")) {
/*  89 */           paramString5 = paramString4 + "/" + paramString5;
/*     */         } else {
/*  91 */           paramString5 = paramString4 + paramString5;
/*     */         } 
/*     */       }
/*  94 */       String str = "";
/*  95 */       if (paramString5.lastIndexOf("/") >= 0 && paramString5.lastIndexOf("/") < paramString5.length()) {
/*  96 */         str = paramString5.substring(paramString5.lastIndexOf("/") + 1, paramString5.length());
/*  97 */         str2 = paramString5.substring(0, paramString5.lastIndexOf("/") + 1);
/*     */       } 
/*  99 */       FTPUtil fTPUtil = new FTPUtil(paramString6, Util.getIntValue(paramString7, 21), paramString9, paramString8);
/*     */       
/* 101 */       logger.info("ftp 相关信息 ：  ftp_addr ： " + paramString6 + " | ftp_port :" + paramString7 + " | ftp_username :" + paramString9 + " | ftp_pwd :" + paramString8);
/*     */       
/* 103 */       logger.info("ftp  文件路径 ：  :" + str2 + " |  文件名称 ：" + paramString10);
/* 104 */       boolean bool = fTPUtil.login();
/* 105 */       if (bool) {
/* 106 */         boolean bool1 = fTPUtil.getFtpClient().changeWorkingDirectory(str2);
/* 107 */         if (bool1) {
/* 108 */           inputStream = fTPUtil.getFtpClient().retrieveFileStream(str);
/* 109 */           byteArrayOutputStream = new ByteArrayOutputStream();
/* 110 */           byte[] arrayOfByte1 = new byte[2048];
/* 111 */           int i = 0;
/* 112 */           while ((i = inputStream.read(arrayOfByte1)) != -1) {
/* 113 */             byteArrayOutputStream.write(arrayOfByte1, 0, i);
/*     */           }
/* 115 */           arrayOfByte = byteArrayOutputStream.toByteArray();
/*     */           
/* 117 */           if ("".equals(paramString10)) {
/* 118 */             paramString10 = str;
/*     */           }
/*     */           
/* 121 */           DocUtil docUtil = new DocUtil();
/* 122 */           str1 = docUtil.getDoc(arrayOfByte, paramString10, paramString1, paramString2, paramString3);
/*     */         } else {
/* 124 */           logger.error("===========getAttachmentId error occured! the path:" + str2 + " is not exists!!!");
/*     */         } 
/*     */       } else {
/*     */         
/* 128 */         logger.error("===========getAttachmentId error occured! FTP logined failure!!!");
/*     */       } 
/* 130 */     } catch (Exception exception) {
/* 131 */       exception.printStackTrace();
/* 132 */       logger.error(exception);
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 137 */     return str1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/doc/file/input/FtpFile2IdConvert.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */