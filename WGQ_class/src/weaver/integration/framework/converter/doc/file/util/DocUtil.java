/*     */ package weaver.integration.framework.converter.doc.file.util;
/*     */ 
/*     */ import com.api.doc.detail.service.DocSaveService;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.ImageFileManager;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ public class DocUtil
/*     */ {
/*  13 */   private static Logger newlog = LoggerFactory.getLogger(DocUtil.class);
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDoc(byte[] paramArrayOfbyte, String paramString1, String paramString2, String paramString3, String paramString4) {
/*  18 */     int i = 0;
/*  19 */     String str = getWorkflowUploadDocCategory(paramString3, paramString4);
/*  20 */     DocSaveService docSaveService = new DocSaveService();
/*  21 */     ImageFileManager imageFileManager = new ImageFileManager();
/*  22 */     imageFileManager.resetParameter();
/*  23 */     imageFileManager.setImagFileName(paramString1);
/*  24 */     imageFileManager.setData(paramArrayOfbyte);
/*  25 */     int j = imageFileManager.saveImageFile();
/*     */     try {
/*  27 */       i = docSaveService.accForDoc(Util.getIntValue(str, -1), j, getUser(paramString2));
/*  28 */     } catch (Exception exception) {
/*  29 */       exception.printStackTrace();
/*  30 */       newlog.error(exception);
/*     */     } 
/*  32 */     return i + "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getWorkflowUploadDocCategory(String paramString1, String paramString2) {
/*  42 */     RecordSet recordSet = new RecordSet();
/*  43 */     String str = "";
/*     */     
/*  45 */     recordSet.executeQuery("select * from workflow_fileupload where workflowid=" + paramString1 + " and fieldid=" + paramString2, new Object[0]);
/*  46 */     if (recordSet.next()) {
/*  47 */       str = recordSet.getString("docCategory");
/*     */     }
/*  49 */     if ("".equals(str)) {
/*     */       
/*  51 */       recordSet.executeQuery("select messageType,chatsType,catelogtype,docCategory,selectedCateLog,limitvalue from workflow_base where id=" + paramString1, new Object[0]);
/*  52 */       if (recordSet.next()) {
/*  53 */         str = recordSet.getString("docCategory");
/*     */       }
/*     */     } 
/*  56 */     if (!"".equals(str) && str.lastIndexOf(",") > -1) {
/*  57 */       str = str.substring(str.lastIndexOf(",") + 1, str.length());
/*     */     }
/*     */     
/*  60 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private User getUser(String paramString) {
/*  71 */     RecordSet recordSet = new RecordSet();
/*  72 */     recordSet.executeQuery("select * from hrmresource where id=?", new Object[] { paramString });
/*  73 */     User user = new User();
/*  74 */     if (recordSet.next()) {
/*  75 */       user = new User();
/*  76 */       user.setUid(recordSet.getInt("id"));
/*  77 */       user.setLoginid(recordSet.getString("loginid"));
/*  78 */       user.setFirstname(recordSet.getString("firstname"));
/*  79 */       user.setLastname(recordSet.getString("lastname"));
/*  80 */       user.setAliasname(recordSet.getString("aliasname"));
/*  81 */       user.setTitle(recordSet.getString("title"));
/*  82 */       user.setTitlelocation(recordSet.getString("titlelocation"));
/*  83 */       user.setSex(recordSet.getString("sex"));
/*  84 */       user.setPwd(recordSet.getString("password"));
/*  85 */       String str = recordSet.getString("systemlanguage");
/*  86 */       user.setLanguage(Util.getIntValue(str, 7));
/*     */       
/*  88 */       user.setTelephone(recordSet.getString("telephone"));
/*  89 */       user.setMobile(recordSet.getString("mobile"));
/*  90 */       user.setMobilecall(recordSet.getString("mobilecall"));
/*  91 */       user.setEmail(recordSet.getString("email"));
/*  92 */       user.setCountryid(recordSet.getString("countryid"));
/*  93 */       user.setLocationid(recordSet.getString("locationid"));
/*  94 */       user.setResourcetype(recordSet.getString("resourcetype"));
/*  95 */       user.setStartdate(recordSet.getString("startdate"));
/*  96 */       user.setEnddate(recordSet.getString("enddate"));
/*  97 */       user.setContractdate(recordSet.getString("contractdate"));
/*  98 */       user.setJobtitle(recordSet.getString("jobtitle"));
/*  99 */       user.setJobgroup(recordSet.getString("jobgroup"));
/* 100 */       user.setJobactivity(recordSet.getString("jobactivity"));
/* 101 */       user.setJoblevel(recordSet.getString("joblevel"));
/* 102 */       user.setSeclevel(recordSet.getString("seclevel"));
/* 103 */       user.setUserDepartment(Util.getIntValue(recordSet.getString("departmentid"), 0));
/* 104 */       user.setUserSubCompany1(Util.getIntValue(recordSet.getString("subcompanyid1"), 0));
/* 105 */       user.setUserSubCompany2(Util.getIntValue(recordSet.getString("subcompanyid2"), 0));
/* 106 */       user.setUserSubCompany3(Util.getIntValue(recordSet.getString("subcompanyid3"), 0));
/* 107 */       user.setUserSubCompany4(Util.getIntValue(recordSet.getString("subcompanyid4"), 0));
/* 108 */       user.setManagerid(recordSet.getString("managerid"));
/* 109 */       user.setAssistantid(recordSet.getString("assistantid"));
/* 110 */       user.setPurchaselimit(recordSet.getString("purchaselimit"));
/* 111 */       user.setCurrencyid(recordSet.getString("currencyid"));
/* 112 */       user.setLastlogindate(recordSet.getString("currentdate"));
/* 113 */       user.setLogintype("1");
/* 114 */       user.setAccount(recordSet.getString("account"));
/*     */     } else {
/*     */       
/* 117 */       user.setLogintype("1");
/*     */     } 
/* 119 */     return user;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/doc/file/util/DocUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */