/*     */ package weaver.integration.framework.converter.doc.file.input;
/*     */ 
/*     */ import com.highgo.jdbc.jdbc.HGBlob;
/*     */ import com.huawei.gauss.jdbc.inner.GaussBlobImpl;
/*     */ import com.kingbase8.jdbc.KbBlob;
/*     */ import com.mysql.cj.jdbc.Blob;
/*     */ import com.mysql.jdbc.Blob;
/*     */ import com.oscar.jdbc.OscarBlob;
/*     */ import dm.jdbc.driver.DmdbBlob;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.InputStream;
/*     */ import java.util.Map;
/*     */ import oracle.sql.BLOB;
/*     */ import org.postgresql.jdbc.PgBlob;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.framework.converter.IConvert;
/*     */ import weaver.integration.framework.converter.doc.file.util.DocUtil;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DbFile2IdConvert
/*     */   implements IConvert
/*     */ {
/*  30 */   private static Logger newlog = LoggerFactory.getLogger(DbFile2IdConvert.class);
/*     */   public Object convert(Object paramObject) {
/*  32 */     Object object = ((Map)paramObject).get("sourcevalue");
/*  33 */     String str1 = Util.null2String(((Map)paramObject).get("imgFileName"));
/*  34 */     String str2 = Util.null2String(((Map)paramObject).get("createrid"));
/*  35 */     String str3 = Util.null2String(((Map)paramObject).get("workflowid"));
/*  36 */     String str4 = Util.null2String(((Map)paramObject).get("fieldid"));
/*  37 */     String str5 = Util.null2String(((Map)paramObject).get("dbtype"));
/*  38 */     byte[] arrayOfByte = null;
/*     */     
/*  40 */     newlog.info("获取的附件字节组类型 ：" + object.getClass().getName());
/*     */     try {
/*  42 */       if ("oracle".equalsIgnoreCase(str5)) {
/*  43 */         BLOB bLOB = (BLOB)object;
/*  44 */         InputStream inputStream = bLOB.getBinaryStream();
/*  45 */         ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/*  46 */         byte[] arrayOfByte1 = new byte[100];
/*  47 */         int i = 0;
/*  48 */         while ((i = inputStream.read(arrayOfByte1, 0, 100)) > 0) {
/*  49 */           byteArrayOutputStream.write(arrayOfByte1, 0, i);
/*     */         }
/*  51 */         arrayOfByte = byteArrayOutputStream.toByteArray();
/*     */       }
/*  53 */       else if ("mysql".equalsIgnoreCase(str5)) {
/*     */         try {
/*  55 */           if (object instanceof Blob) {
/*  56 */             Blob blob = (Blob)object;
/*  57 */             InputStream inputStream = blob.getBinaryStream();
/*  58 */             ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/*  59 */             byte[] arrayOfByte1 = new byte[100];
/*  60 */             int i = 0;
/*  61 */             while ((i = inputStream.read(arrayOfByte1, 0, 100)) > 0) {
/*  62 */               byteArrayOutputStream.write(arrayOfByte1, 0, i);
/*     */             }
/*  64 */             arrayOfByte = byteArrayOutputStream.toByteArray();
/*  65 */           } else if (object instanceof Blob) {
/*  66 */             Blob blob = (Blob)object;
/*  67 */             InputStream inputStream = blob.getBinaryStream();
/*  68 */             ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/*  69 */             byte[] arrayOfByte1 = new byte[100];
/*  70 */             int i = 0;
/*  71 */             while ((i = inputStream.read(arrayOfByte1, 0, 100)) > 0) {
/*  72 */               byteArrayOutputStream.write(arrayOfByte1, 0, i);
/*     */             }
/*  74 */             arrayOfByte = byteArrayOutputStream.toByteArray();
/*     */           }
/*     */         
/*  77 */         } catch (Exception exception) {}
/*     */ 
/*     */ 
/*     */       
/*     */       }
/*  82 */       else if ("gs".equalsIgnoreCase(str5)) {
/*  83 */         GaussBlobImpl gaussBlobImpl = (GaussBlobImpl)object;
/*  84 */         arrayOfByte = gaussBlobImpl.getBinaryData();
/*     */       }
/*  86 */       else if ("pg".equalsIgnoreCase(str5)) {
/*  87 */         PgBlob pgBlob = (PgBlob)object;
/*  88 */         InputStream inputStream = pgBlob.getBinaryStream();
/*  89 */         ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/*  90 */         byte[] arrayOfByte1 = new byte[100];
/*  91 */         int i = 0;
/*  92 */         while ((i = inputStream.read(arrayOfByte1, 0, 100)) > 0) {
/*  93 */           byteArrayOutputStream.write(arrayOfByte1, 0, i);
/*     */         }
/*  95 */         arrayOfByte = byteArrayOutputStream.toByteArray();
/*     */       }
/*  97 */       else if ("hg".equalsIgnoreCase(str5)) {
/*  98 */         HGBlob hGBlob = (HGBlob)object;
/*  99 */         InputStream inputStream = hGBlob.getBinaryStream();
/* 100 */         ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/* 101 */         byte[] arrayOfByte1 = new byte[100];
/* 102 */         int i = 0;
/* 103 */         while ((i = inputStream.read(arrayOfByte1, 0, 100)) > 0) {
/* 104 */           byteArrayOutputStream.write(arrayOfByte1, 0, i);
/*     */         }
/* 106 */         arrayOfByte = byteArrayOutputStream.toByteArray();
/*     */       }
/* 108 */       else if ("og".equalsIgnoreCase(str5)) {
/* 109 */         PgBlob pgBlob = (PgBlob)object;
/* 110 */         InputStream inputStream = pgBlob.getBinaryStream();
/* 111 */         ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/* 112 */         byte[] arrayOfByte1 = new byte[100];
/* 113 */         int i = 0;
/* 114 */         while ((i = inputStream.read(arrayOfByte1, 0, 100)) > 0) {
/* 115 */           byteArrayOutputStream.write(arrayOfByte1, 0, i);
/*     */         }
/* 117 */         arrayOfByte = byteArrayOutputStream.toByteArray();
/*     */       }
/* 119 */       else if ("st".equalsIgnoreCase(str5)) {
/* 120 */         OscarBlob oscarBlob = (OscarBlob)object;
/* 121 */         InputStream inputStream = oscarBlob.getBinaryStream();
/* 122 */         ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/* 123 */         byte[] arrayOfByte1 = new byte[100];
/* 124 */         int i = 0;
/* 125 */         while ((i = inputStream.read(arrayOfByte1, 0, 100)) > 0) {
/* 126 */           byteArrayOutputStream.write(arrayOfByte1, 0, i);
/*     */         }
/* 128 */         arrayOfByte = byteArrayOutputStream.toByteArray();
/* 129 */       } else if ("dm".equalsIgnoreCase(str5)) {
/* 130 */         DmdbBlob dmdbBlob = (DmdbBlob)object;
/* 131 */         InputStream inputStream = dmdbBlob.getBinaryStream();
/* 132 */         ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/* 133 */         byte[] arrayOfByte1 = new byte[100];
/* 134 */         int i = 0;
/* 135 */         while ((i = inputStream.read(arrayOfByte1, 0, 100)) > 0) {
/* 136 */           byteArrayOutputStream.write(arrayOfByte1, 0, i);
/*     */         }
/* 138 */         arrayOfByte = byteArrayOutputStream.toByteArray();
/* 139 */       } else if ("jc".equalsIgnoreCase(str5)) {
/* 140 */         KbBlob kbBlob = (KbBlob)object;
/* 141 */         InputStream inputStream = kbBlob.getBinaryStream();
/* 142 */         ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/* 143 */         byte[] arrayOfByte1 = new byte[100];
/* 144 */         int i = 0;
/* 145 */         while ((i = inputStream.read(arrayOfByte1, 0, 100)) > 0) {
/* 146 */           byteArrayOutputStream.write(arrayOfByte1, 0, i);
/*     */         }
/* 148 */         arrayOfByte = byteArrayOutputStream.toByteArray();
/*     */ 
/*     */       
/*     */       }
/*     */       else {
/*     */ 
/*     */ 
/*     */         
/* 156 */         arrayOfByte = (byte[])object;
/*     */       } 
/* 158 */     } catch (Exception exception) {
/* 159 */       exception.printStackTrace();
/* 160 */       newlog.error(exception);
/*     */     } 
/*     */     
/* 163 */     String str6 = "";
/* 164 */     DocUtil docUtil = new DocUtil();
/* 165 */     str6 = docUtil.getDoc(arrayOfByte, str1, str2, str3, str4);
/* 166 */     return str6;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/doc/file/input/DbFile2IdConvert.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */