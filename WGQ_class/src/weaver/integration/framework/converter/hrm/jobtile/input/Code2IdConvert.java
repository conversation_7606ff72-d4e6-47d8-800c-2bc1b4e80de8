/*    */ package weaver.integration.framework.converter.hrm.jobtile.input;
/*    */ 
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.integration.framework.converter.IConvert;
/*    */ import weaver.integration.framework.converter.rule.RuleBase;
/*    */ 
/*    */ public class Code2IdConvert
/*    */   implements IConvert {
/*    */   public Object convert(Object paramObject) {
/* 12 */     if (paramObject instanceof Map) {
/* 13 */       String str = Util.null2String(((Map)paramObject).get("sourcevalue"));
/* 14 */       RecordSet recordSet = new RecordSet();
/* 15 */       recordSet.executeQuery(RuleBase.HRM_JOBTITLE, new Object[] { str });
/* 16 */       if (recordSet.next()) {
/* 17 */         str = recordSet.getString(1);
/*    */       }
/* 19 */       return str;
/*    */     } 
/* 21 */     return paramObject;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/hrm/jobtile/input/Code2IdConvert.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */