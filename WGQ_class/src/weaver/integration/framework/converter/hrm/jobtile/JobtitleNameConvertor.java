/*    */ package weaver.integration.framework.converter.hrm.jobtile;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.integration.framework.converter.IConvert;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class JobtitleNameConvertor
/*    */   implements IConvert
/*    */ {
/*    */   public Object convert(Object paramObject) {
/* 12 */     RecordSet recordSet = new RecordSet();
/* 13 */     recordSet.executeQuery("SELECT jobtitlename FROM HrmJobTitles WHERE id=?", new Object[] { paramObject });
/* 14 */     if (recordSet.next()) {
/* 15 */       return recordSet.getString("jobtitlename");
/*    */     }
/* 17 */     return "";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/hrm/jobtile/JobtitleNameConvertor.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */