/*    */ package weaver.integration.framework.converter.hrm.resource.input;
/*    */ 
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.integration.framework.converter.IConvert;
/*    */ import weaver.integration.framework.converter.rule.RuleBase;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Email2IdConvert
/*    */   implements IConvert
/*    */ {
/* 16 */   private Logger newlog = LoggerFactory.getLogger(getClass());
/*    */   public Object convert(Object paramObject) {
/* 18 */     String str = Util.null2String(((Map)paramObject).get("sourcevalue"));
/* 19 */     RecordSet recordSet = new RecordSet();
/* 20 */     recordSet.executeQuery(RuleBase.HRM_HRMRESOUCE_EMAIL, new Object[] { str });
/* 21 */     if (recordSet.next()) {
/* 22 */       str = recordSet.getString(1);
/*    */     } else {
/* 24 */       str = "";
/*    */     } 
/* 26 */     return str;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/hrm/resource/input/Email2IdConvert.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */