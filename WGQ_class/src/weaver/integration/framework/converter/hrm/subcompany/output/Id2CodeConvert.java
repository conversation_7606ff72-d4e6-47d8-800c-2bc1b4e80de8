/*   */ package weaver.integration.framework.converter.hrm.subcompany.output;
/*   */ 
/*   */ import weaver.integration.framework.converter.IConvert;
/*   */ 
/*   */ public class Id2CodeConvert
/*   */   implements IConvert {
/*   */   public Object convert(Object paramObject) {
/* 8 */     return new String("12345");
/*   */   }
/*   */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/hrm/subcompany/output/Id2CodeConvert.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */