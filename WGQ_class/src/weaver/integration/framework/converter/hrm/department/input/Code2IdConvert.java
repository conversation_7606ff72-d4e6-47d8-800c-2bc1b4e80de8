/*    */ package weaver.integration.framework.converter.hrm.department.input;
/*    */ 
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.integration.framework.converter.IConvert;
/*    */ import weaver.integration.framework.converter.rule.RuleBase;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Code2IdConvert
/*    */   implements IConvert
/*    */ {
/* 17 */   private static Logger newlog = LoggerFactory.getLogger(Code2IdConvert.class);
/*    */   
/*    */   public Object convert(Object paramObject) {
/* 20 */     String str = Util.null2String(((Map)paramObject).get("sourcevalue"));
/* 21 */     RecordSet recordSet = new RecordSet();
/* 22 */     recordSet.executeQuery(RuleBase.HRM_DEP, new Object[] { str });
/* 23 */     if (recordSet.next()) {
/* 24 */       str = recordSet.getString(1);
/*    */     } else {
/* 26 */       str = "";
/*    */     } 
/*    */     
/* 29 */     return str;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/hrm/department/input/Code2IdConvert.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */