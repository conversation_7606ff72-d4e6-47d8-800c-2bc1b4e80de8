/*    */ package weaver.integration.framework.converter.hrm.department.input;
/*    */ 
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.integration.framework.converter.IConvert;
/*    */ import weaver.integration.framework.converter.rule.RuleBase;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Id2IdConvert
/*    */   implements IConvert
/*    */ {
/* 17 */   private static Logger newlog = LoggerFactory.getLogger(Id2IdConvert.class);
/*    */   public Object convert(Object paramObject) {
/* 19 */     String str = Util.null2String(((Map)paramObject).get("sourcevalue"));
/* 20 */     RecordSet recordSet = new RecordSet();
/* 21 */     recordSet.executeQuery(RuleBase.HRM_DEP_ID, new Object[] { str });
/* 22 */     if (recordSet.next()) {
/* 23 */       str = recordSet.getString(1);
/*    */     } else {
/* 25 */       str = "";
/*    */     } 
/* 27 */     return str;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/hrm/department/input/Id2IdConvert.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */