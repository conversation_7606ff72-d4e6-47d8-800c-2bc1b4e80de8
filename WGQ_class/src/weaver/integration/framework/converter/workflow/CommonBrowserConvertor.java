/*     */ package weaver.integration.framework.converter.workflow;
/*     */ 
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.framework.Context;
/*     */ import weaver.integration.framework.converter.IConvert;
/*     */ import weaver.integration.framework.data.RecordData;
/*     */ import weaver.integration.framework.mapping.impl.FieldDataMapping;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.workflow.dmlaction.DBTypeUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CommonBrowserConvertor
/*     */   implements IConvert
/*     */ {
/*     */   public Object convert(Object paramObject) {
/*  23 */     FieldDataMapping fieldDataMapping = (FieldDataMapping)paramObject;
/*  24 */     Object object = fieldDataMapping.getSourceField().getFieldValue();
/*  25 */     String str1 = fieldDataMapping.getSourceField().getFieldName();
/*     */     
/*  27 */     Context context = fieldDataMapping.getContext();
/*  28 */     String str2 = context.getFieldHtmltype();
/*  29 */     String str3 = context.getFieldType();
/*  30 */     String str4 = context.getChangeType();
/*  31 */     String str5 = context.getCustomsql();
/*  32 */     Map map1 = context.getSystemVariables();
/*  33 */     Map map2 = context.getWorkflowVariables();
/*  34 */     Map map3 = context.getAttributes();
/*  35 */     String str6 = Util.null2String(map3.get("datasourceid"));
/*  36 */     RecordData recordData = context.getRecordData();
/*  37 */     Map map4 = recordData.getFieldData();
/*     */     
/*  39 */     String str7 = Util.null2String(map4.get(str1.toLowerCase()));
/*     */     
/*  41 */     if (str4.equals("6")) {
/*     */       
/*  43 */       String str = Util.null2String(map4.get(str1.toLowerCase()));
/*  44 */       str7 = getTranSqlValue(str5, str, str6);
/*     */     }
/*  46 */     else if (str2.equals("3")) {
/*     */       
/*  48 */       if (!"".equals(str7)) {
/*  49 */         if (str3.equals("1")) {
/*     */           
/*  51 */           str7 = getCreater(str7, str4);
/*  52 */         } else if (str3.equals("4")) {
/*     */           
/*  54 */           str7 = getDept(str7, str4);
/*  55 */         } else if (str3.equals("164")) {
/*     */           
/*  57 */           str7 = getSubCom(str7, str4);
/*     */         } 
/*     */       }
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/*  64 */     return str7;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCreater(String paramString1, String paramString2) {
/*  75 */     paramString1 = paramString1.replace("'", "''");
/*  76 */     RecordSet recordSet = new RecordSet();
/*  77 */     String str1 = "";
/*  78 */     String str2 = "";
/*  79 */     if (paramString2.equals("0")) {
/*  80 */       str2 = "select id from HrmResource where id='" + paramString1 + "' and status<4  and (loginid !='' Or  loginid is not  null) ";
/*  81 */     } else if (paramString2.equals("1")) {
/*  82 */       str2 = "select id from HrmResource where workcode='" + paramString1 + "' and  status<4 and (loginid !='' Or  loginid is not  null) ";
/*  83 */     } else if (paramString2.equals("2")) {
/*  84 */       str2 = "select id from HrmResource where loginid='" + paramString1 + "' and  status<4 and (loginid !='' Or  loginid is not  null) ";
/*  85 */     } else if (paramString2.equals("3")) {
/*  86 */       str2 = "select id from HrmResource where mobile='" + paramString1 + "' and  status<4  and (loginid !='' Or  loginid is not  null) ";
/*  87 */     } else if (paramString2.equals("4")) {
/*  88 */       str2 = "select id from HrmResource where email='" + paramString1 + "' and  status<4  and (loginid !='' Or  loginid is not  null) ";
/*     */     }
/*  90 */     else if (paramString2.equals("5")) {
/*  91 */       str2 = "select id from HrmResource where id='" + paramString1 + "' and  status<4  and (loginid !='' Or  loginid is not  null) ";
/*     */     } 
/*     */     
/*  94 */     recordSet.executeQuery(str2, new Object[0]);
/*  95 */     if (recordSet.next()) str1 = recordSet.getString("id");
/*     */ 
/*     */     
/*  98 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDept(String paramString1, String paramString2) {
/* 109 */     paramString1 = paramString1.replace("'", "''");
/* 110 */     RecordSet recordSet = new RecordSet();
/* 111 */     String str1 = "";
/* 112 */     String str2 = "";
/* 113 */     if (paramString2.equals("0")) {
/* 114 */       str2 = "select id from HrmDepartment where id='" + paramString1 + "'";
/* 115 */     } else if (paramString2.equals("1")) {
/* 116 */       str2 = "select id from HrmDepartment where departmentcode='" + paramString1 + "'";
/*     */     } 
/*     */     
/* 119 */     recordSet.executeQuery(str2, new Object[0]);
/* 120 */     if (recordSet.next()) str1 = recordSet.getString("id");
/*     */     
/* 122 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubCom(String paramString1, String paramString2) {
/* 133 */     paramString1 = paramString1.replace("'", "''");
/* 134 */     RecordSet recordSet = new RecordSet();
/* 135 */     String str1 = "";
/* 136 */     String str2 = "";
/* 137 */     if (paramString2.equals("0")) {
/* 138 */       str2 = "select id from HrmSubCompany where id='" + paramString1 + "'";
/* 139 */     } else if (paramString2.equals("1")) {
/* 140 */       str2 = "select id from HrmSubCompany where subcompanycode='" + paramString1 + "'";
/*     */     } 
/*     */     
/* 143 */     recordSet.executeQuery(str2, new Object[0]);
/* 144 */     if (recordSet.next()) str1 = recordSet.getString("id");
/*     */     
/* 146 */     return str1;
/*     */   }
/*     */   
/*     */   public String getTranSqlValue(String paramString1, String paramString2, String paramString3) {
/* 150 */     paramString2 = paramString2.replace("'", "''");
/* 151 */     paramString1 = DBTypeUtil.replaceString(paramString1, "{?currentvalue}", paramString2);
/* 152 */     RecordSet recordSet = new RecordSet();
/* 153 */     if (!"".equals(paramString3)) {
/* 154 */       recordSet.executeSql(paramString1, paramString3);
/*     */     } else {
/* 156 */       recordSet.executeSql(paramString1);
/*     */     } 
/* 158 */     if (recordSet.next()) {
/* 159 */       return recordSet.getString(1);
/*     */     }
/* 161 */     return "";
/*     */   }
/*     */   
/* 164 */   private Logger logger = LoggerFactory.getLogger(getClass());
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/workflow/CommonBrowserConvertor.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */