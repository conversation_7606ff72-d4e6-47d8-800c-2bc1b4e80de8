/*    */ package weaver.integration.framework.converter.workflow;
/*    */ 
/*    */ import java.util.Calendar;
/*    */ import java.util.Map;
/*    */ import weaver.general.DateUtil;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.resource.ResourceComInfo;
/*    */ import weaver.integration.framework.Context;
/*    */ import weaver.integration.framework.converter.IConvert;
/*    */ import weaver.integration.framework.data.RecordData;
/*    */ import weaver.integration.framework.mapping.impl.FieldDataMapping;
/*    */ import weaver.workflow.workflow.WorkflowComInfo;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RequestNameConvertor
/*    */   implements IConvert
/*    */ {
/*    */   public Object convert(Object paramObject) {
/* 22 */     FieldDataMapping fieldDataMapping = (FieldDataMapping)paramObject;
/* 23 */     Object object = fieldDataMapping.getSourceField().getFieldValue();
/* 24 */     String str1 = fieldDataMapping.getSourceField().getFieldName();
/*    */     
/* 26 */     Context context = fieldDataMapping.getContext();
/* 27 */     Map map1 = context.getSystemVariables();
/* 28 */     Map map2 = context.getWorkflowVariables();
/* 29 */     RecordData recordData = context.getRecordData();
/* 30 */     Map map3 = recordData.getFieldData();
/*    */     
/* 32 */     String str2 = (String)map2.get("workflowid");
/* 33 */     String str3 = (String)map1.get("userid");
/*    */ 
/*    */ 
/*    */     
/* 37 */     WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/* 38 */     ResourceComInfo resourceComInfo = null;
/*    */     try {
/* 40 */       resourceComInfo = new ResourceComInfo();
/* 41 */     } catch (Exception exception) {
/* 42 */       exception.printStackTrace();
/*    */     } 
/*    */     
/* 45 */     String str4 = "";
/* 46 */     String str5 = Util.null2String(workflowComInfo.getWorkflowname(str2));
/* 47 */     String str6 = Util.toScreen(resourceComInfo.getResourcename(str3), 7);
/* 48 */     String str7 = "";
/* 49 */     Calendar calendar = Calendar.getInstance();
/*    */ 
/*    */     
/* 52 */     str7 = Util.add0(calendar.get(1), 4) + "-" + Util.add0(calendar.get(2) + 1, 2) + "-" + Util.add0(calendar.get(5), 2);
/* 53 */     DateUtil dateUtil = new DateUtil();
/*    */     
/* 55 */     if (str1.equals("")) {
/*    */       
/* 57 */       str4 = Util.null2String(dateUtil.getWFTitleNew("" + str2, "" + str3, "" + str6, "1"));
/* 58 */       if (str4.equals("")) {
/* 59 */         str4 = str5 + "-" + str6 + "-" + str7;
/*    */       }
/*    */     } else {
/* 62 */       String str = Util.null2String(map3.get(str1.toLowerCase()));
/* 63 */       if (!str.equals("")) {
/* 64 */         str4 = str;
/*    */       } else {
/* 66 */         str4 = Util.null2String(dateUtil.getWFTitleNew("" + str2, "" + str3, "" + str6, "1"));
/* 67 */         if (str4.equals("")) {
/* 68 */           str4 = str5 + "-" + str6 + "-" + str7;
/*    */         }
/*    */       } 
/*    */     } 
/*    */     
/* 73 */     return str4;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/workflow/RequestNameConvertor.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */