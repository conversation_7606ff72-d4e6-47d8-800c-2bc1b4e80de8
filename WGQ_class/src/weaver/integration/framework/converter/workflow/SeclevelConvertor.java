/*    */ package weaver.integration.framework.converter.workflow;
/*    */ 
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.integration.framework.Context;
/*    */ import weaver.integration.framework.converter.IConvert;
/*    */ import weaver.integration.framework.data.RecordData;
/*    */ import weaver.integration.framework.mapping.impl.FieldDataMapping;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ import weaver.workflow.dmlaction.DBTypeUtil;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SeclevelConvertor
/*    */   implements IConvert
/*    */ {
/*    */   public Object convert(Object paramObject) {
/* 22 */     FieldDataMapping fieldDataMapping = (FieldDataMapping)paramObject;
/* 23 */     Object object = fieldDataMapping.getSourceField().getFieldValue();
/* 24 */     String str1 = fieldDataMapping.getSourceField().getFieldName();
/*    */     
/* 26 */     Context context = fieldDataMapping.getContext();
/* 27 */     String str2 = context.getChangeType();
/* 28 */     String str3 = context.getCustomsql();
/* 29 */     Map map1 = context.getSystemVariables();
/* 30 */     Map map2 = context.getWorkflowVariables();
/* 31 */     Map map3 = context.getAttributes();
/* 32 */     String str4 = Util.null2String(map3.get("datasourceid"));
/* 33 */     RecordData recordData = context.getRecordData();
/* 34 */     Map map4 = recordData.getFieldData();
/*    */     
/* 36 */     String str5 = "";
/* 37 */     if ("6".equals(str2)) {
/* 38 */       String str = Util.null2String(map4.get(str1.toLowerCase()));
/* 39 */       str5 = getTranSqlValue(str3, str, str4);
/*    */     } else {
/* 41 */       str5 = str3;
/*    */     } 
/*    */     
/* 44 */     if (str5 == null || "".equals(str5)) {
/* 45 */       str5 = "4";
/*    */     }
/* 47 */     if (!"1".equals(str5) && !"2".equals(str5) && !"3".equals(str5) && !"4".equals(str5)) {
/* 48 */       str5 = "4";
/*    */     }
/* 50 */     return str5;
/*    */   }
/*    */   
/*    */   public String getTranSqlValue(String paramString1, String paramString2, String paramString3) {
/* 54 */     paramString2 = paramString2.replace("'", "''");
/* 55 */     paramString1 = DBTypeUtil.replaceString(paramString1, "{?currentvalue}", paramString2);
/* 56 */     RecordSet recordSet = new RecordSet();
/* 57 */     if (!"".equals(paramString3)) {
/* 58 */       recordSet.executeSql(paramString1, paramString3);
/*    */     } else {
/* 60 */       recordSet.executeSql(paramString1);
/*    */     } 
/* 62 */     if (recordSet.next()) {
/* 63 */       return recordSet.getString(1);
/*    */     }
/* 65 */     return "";
/*    */   }
/*    */   
/* 68 */   private Logger logger = LoggerFactory.getLogger(getClass());
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/workflow/SeclevelConvertor.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */