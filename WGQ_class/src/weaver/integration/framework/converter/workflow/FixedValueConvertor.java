/*    */ package weaver.integration.framework.converter.workflow;
/*    */ 
/*    */ import com.engine.integration.biz.SystemVariableResolver;
/*    */ import java.util.Map;
/*    */ import java.util.regex.Matcher;
/*    */ import java.util.regex.Pattern;
/*    */ import weaver.general.Util;
/*    */ import weaver.integration.framework.Context;
/*    */ import weaver.integration.framework.converter.IConvert;
/*    */ import weaver.integration.framework.data.RecordData;
/*    */ import weaver.integration.framework.mapping.impl.FieldDataMapping;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FixedValueConvertor
/*    */   implements IConvert
/*    */ {
/*    */   public Object convert(Object paramObject) {
/* 21 */     FieldDataMapping fieldDataMapping = (FieldDataMapping)paramObject;
/* 22 */     Object object = fieldDataMapping.getSourceField().getFieldValue();
/* 23 */     String str1 = fieldDataMapping.getSourceField().getFieldName();
/*    */     
/* 25 */     Context context = fieldDataMapping.getContext();
/* 26 */     String str2 = context.getChangeType();
/* 27 */     String str3 = context.getCustomsql();
/* 28 */     String str4 = context.getFieldHtmltype();
/* 29 */     String str5 = context.getFieldType();
/* 30 */     Map map1 = context.getSystemVariables();
/* 31 */     Map map2 = context.getWorkflowVariables();
/* 32 */     RecordData recordData = context.getRecordData();
/* 33 */     Map map3 = recordData.getFieldData();
/*    */ 
/*    */     
/* 36 */     String str6 = str3;
/* 37 */     if (str6 != null && !"".equals(str6) && hasVariables(str6)) {
/* 38 */       SystemVariableResolver systemVariableResolver = new SystemVariableResolver();
/* 39 */       str6 = systemVariableResolver.getFixedValueByUserVariable((String)map1.get("userid"), str6);
/*    */     } 
/* 41 */     if ("1".equals(str4) && "2".equals(context.getFieldType())) {
/*    */       
/* 43 */       str6 = Util.getIntValue(str6, 0) + "";
/* 44 */     } else if ("1".equals(str4) && ("3".equals(str5) || "4".equals(str5))) {
/*    */       
/* 46 */       str6 = Util.getDoubleValue(str6, 0.0D) + "";
/*    */     }
/* 48 */     else if ("3".equals(str4)) {
/*    */       
/* 50 */       str6 = Util.getIntValue(str6, 0) + "";
/*    */     } 
/*    */ 
/*    */     
/* 54 */     return str6;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private boolean hasVariables(String paramString) {
/* 63 */     Pattern pattern = Pattern.compile("(\\{\\?.{1,}\\})");
/* 64 */     Matcher matcher = pattern.matcher(paramString);
/* 65 */     return matcher.find();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/workflow/FixedValueConvertor.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */