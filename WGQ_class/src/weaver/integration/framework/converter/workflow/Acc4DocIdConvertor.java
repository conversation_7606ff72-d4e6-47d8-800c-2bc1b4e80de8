/*     */ package weaver.integration.framework.converter.workflow;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.api.doc.detail.service.DocSaveService;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.InputStream;
/*     */ import java.io.OutputStream;
/*     */ import java.net.HttpURLConnection;
/*     */ import java.net.URL;
/*     */ import java.net.URLConnection;
/*     */ import java.nio.file.Files;
/*     */ import java.nio.file.Paths;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.ImageFileManager;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.integration.framework.Context;
/*     */ import weaver.integration.framework.converter.IConvert;
/*     */ import weaver.integration.framework.data.RecordData;
/*     */ import weaver.integration.framework.mapping.impl.FieldDataMapping;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.util.FTPUtil;
/*     */ 
/*     */ 
/*     */ public class Acc4DocIdConvertor
/*     */   implements IConvert
/*     */ {
/*     */   public Object convert(Object paramObject) {
/*  34 */     FieldDataMapping fieldDataMapping = (FieldDataMapping)paramObject;
/*  35 */     Object object1 = fieldDataMapping.getSourceField().getFieldValue();
/*  36 */     String str1 = fieldDataMapping.getSourceField().getFieldName();
/*     */     
/*  38 */     Context context = fieldDataMapping.getContext();
/*  39 */     String str2 = context.getFieldId();
/*  40 */     String str3 = context.getChangeType();
/*  41 */     String str4 = context.getCustomsql();
/*  42 */     Map map1 = context.getSystemVariables();
/*  43 */     Map map2 = context.getWorkflowVariables();
/*  44 */     Map map3 = context.getAttributes();
/*  45 */     RecordData recordData = context.getRecordData();
/*  46 */     Map<String, Object> map = recordData.getFieldData();
/*  47 */     Object object2 = map.get(str1.toLowerCase());
/*     */     
/*  49 */     String str5 = (String)map2.get("workflowid");
/*  50 */     String str6 = (String)map1.get("userid");
/*     */     
/*  52 */     String str7 = (String)map3.get("attachment_settings");
/*  53 */     String str8 = (String)map3.get("dbtype");
/*     */     
/*  55 */     return getAttachmentId(str5, str6, str2, object2, str7, map, str1, str8);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getAttachmentId(String paramString1, String paramString2, String paramString3, Object paramObject, String paramString4, Map<String, Object> paramMap, String paramString5, String paramString6) {
/*  75 */     int i = -1;
/*  76 */     if (paramObject != null && !"".equals(Util.null2String(paramString4))) {
/*  77 */       String str = getWorkflowUploadDocCategory(paramString1, paramString3);
/*  78 */       if ("".equals(str)) {
/*  79 */         return "-1";
/*     */       }
/*  81 */       JSONObject jSONObject = JSON.parseObject(paramString4);
/*  82 */       int j = Util.getIntValue(jSONObject.getString("attachment_type"), -1);
/*  83 */       byte[] arrayOfByte = null;
/*  84 */       FileInputStream fileInputStream = null;
/*  85 */       InputStream inputStream = null;
/*  86 */       OutputStream outputStream = null;
/*  87 */       ByteArrayOutputStream byteArrayOutputStream = null; try {
/*     */         String str4; URL uRL; URLConnection uRLConnection; HttpURLConnection httpURLConnection; int k, m; String str5; byte[] arrayOfByte1; String str6; int n; String str7, str8, str9, str10, str11; FTPUtil fTPUtil; boolean bool; String str12;
/*     */         File file;
/*  90 */         String str1 = "";
/*  91 */         String str2 = Util.null2String(jSONObject.getString("db_field_title"));
/*  92 */         if ("null".equalsIgnoreCase(str2)) {
/*  93 */           str2 = "";
/*     */         }
/*  95 */         if (!"".equals(str2)) {
/*  96 */           str1 = (String)paramMap.get(str2.toLowerCase());
/*     */         }
/*     */         
/*  99 */         switch (j) {
/*     */           case 3:
/* 101 */             if ("oracle".equalsIgnoreCase(paramString6)) {
/* 102 */               arrayOfByte = (byte[])paramObject; break;
/* 103 */             }  if ("mysql".equalsIgnoreCase(paramString6)) {
/* 104 */               arrayOfByte = (byte[])paramObject; break;
/*     */             } 
/* 106 */             arrayOfByte = (byte[])paramObject;
/*     */             break;
/*     */ 
/*     */ 
/*     */ 
/*     */           
/*     */           case 4:
/* 113 */             paramObject = paramMap.get(paramString5.toLowerCase());
/* 114 */             str3 = "";
/* 115 */             str4 = Util.null2String(jSONObject.getString("url_prefix"));
/* 116 */             if (!"".equals(str4)) {
/* 117 */               if (!str4.endsWith("/") && !((String)paramObject).startsWith("/")) {
/* 118 */                 paramObject = str4 + "/" + paramObject;
/*     */               } else {
/* 120 */                 paramObject = str4 + paramObject;
/*     */               } 
/*     */             }
/*     */             
/* 124 */             str3 = (String)paramObject;
/*     */             
/* 126 */             uRL = new URL(str3);
/* 127 */             uRLConnection = uRL.openConnection();
/* 128 */             uRLConnection.connect();
/* 129 */             httpURLConnection = (HttpURLConnection)uRLConnection;
/* 130 */             k = httpURLConnection.getResponseCode();
/* 131 */             if (k != 200) {
/* 132 */               this.logger.error("===========getAttachmentId error, the url has no connect!!!!, url is:" + str3); break;
/*     */             } 
/* 134 */             m = uRLConnection.getContentLength();
/* 135 */             inputStream = uRLConnection.getInputStream();
/* 136 */             byteArrayOutputStream = new ByteArrayOutputStream();
/* 137 */             arrayOfByte1 = new byte[2048];
/* 138 */             n = 0;
/* 139 */             while ((n = inputStream.read(arrayOfByte1)) != -1) {
/* 140 */               byteArrayOutputStream.write(arrayOfByte1, 0, n);
/*     */             }
/* 142 */             arrayOfByte = byteArrayOutputStream.toByteArray();
/*     */             
/* 144 */             if ("".equals(str1)) {
/* 145 */               String str13 = uRL.getFile();
/* 146 */               if (str13 != null) {
/* 147 */                 int i1 = str13.lastIndexOf("/");
/* 148 */                 if (i1 >= 0 && str13.length() > i1) {
/* 149 */                   str13 = str13.substring(i1 + 1);
/*     */                 }
/* 151 */                 str1 = str13;
/*     */               } 
/*     */             } 
/*     */             break;
/*     */           
/*     */           case 1:
/* 157 */             paramObject = paramMap.get(paramString5.toLowerCase());
/* 158 */             str5 = Util.null2String(jSONObject.getString("ftp_addr"));
/* 159 */             str6 = Util.null2String(jSONObject.getString("ftp_dir"));
/* 160 */             str7 = Util.null2String(jSONObject.getString("ftp_port"));
/* 161 */             str8 = Util.null2String(jSONObject.getString("ftp_pwd"));
/* 162 */             str9 = Util.null2String(jSONObject.getString("ftp_username"));
/* 163 */             str10 = "/";
/* 164 */             if (!"".equals(str6)) {
/* 165 */               if (!str6.endsWith("/") && !((String)paramObject).startsWith("/")) {
/* 166 */                 paramObject = str6 + "/" + paramObject;
/*     */               } else {
/* 168 */                 paramObject = str6 + paramObject;
/*     */               } 
/*     */             }
/*     */             
/* 172 */             str11 = "";
/* 173 */             if (((String)paramObject).lastIndexOf("/") >= 0 && ((String)paramObject).lastIndexOf("/") < ((String)paramObject).length()) {
/* 174 */               str11 = ((String)paramObject).substring(((String)paramObject).lastIndexOf("/") + 1, ((String)paramObject).length());
/* 175 */               str10 = ((String)paramObject).substring(0, ((String)paramObject).lastIndexOf("/") + 1);
/*     */             } 
/*     */             
/* 178 */             fTPUtil = new FTPUtil(str5, Util.getIntValue(str7, 21), str9, str8);
/* 179 */             bool = fTPUtil.login();
/*     */             
/* 181 */             if (bool) {
/* 182 */               boolean bool1 = fTPUtil.getFtpClient().changeWorkingDirectory(str10);
/*     */               
/* 184 */               if (bool1) {
/* 185 */                 inputStream = fTPUtil.getFtpClient().retrieveFileStream(str11);
/* 186 */                 byteArrayOutputStream = new ByteArrayOutputStream();
/* 187 */                 byte[] arrayOfByte2 = new byte[2048];
/* 188 */                 int i1 = 0;
/* 189 */                 while ((i1 = inputStream.read(arrayOfByte2)) != -1) {
/* 190 */                   byteArrayOutputStream.write(arrayOfByte2, 0, i1);
/*     */                 }
/* 192 */                 arrayOfByte = byteArrayOutputStream.toByteArray();
/*     */                 
/* 194 */                 if ("".equals(str1))
/* 195 */                   str1 = str11; 
/*     */                 break;
/*     */               } 
/* 198 */               this.logger.error("===========getAttachmentId error occured! the path:" + str10 + " is not exists!!!");
/*     */               
/*     */               break;
/*     */             } 
/* 202 */             this.logger.error("===========getAttachmentId error occured! FTP logined failure!!!");
/*     */             break;
/*     */           
/*     */           case 2:
/* 206 */             paramObject = paramMap.get(paramString5.toLowerCase());
/* 207 */             str12 = Util.null2String(jSONObject.getString("local_dir"));
/* 208 */             if (!"".equals(str12)) {
/* 209 */               if (!str12.endsWith("/") && !((String)paramObject).startsWith("/")) {
/* 210 */                 paramObject = str12 + "/" + paramObject;
/*     */               } else {
/* 212 */                 paramObject = str12 + paramObject;
/*     */               } 
/*     */             }
/*     */             
/* 216 */             file = new File((String)paramObject);
/* 217 */             if (file.exists() && file.isFile()) {
/* 218 */               arrayOfByte = Files.readAllBytes(Paths.get(file.getAbsolutePath(), new String[0]));
/* 219 */               if ("".equals(str1)) {
/* 220 */                 String str13 = file.getName();
/* 221 */                 if (str13 != null) {
/* 222 */                   int i1 = str13.lastIndexOf("/");
/* 223 */                   if (i1 >= 0 && str13.length() > i1) {
/* 224 */                     str13 = str13.substring(i1 + 1);
/*     */                   }
/* 226 */                   str1 = str13;
/*     */                 } 
/*     */               } 
/*     */             } 
/*     */             break;
/*     */         } 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 236 */         if (arrayOfByte != null && !"".equals(str1)) {
/*     */           
/* 238 */           DocSaveService docSaveService = new DocSaveService();
/* 239 */           ImageFileManager imageFileManager = new ImageFileManager();
/* 240 */           imageFileManager.resetParameter();
/* 241 */           imageFileManager.setImagFileName(str1);
/* 242 */           imageFileManager.setData(arrayOfByte);
/* 243 */           int i1 = imageFileManager.saveImageFile();
/* 244 */           i = docSaveService.accForDoc(Util.getIntValue(str, -1), i1, getUser(paramString2));
/*     */         } else {
/* 246 */           this.logger.error("===========getAttachmentId error occurred!!! no data!!!");
/*     */         } 
/*     */         
/* 249 */         String str3 = i + ""; return str3;
/* 250 */       } catch (Exception exception) {
/* 251 */         exception.printStackTrace();
/* 252 */         this.logger.error("===========getAttachmentId error occured!" + exception.getMessage());
/* 253 */         return "-1";
/*     */       } finally {
/*     */         try {
/* 256 */           if (fileInputStream != null) {
/* 257 */             fileInputStream.close();
/*     */           }
/* 259 */           if (inputStream != null) {
/* 260 */             inputStream.close();
/*     */           }
/* 262 */           if (byteArrayOutputStream != null) {
/* 263 */             byteArrayOutputStream.close();
/*     */           }
/* 265 */           if (outputStream != null) {
/* 266 */             outputStream.close();
/*     */           }
/* 268 */         } catch (Exception exception) {
/* 269 */           this.logger.error("================close resource error occurred!!" + exception.getMessage());
/* 270 */           exception.printStackTrace();
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 277 */     return "-1";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getWorkflowUploadDocCategory(String paramString1, String paramString2) {
/* 288 */     RecordSet recordSet = new RecordSet();
/* 289 */     String str = "";
/*     */     
/* 291 */     recordSet.executeQuery("select * from workflow_fileupload where workflowid=" + paramString1 + " and fieldid=" + paramString2, new Object[0]);
/* 292 */     if (recordSet.next()) {
/* 293 */       str = recordSet.getString("docCategory");
/*     */     }
/* 295 */     if ("".equals(str)) {
/*     */       
/* 297 */       recordSet.executeQuery("select messageType,chatsType,catelogtype,docCategory,selectedCateLog,limitvalue from workflow_base where id=" + paramString1, new Object[0]);
/* 298 */       if (recordSet.next()) {
/* 299 */         str = recordSet.getString("docCategory");
/*     */       }
/*     */     } 
/* 302 */     if (!"".equals(str) && str.lastIndexOf(",") > -1) {
/* 303 */       str = str.substring(str.lastIndexOf(",") + 1, str.length());
/*     */     }
/*     */     
/* 306 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private User getUser(String paramString) {
/* 315 */     RecordSet recordSet = new RecordSet();
/* 316 */     recordSet.executeQuery("select * from hrmresource where id=?", new Object[] { paramString });
/* 317 */     User user = new User();
/* 318 */     if (recordSet.next()) {
/* 319 */       user = new User();
/* 320 */       user.setUid(recordSet.getInt("id"));
/* 321 */       user.setLoginid(recordSet.getString("loginid"));
/* 322 */       user.setFirstname(recordSet.getString("firstname"));
/* 323 */       user.setLastname(recordSet.getString("lastname"));
/* 324 */       user.setAliasname(recordSet.getString("aliasname"));
/* 325 */       user.setTitle(recordSet.getString("title"));
/* 326 */       user.setTitlelocation(recordSet.getString("titlelocation"));
/* 327 */       user.setSex(recordSet.getString("sex"));
/* 328 */       user.setPwd(recordSet.getString("password"));
/* 329 */       String str = recordSet.getString("systemlanguage");
/* 330 */       user.setLanguage(Util.getIntValue(str, 7));
/*     */       
/* 332 */       user.setTelephone(recordSet.getString("telephone"));
/* 333 */       user.setMobile(recordSet.getString("mobile"));
/* 334 */       user.setMobilecall(recordSet.getString("mobilecall"));
/* 335 */       user.setEmail(recordSet.getString("email"));
/* 336 */       user.setCountryid(recordSet.getString("countryid"));
/* 337 */       user.setLocationid(recordSet.getString("locationid"));
/* 338 */       user.setResourcetype(recordSet.getString("resourcetype"));
/* 339 */       user.setStartdate(recordSet.getString("startdate"));
/* 340 */       user.setEnddate(recordSet.getString("enddate"));
/* 341 */       user.setContractdate(recordSet.getString("contractdate"));
/* 342 */       user.setJobtitle(recordSet.getString("jobtitle"));
/* 343 */       user.setJobgroup(recordSet.getString("jobgroup"));
/* 344 */       user.setJobactivity(recordSet.getString("jobactivity"));
/* 345 */       user.setJoblevel(recordSet.getString("joblevel"));
/* 346 */       user.setSeclevel(recordSet.getString("seclevel"));
/* 347 */       user.setUserDepartment(Util.getIntValue(recordSet.getString("departmentid"), 0));
/* 348 */       user.setUserSubCompany1(Util.getIntValue(recordSet.getString("subcompanyid1"), 0));
/* 349 */       user.setUserSubCompany2(Util.getIntValue(recordSet.getString("subcompanyid2"), 0));
/* 350 */       user.setUserSubCompany3(Util.getIntValue(recordSet.getString("subcompanyid3"), 0));
/* 351 */       user.setUserSubCompany4(Util.getIntValue(recordSet.getString("subcompanyid4"), 0));
/* 352 */       user.setManagerid(recordSet.getString("managerid"));
/* 353 */       user.setAssistantid(recordSet.getString("assistantid"));
/* 354 */       user.setPurchaselimit(recordSet.getString("purchaselimit"));
/* 355 */       user.setCurrencyid(recordSet.getString("currencyid"));
/* 356 */       user.setLastlogindate(recordSet.getString("currentdate"));
/* 357 */       user.setLogintype("1");
/* 358 */       user.setAccount(recordSet.getString("account"));
/*     */     } else {
/*     */       
/* 361 */       user.setLogintype("1");
/*     */     } 
/* 363 */     return user;
/*     */   }
/*     */   
/* 366 */   private Logger logger = LoggerFactory.getLogger(getClass());
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/workflow/Acc4DocIdConvertor.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */