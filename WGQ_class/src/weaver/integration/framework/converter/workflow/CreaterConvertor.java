/*     */ package weaver.integration.framework.converter.workflow;
/*     */ 
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.framework.Context;
/*     */ import weaver.integration.framework.converter.IConvert;
/*     */ import weaver.integration.framework.data.RecordData;
/*     */ import weaver.integration.framework.mapping.impl.FieldDataMapping;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.workflow.dmlaction.DBTypeUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CreaterConvertor
/*     */   implements IConvert
/*     */ {
/*     */   public Object convert(Object paramObject) {
/*  22 */     FieldDataMapping fieldDataMapping = (FieldDataMapping)paramObject;
/*  23 */     Object object = fieldDataMapping.getSourceField().getFieldValue();
/*  24 */     String str1 = fieldDataMapping.getSourceField().getFieldName();
/*     */     
/*  26 */     Context context = fieldDataMapping.getContext();
/*     */     
/*  28 */     String str2 = context.getChangeType();
/*  29 */     String str3 = context.getCustomsql();
/*  30 */     Map map1 = context.getSystemVariables();
/*  31 */     Map map2 = context.getWorkflowVariables();
/*  32 */     Map map3 = context.getAttributes();
/*  33 */     String str4 = Util.null2String(map3.get("datasourceid"));
/*  34 */     RecordData recordData = context.getRecordData();
/*  35 */     Map map4 = recordData.getFieldData();
/*     */ 
/*     */     
/*  38 */     String str5 = "";
/*  39 */     String str6 = Util.null2String(recordData.getFieldData().get(str1.toLowerCase()));
/*  40 */     if ((str2 == null || "0".equals(str2) || "".equals(str2)) && "".equals(str6)) {
/*  41 */       str5 = "1";
/*  42 */     } else if (str2.equals("5")) {
/*  43 */       str5 = Util.null2String(getCreater(str1, "5"));
/*  44 */     } else if (str2.equals("6")) {
/*     */       
/*  46 */       if (!"".equals(str3)) {
/*  47 */         str5 = getTranSqlValue(str3, str6, str4);
/*  48 */         if (str5 != null && str5.length() > 0) {
/*  49 */           str5 = getCreater(str5, "0");
/*     */         }
/*     */       } else {
/*     */         
/*  53 */         str5 = str6;
/*     */       } 
/*     */     } else {
/*  56 */       str5 = getCreater(str6, str2);
/*     */     } 
/*     */ 
/*     */     
/*  60 */     return str5;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCreater(String paramString1, String paramString2) {
/*  72 */     if (paramString1 == null || paramString1.equals("")) return ""; 
/*  73 */     paramString1 = paramString1.replace("'", "''");
/*  74 */     RecordSet recordSet = new RecordSet();
/*  75 */     String str1 = "";
/*  76 */     String str2 = "";
/*  77 */     if (paramString2.equals("0")) {
/*  78 */       str2 = "select id from HrmResource where id='" + paramString1 + "' and status<4  and (loginid !='' Or  loginid is not  null) ";
/*  79 */     } else if (paramString2.equals("1")) {
/*  80 */       str2 = "select id from HrmResource where workcode='" + paramString1 + "' and  status<4 and (loginid !='' Or  loginid is not  null) ";
/*  81 */     } else if (paramString2.equals("2")) {
/*  82 */       str2 = "select id from HrmResource where loginid='" + paramString1 + "' and  status<4 and (loginid !='' Or  loginid is not  null) ";
/*  83 */     } else if (paramString2.equals("3")) {
/*  84 */       str2 = "select id from HrmResource where mobile='" + paramString1 + "' and  status<4  and (loginid !='' Or  loginid is not  null) ";
/*  85 */     } else if (paramString2.equals("4")) {
/*  86 */       str2 = "select id from HrmResource where email='" + paramString1 + "' and  status<4  and (loginid !='' Or  loginid is not  null) ";
/*  87 */     } else if (paramString2.equals("5")) {
/*  88 */       str2 = "select id from HrmResource where id='" + paramString1 + "' and  status<4  and (loginid !='' Or  loginid is not  null) ";
/*     */     } 
/*     */     
/*  91 */     recordSet.executeQuery(str2, new Object[0]);
/*  92 */     if (recordSet.next()) str1 = recordSet.getString("id");
/*     */     
/*  94 */     return str1;
/*     */   }
/*     */   
/*     */   public String getTranSqlValue(String paramString1, String paramString2, String paramString3) {
/*  98 */     paramString2 = paramString2.replace("'", "''");
/*  99 */     paramString1 = DBTypeUtil.replaceString(paramString1, "{?currentvalue}", paramString2);
/* 100 */     RecordSet recordSet = new RecordSet();
/* 101 */     if (!"".equals(paramString3)) {
/* 102 */       recordSet.executeSql(paramString1, paramString3);
/*     */     } else {
/* 104 */       recordSet.executeSql(paramString1);
/*     */     } 
/* 106 */     if (recordSet.next()) {
/* 107 */       return recordSet.getString(1);
/*     */     }
/* 109 */     return "";
/*     */   }
/*     */   
/* 112 */   private Logger logger = LoggerFactory.getLogger(getClass());
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/workflow/CreaterConvertor.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */