/*    */ package weaver.integration.framework.converter.workflow;
/*    */ 
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.integration.framework.Context;
/*    */ import weaver.integration.framework.converter.IConvert;
/*    */ import weaver.integration.framework.data.RecordData;
/*    */ import weaver.integration.framework.mapping.impl.FieldDataMapping;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ import weaver.workflow.dmlaction.DBTypeUtil;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CustomSQLConvertor
/*    */   implements IConvert
/*    */ {
/* 24 */   private Logger newlog = LoggerFactory.getLogger(CustomSQLConvertor.class);
/*    */ 
/*    */   
/*    */   public Object convert(Object paramObject) {
/*    */     try {
/* 29 */       FieldDataMapping fieldDataMapping = (FieldDataMapping)paramObject;
/* 30 */       Object object = fieldDataMapping.getSourceField().getFieldValue();
/* 31 */       String str1 = fieldDataMapping.getSourceField().getFieldName();
/*    */       
/* 33 */       Context context = fieldDataMapping.getContext();
/* 34 */       String str2 = context.getChangeType();
/* 35 */       String str3 = context.getCustomsql();
/* 36 */       Map map1 = context.getSystemVariables();
/* 37 */       Map map2 = context.getWorkflowVariables();
/* 38 */       Map map3 = context.getAttributes();
/* 39 */       String str4 = Util.null2String(map3.get("datasourceid"));
/* 40 */       RecordData recordData = context.getRecordData();
/* 41 */       Map map4 = recordData.getFieldData();
/*    */       
/* 43 */       String str5 = Util.null2String(recordData.getFieldData().get(str1.toLowerCase()));
/*    */       
/* 45 */       this.newlog.info("字段名 " + str1 + "  |  取值 ：" + str5 + " |  datasourceid :" + str4);
/* 46 */       str5 = str5.replace("'", "''");
/* 47 */       String str6 = DBTypeUtil.replaceString(str3, "{?currentvalue}", str5);
/*    */       
/* 49 */       this.newlog.info(" 转换SQL ：" + str6);
/* 50 */       RecordSet recordSet = new RecordSet();
/* 51 */       if (!"".equals(str4)) {
/* 52 */         recordSet.executeSql(str6, str4);
/*    */       } else {
/* 54 */         recordSet.executeSql(str6);
/*    */       } 
/*    */       
/* 57 */       if (recordSet.next()) {
/* 58 */         return recordSet.getString(1);
/*    */       }
/* 60 */     } catch (Exception exception) {
/* 61 */       this.newlog.error(exception);
/*    */     } 
/*    */     
/* 64 */     return "";
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/converter/workflow/CustomSQLConvertor.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */