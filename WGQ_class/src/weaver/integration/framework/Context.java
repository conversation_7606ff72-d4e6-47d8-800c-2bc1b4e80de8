/*     */ package weaver.integration.framework;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.integration.framework.data.RecordData;
/*     */ import weaver.integration.framework.mapping.impl.FieldDataMapping;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Context
/*     */ {
/*     */   private String fieldId;
/*     */   private String fieldName;
/*     */   private String fieldHtmltype;
/*     */   private String fieldType;
/*     */   private String fieldDBType;
/*     */   private String changeType;
/*     */   private String customsql;
/*     */   private String wffileddbtype;
/*     */   private RecordData recordData;
/*     */   private FieldDataMapping fieldDataMapping;
/*     */   private Map<String, Object> attributes;
/*     */   private Map<String, Object> systemVariables;
/*     */   private Map<String, Object> workflowVariables;
/*     */   
/*     */   public String getFieldDBType() {
/*  27 */     return this.fieldDBType;
/*     */   }
/*     */   
/*     */   public void setFieldDBType(String paramString) {
/*  31 */     this.fieldDBType = paramString;
/*     */   }
/*     */   
/*     */   public String getCustomsql() {
/*  35 */     return this.customsql;
/*     */   }
/*     */   
/*     */   public void setCustomsql(String paramString) {
/*  39 */     this.customsql = paramString;
/*     */   }
/*     */   
/*     */   public String getFieldId() {
/*  43 */     return this.fieldId;
/*     */   }
/*     */   
/*     */   public void setFieldId(String paramString) {
/*  47 */     this.fieldId = paramString;
/*     */   }
/*     */   
/*     */   public String getFieldName() {
/*  51 */     return this.fieldName;
/*     */   }
/*     */   
/*     */   public void setFieldName(String paramString) {
/*  55 */     this.fieldName = paramString;
/*     */   }
/*     */   
/*     */   public String getFieldHtmltype() {
/*  59 */     return this.fieldHtmltype;
/*     */   }
/*     */   
/*     */   public void setFieldHtmltype(String paramString) {
/*  63 */     this.fieldHtmltype = paramString;
/*     */   }
/*     */   
/*     */   public String getFieldType() {
/*  67 */     return this.fieldType;
/*     */   }
/*     */   
/*     */   public void setFieldType(String paramString) {
/*  71 */     this.fieldType = paramString;
/*     */   }
/*     */   
/*     */   public String getChangeType() {
/*  75 */     return this.changeType;
/*     */   }
/*     */   
/*     */   public void setChangeType(String paramString) {
/*  79 */     this.changeType = paramString;
/*     */   }
/*     */   
/*     */   public String getWffileddbtype() {
/*  83 */     return this.wffileddbtype;
/*     */   }
/*     */   
/*     */   public void setWffileddbtype(String paramString) {
/*  87 */     this.wffileddbtype = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Context() {
/*  98 */     this.recordData = new RecordData();
/*  99 */     this.fieldDataMapping = new FieldDataMapping();
/* 100 */     this.attributes = new HashMap<>();
/* 101 */     this.systemVariables = new HashMap<>();
/* 102 */     this.workflowVariables = new HashMap<>();
/*     */   }
/*     */   
/*     */   public Context(RecordData paramRecordData, FieldDataMapping paramFieldDataMapping, Map<String, Object> paramMap1, Map<String, Object> paramMap2, Map<String, Object> paramMap3) {
/* 106 */     this.recordData = paramRecordData;
/* 107 */     this.fieldDataMapping = paramFieldDataMapping;
/* 108 */     this.attributes = paramMap1;
/* 109 */     this.systemVariables = paramMap2;
/* 110 */     this.workflowVariables = paramMap3;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getWorkflowVariables() {
/* 114 */     return this.workflowVariables;
/*     */   }
/*     */   
/*     */   public void setWorkflowVariables(Map<String, Object> paramMap) {
/* 118 */     this.workflowVariables = paramMap;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getSystemVariables() {
/* 122 */     return this.systemVariables;
/*     */   }
/*     */   
/*     */   public void setSystemVariables(Map<String, Object> paramMap) {
/* 126 */     this.systemVariables = paramMap;
/*     */   }
/*     */   
/*     */   public RecordData getRecordData() {
/* 130 */     return this.recordData;
/*     */   }
/*     */   
/*     */   public void setRecordData(RecordData paramRecordData) {
/* 134 */     this.recordData = paramRecordData;
/*     */   }
/*     */   
/*     */   public FieldDataMapping getFieldDataMapping() {
/* 138 */     return this.fieldDataMapping;
/*     */   }
/*     */   
/*     */   public void setFieldDataMapping(FieldDataMapping paramFieldDataMapping) {
/* 142 */     this.fieldDataMapping = paramFieldDataMapping;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getAttributes() {
/* 146 */     return this.attributes;
/*     */   }
/*     */   
/*     */   public void setAttributes(Map<String, Object> paramMap) {
/* 150 */     this.attributes = paramMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/framework/Context.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */