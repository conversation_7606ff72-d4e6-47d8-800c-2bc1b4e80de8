package weaver.integration.whitelist;

import java.io.IOException;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;

public interface WhiteListHandler {
  boolean handleWhiteList(ServletRequest paramServletRequest, ServletResponse paramServletResponse, FilterChain paramFilterChain) throws IOException, ServletException;
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/whitelist/WhiteListHandler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */