/*    */ package weaver.integration.whitelist;
/*    */ 
/*    */ import java.util.List;
/*    */ import javax.servlet.ServletRequest;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import weaver.general.Util;
/*    */ import weaver.integration.cache.WhiteListMappingCache;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WhiteListUtils
/*    */ {
/*    */   public static boolean isWhite(ServletRequest paramServletRequest, String paramString) {
/* 15 */     List list = WhiteListMappingCache.getExclusionsByFilter(paramString);
/* 16 */     HttpServletRequest httpServletRequest = (HttpServletRequest)paramServletRequest;
/* 17 */     String str = Util.null2String(httpServletRequest.getRequestURI());
/* 18 */     boolean bool = false;
/* 19 */     for (String str1 : list) {
/* 20 */       if (str.indexOf(str1) >= 0) {
/* 21 */         bool = true;
/*    */         break;
/*    */       } 
/*    */     } 
/* 25 */     return bool;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/whitelist/WhiteListUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */