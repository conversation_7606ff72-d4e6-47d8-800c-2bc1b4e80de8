/*    */ package weaver.integration.whitelist.service;
/*    */ 
/*    */ import java.util.List;
/*    */ import weaver.crazydream.util.Condition;
/*    */ import weaver.integration.customfield.service.AbstractService;
/*    */ import weaver.integration.customfield.util.CommonEntityService;
/*    */ import weaver.integration.whitelist.entity.WhiteList;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WhiteListService
/*    */   implements AbstractService
/*    */ {
/*    */   public String add(Object paramObject) {
/* 17 */     CommonEntityService commonEntityService = new CommonEntityService(WhiteList.class);
/* 18 */     commonEntityService.addEntity(paramObject);
/* 19 */     return "";
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean update(Object paramObject) {
/* 24 */     CommonEntityService commonEntityService = new CommonEntityService(WhiteList.class);
/* 25 */     return commonEntityService.updateEntity(paramObject);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean delete(String paramString) {
/* 31 */     CommonEntityService commonEntityService = new CommonEntityService(WhiteList.class);
/* 32 */     return commonEntityService.deleteEntity(paramString);
/*    */   }
/*    */ 
/*    */   
/*    */   public WhiteList get(String paramString) {
/* 37 */     CommonEntityService commonEntityService = new CommonEntityService(WhiteList.class);
/* 38 */     return (WhiteList)commonEntityService.getEntity(paramString);
/*    */   }
/*    */ 
/*    */   
/*    */   public List<?> getList(Condition paramCondition) {
/* 43 */     CommonEntityService commonEntityService = new CommonEntityService(WhiteList.class);
/* 44 */     return commonEntityService.getEntitiesByConditionToList(paramCondition);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/whitelist/service/WhiteListService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */