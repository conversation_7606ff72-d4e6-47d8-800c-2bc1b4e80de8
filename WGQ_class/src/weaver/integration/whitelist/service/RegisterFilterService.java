/*    */ package weaver.integration.whitelist.service;
/*    */ 
/*    */ import java.util.List;
/*    */ import weaver.crazydream.util.Condition;
/*    */ import weaver.integration.customfield.service.AbstractService;
/*    */ import weaver.integration.customfield.util.CommonEntityService;
/*    */ import weaver.integration.whitelist.entity.RegisterFilter;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RegisterFilterService
/*    */   implements AbstractService
/*    */ {
/*    */   public String add(Object paramObject) {
/* 20 */     CommonEntityService commonEntityService = new CommonEntityService(RegisterFilter.class);
/* 21 */     commonEntityService.addEntity(paramObject);
/* 22 */     return "";
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean update(Object paramObject) {
/* 27 */     CommonEntityService commonEntityService = new CommonEntityService(RegisterFilter.class);
/* 28 */     return commonEntityService.updateEntity(paramObject);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean delete(String paramString) {
/* 34 */     CommonEntityService commonEntityService = new CommonEntityService(RegisterFilter.class);
/* 35 */     return commonEntityService.deleteEntity(paramString);
/*    */   }
/*    */ 
/*    */   
/*    */   public RegisterFilter get(String paramString) {
/* 40 */     CommonEntityService commonEntityService = new CommonEntityService(RegisterFilter.class);
/* 41 */     return (RegisterFilter)commonEntityService.getEntity(paramString);
/*    */   }
/*    */ 
/*    */   
/*    */   public List<?> getList(Condition paramCondition) {
/* 46 */     CommonEntityService commonEntityService = new CommonEntityService(RegisterFilter.class);
/* 47 */     return commonEntityService.getEntitiesByConditionToList(paramCondition);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/whitelist/service/RegisterFilterService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */