/*    */ package weaver.integration.whitelist.entity;
/*    */ 
/*    */ import javax.persistence.Column;
/*    */ import javax.persistence.Table;
/*    */ import weaver.integration.customfield.entity.AbstractEntity;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Table(name = "Int_RegisterFilter")
/*    */ public class RegisterFilter
/*    */   extends AbstractEntity
/*    */ {
/*    */   private String name;
/*    */   private String clazz;
/*    */   private String desc;
/*    */   private int orderNum;
/*    */   
/*    */   @Column(name = "name_")
/*    */   public String getName() {
/* 21 */     return this.name;
/*    */   }
/*    */   
/*    */   public void setName(String paramString) {
/* 25 */     this.name = paramString;
/*    */   }
/*    */   
/*    */   @Column(name = "clazz_")
/*    */   public String getClazz() {
/* 30 */     return this.clazz;
/*    */   }
/*    */   
/*    */   public void setClazz(String paramString) {
/* 34 */     this.clazz = paramString;
/*    */   }
/*    */   
/*    */   @Column(name = "desc_")
/*    */   public String getDesc() {
/* 39 */     return this.desc;
/*    */   }
/*    */   
/*    */   public void setDesc(String paramString) {
/* 43 */     this.desc = paramString;
/*    */   }
/*    */   
/*    */   @Column(name = "orderNum")
/*    */   public int getOrderNum() {
/* 48 */     return this.orderNum;
/*    */   }
/*    */   
/*    */   public void setOrderNum(int paramInt) {
/* 52 */     this.orderNum = paramInt;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/whitelist/entity/RegisterFilter.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */