/*    */ package weaver.integration.whitelist.entity;
/*    */ 
/*    */ import javax.persistence.Column;
/*    */ import javax.persistence.Table;
/*    */ import weaver.integration.customfield.entity.AbstractEntity;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Table(name = "Int_WhiteList")
/*    */ public class WhiteList
/*    */   extends AbstractEntity
/*    */ {
/*    */   private String exclude;
/*    */   private String type;
/*    */   private String desc;
/*    */   private int orderNum;
/*    */   private String is_sys;
/*    */   
/*    */   @Column(name = "is_sys")
/*    */   public String getIs_sys() {
/* 22 */     return this.is_sys;
/*    */   }
/*    */   
/*    */   public void setIs_sys(String paramString) {
/* 26 */     this.is_sys = paramString;
/*    */   }
/*    */   
/*    */   @Column(name = "exclude_")
/*    */   public String getExclude() {
/* 31 */     return this.exclude;
/*    */   }
/*    */   
/*    */   public void setExclude(String paramString) {
/* 35 */     this.exclude = paramString;
/*    */   }
/*    */   
/*    */   @Column(name = "type_")
/*    */   public String getType() {
/* 40 */     return this.type;
/*    */   }
/*    */   
/*    */   public void setType(String paramString) {
/* 44 */     this.type = paramString;
/*    */   }
/*    */   
/*    */   @Column(name = "desc_")
/*    */   public String getDesc() {
/* 49 */     return this.desc;
/*    */   }
/*    */   
/*    */   public void setDesc(String paramString) {
/* 53 */     this.desc = paramString;
/*    */   }
/*    */   
/*    */   @Column(name = "orderNum")
/*    */   public int getOrderNum() {
/* 58 */     return this.orderNum;
/*    */   }
/*    */   
/*    */   public void setOrderNum(int paramInt) {
/* 62 */     this.orderNum = paramInt;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/whitelist/entity/WhiteList.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */