/*    */ package weaver.integration.whitelist.entity;
/*    */ 
/*    */ import javax.persistence.Column;
/*    */ import javax.persistence.Table;
/*    */ import weaver.integration.customfield.entity.AbstractEntity;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Table(name = "Int_WhiteList_Mapping")
/*    */ public class WhiteListMapping
/*    */   extends AbstractEntity
/*    */ {
/*    */   private int whiteListId;
/*    */   private int filterId;
/*    */   
/*    */   @Column(name = "whitelist_id")
/*    */   public int getWhiteListId() {
/* 19 */     return this.whiteListId;
/*    */   }
/*    */   
/*    */   public void setWhiteListId(int paramInt) {
/* 23 */     this.whiteListId = paramInt;
/*    */   }
/*    */   
/*    */   @Column(name = "filter_id")
/*    */   public int getFilterId() {
/* 28 */     return this.filterId;
/*    */   }
/*    */   
/*    */   public void setFilterId(int paramInt) {
/* 32 */     this.filterId = paramInt;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/whitelist/entity/WhiteListMapping.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */