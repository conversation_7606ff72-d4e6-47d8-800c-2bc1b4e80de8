/*    */ package weaver.integration.customfield.ddl;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.Iterator;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.integration.customfield.entity.ModuleDDLDef;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DDLActionImpl
/*    */   implements DDLAction
/*    */ {
/* 18 */   private Logger logger = LoggerFactory.getLogger(DDLActionImpl.class);
/*    */   
/*    */   public Map<String, Object> execute(ModuleDDLDef paramModuleDDLDef, Map<String, Object> paramMap) {
/* 21 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 22 */     boolean bool = false;
/* 23 */     if (paramModuleDDLDef != null) {
/* 24 */       String str1 = paramModuleDDLDef.getDdlclazz();
/* 25 */       String str2 = paramModuleDDLDef.getDdltemplate();
/* 26 */       if (str1 == null || "".equals(str1)) {
/*    */         
/* 28 */         if (str2 != null && str2.length() > 0) {
/* 29 */           if (paramMap != null && paramMap.size() > 0) {
/* 30 */             for (Iterator<String> iterator = paramMap.keySet().iterator(); iterator.hasNext(); ) {
/* 31 */               String str3 = Util.null2String(iterator.next());
/* 32 */               String str4 = paramMap.get(str3).toString();
/* 33 */               str2 = str2.replace("$" + str3 + "$", str4);
/*    */             } 
/*    */           }
/* 36 */           this.logger.info("=================execute ddl:" + str2);
/* 37 */           RecordSet recordSet = new RecordSet();
/* 38 */           bool = recordSet.execute(str2);
/*    */         } 
/*    */       } else {
/*    */ 
/*    */         
/*    */         try {
/* 44 */           Map<String, Object> map = ((DDLAction)Class.forName(str1).newInstance()).execute(paramModuleDDLDef, paramMap);
/* 45 */           if (map != null && map.containsKey("result")) {
/* 46 */             bool = ((Boolean)map.get("result")).booleanValue();
/*    */           }
/* 48 */         } catch (Exception exception) {
/* 49 */           this.logger.error("========================error occured!!!" + exception.getMessage());
/* 50 */           hashMap.put("result", Boolean.valueOf(false));
/* 51 */           exception.printStackTrace();
/*    */         } 
/*    */       } 
/*    */     } 
/* 55 */     hashMap.put("result", Boolean.valueOf(bool));
/*    */     
/* 57 */     return (Map)hashMap;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Object> execute(String paramString, Map<String, Object> paramMap) {
/* 62 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/customfield/ddl/DDLActionImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */