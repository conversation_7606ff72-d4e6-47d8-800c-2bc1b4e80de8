package weaver.integration.customfield.ddl;

import java.util.Map;
import weaver.integration.customfield.entity.ModuleDDLDef;

public interface DDLAction {
  Map<String, Object> execute(ModuleDDLDef paramModuleDDLDef, Map<String, Object> paramMap);
  
  Map<String, Object> execute(String paramString, Map<String, Object> paramMap);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/customfield/ddl/DDLAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */