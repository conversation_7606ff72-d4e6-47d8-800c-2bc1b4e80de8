/*     */ package weaver.integration.customfield.util;
/*     */ import java.lang.annotation.Annotation;
/*     */ import java.lang.reflect.Method;
/*     */ import java.sql.Blob;
/*     */ import java.sql.Clob;
/*     */ import java.sql.Connection;
/*     */ import java.sql.Date;
/*     */ import java.sql.PreparedStatement;
/*     */ import java.sql.ResultSet;
/*     */ import java.sql.SQLException;
/*     */ import java.sql.Timestamp;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.Map;
/*     */ import javax.persistence.Column;
/*     */ import weaver.conn.ConnectionPool;
/*     */ import weaver.conn.WeaverConnection;
/*     */ import weaver.crazydream.util.Condition;
/*     */ 
/*     */ public class CommonEntityService {
/*  22 */   private Class<?> clazz = null;
/*     */   
/*     */   public Class<?> getClazz() {
/*  25 */     return this.clazz;
/*     */   }
/*     */   
/*     */   public void setClazz(Class<?> paramClass) {
/*  29 */     this.clazz = paramClass;
/*     */   }
/*     */ 
/*     */   
/*     */   public CommonEntityService(Class<?> paramClass) {
/*  34 */     this.clazz = paramClass;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private CommonEntityService() {}
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Connection getConnection() {
/*  47 */     return (Connection)ConnectionPool.getInstance().getConnection();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getEntityIdName() {
/*  58 */     Method[] arrayOfMethod = this.clazz.getMethods();
/*  59 */     String str = null;
/*  60 */     for (Method method : arrayOfMethod) {
/*  61 */       boolean bool = false;
/*  62 */       Annotation[] arrayOfAnnotation = method.getAnnotations();
/*  63 */       for (Annotation annotation : arrayOfAnnotation) {
/*  64 */         if (annotation instanceof javax.persistence.Id) {
/*  65 */           bool = true;
/*     */           break;
/*     */         } 
/*     */       } 
/*  69 */       if (bool) {
/*  70 */         for (byte b = 0; b < arrayOfAnnotation.length; b++) {
/*  71 */           if (arrayOfAnnotation[b] instanceof Column) {
/*  72 */             str = ((Column)arrayOfAnnotation[b]).name();
/*     */             break;
/*     */           } 
/*     */         } 
/*     */         break;
/*     */       } 
/*     */     } 
/*  79 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getTableName() {
/*  89 */     String str = null;
/*  90 */     Annotation[] arrayOfAnnotation = this.clazz.getAnnotations();
/*  91 */     for (Annotation annotation : arrayOfAnnotation) {
/*  92 */       if (annotation instanceof Table) {
/*  93 */         Table table = (Table)annotation;
/*  94 */         str = table.name();
/*     */       } 
/*     */     } 
/*  97 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Method getIdMethod() {
/* 107 */     Method[] arrayOfMethod = this.clazz.getMethods();
/* 108 */     Annotation[] arrayOfAnnotation = null;
/* 109 */     for (Method method : arrayOfMethod) {
/* 110 */       arrayOfAnnotation = method.getDeclaredAnnotations();
/* 111 */       for (Annotation annotation : arrayOfAnnotation) {
/* 112 */         if (annotation instanceof javax.persistence.Id) {
/* 113 */           return method;
/*     */         }
/*     */       } 
/*     */     } 
/* 117 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Map<String, Method> getGetMethodNoId() {
/* 128 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 130 */     Method[] arrayOfMethod = this.clazz.getMethods();
/* 131 */     Annotation[] arrayOfAnnotation = null;
/* 132 */     for (Method method : arrayOfMethod) {
/*     */       
/* 134 */       arrayOfAnnotation = method.getAnnotations();
/* 135 */       if (arrayOfAnnotation.length <= 1)
/*     */       {
/*     */         
/* 138 */         for (Annotation annotation : arrayOfAnnotation) {
/* 139 */           if (annotation instanceof Column) {
/* 140 */             Column column = (Column)annotation;
/* 141 */             hashMap.put(column.name(), method);
/*     */             
/*     */             break;
/*     */           } 
/*     */         } 
/*     */       }
/*     */     } 
/* 148 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Map<String, Method> getSetMethod() {
/* 159 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 160 */     Method[] arrayOfMethod = this.clazz.getMethods();
/* 161 */     Annotation[] arrayOfAnnotation = null;
/* 162 */     for (Method method : arrayOfMethod) {
/* 163 */       arrayOfAnnotation = method.getDeclaredAnnotations();
/* 164 */       for (Annotation annotation : arrayOfAnnotation) {
/* 165 */         if (annotation instanceof Column) {
/* 166 */           Column column = (Column)annotation;
/*     */           
/* 168 */           String str = method.getName().replaceFirst("get", "set");
/*     */           
/* 170 */           Method method1 = null;
/*     */           try {
/* 172 */             method1 = this.clazz.getMethod(str, new Class[] { method
/* 173 */                   .getReturnType() });
/* 174 */           } catch (Exception exception) {
/* 175 */             exception.printStackTrace();
/*     */           } 
/* 177 */           hashMap.put(column.name(), method1);
/*     */         } 
/*     */       } 
/*     */     } 
/* 181 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Object getResultsetFromSetmethodParameter(Class<?> paramClass, ResultSet paramResultSet, String paramString) {
/* 194 */     String str = null;
/*     */     try {
/* 196 */       if (paramClass == String.class) {
/* 197 */         str = paramResultSet.getString(paramString);
/* 198 */       } else if (paramClass == int.class) {
/* 199 */         Integer integer = Integer.valueOf(paramResultSet.getInt(paramString));
/* 200 */       } else if (paramClass == double.class) {
/* 201 */         Double double_ = Double.valueOf(paramResultSet.getDouble(paramString));
/* 202 */       } else if (paramClass == float.class) {
/* 203 */         Float float_ = Float.valueOf(paramResultSet.getFloat(paramString));
/* 204 */       } else if (paramClass == Date.class) {
/* 205 */         Date date = paramResultSet.getDate(paramString);
/* 206 */       } else if (paramClass == Timestamp.class) {
/* 207 */         Timestamp timestamp = paramResultSet.getTimestamp(paramString);
/* 208 */       } else if (paramClass == long.class) {
/* 209 */         Long long_ = Long.valueOf(paramResultSet.getLong(paramString));
/* 210 */       } else if (paramClass == Blob.class) {
/* 211 */         Blob blob = paramResultSet.getBlob(paramString);
/* 212 */       } else if (paramClass == Clob.class) {
/* 213 */         Clob clob = paramResultSet.getClob(paramString);
/* 214 */         if (clob != null) {
/* 215 */           Clob clob1 = clob;
/* 216 */           str = clob1.getSubString(1L, (int)clob1.length());
/*     */         }
/*     */       
/*     */       } 
/* 220 */     } catch (Exception exception) {
/* 221 */       exception.printStackTrace();
/*     */     } 
/* 223 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void setPreparedstatementObjectFromGetmethodReturn(Class<?> paramClass, PreparedStatement paramPreparedStatement, int paramInt, Object paramObject) {
/*     */     try {
/* 241 */       if (paramClass == String.class) {
/* 242 */         paramPreparedStatement.setString(paramInt, (String)paramObject);
/* 243 */       } else if (paramClass == int.class) {
/* 244 */         paramPreparedStatement.setInt(paramInt, ((Integer)paramObject).intValue());
/* 245 */       } else if (paramClass == double.class) {
/* 246 */         paramPreparedStatement.setDouble(paramInt, ((Double)paramObject).doubleValue());
/* 247 */       } else if (paramClass == float.class) {
/* 248 */         paramPreparedStatement.setFloat(paramInt, ((Float)paramObject).floatValue());
/* 249 */       } else if (paramClass == Date.class) {
/* 250 */         paramPreparedStatement.setDate(paramInt, (Date)paramObject);
/* 251 */       } else if (paramClass == Timestamp.class) {
/* 252 */         paramPreparedStatement.setTimestamp(paramInt, (Timestamp)paramObject);
/* 253 */       } else if (paramClass == long.class) {
/* 254 */         paramPreparedStatement.setLong(paramInt, ((Long)paramObject).longValue());
/* 255 */       } else if (paramClass == Blob.class) {
/* 256 */         paramPreparedStatement.setBlob(paramInt, (Blob)paramObject);
/* 257 */       } else if (paramClass == Clob.class) {
/* 258 */         paramPreparedStatement.setClob(paramInt, (Clob)paramObject);
/*     */       } 
/* 260 */     } catch (Exception exception) {
/* 261 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Object getEntity(String paramString) {
/* 273 */     Map<String, Method> map = getSetMethod();
/*     */     
/* 275 */     StringBuffer stringBuffer = new StringBuffer("select ");
/* 276 */     Iterator<String> iterator = map.keySet().iterator();
/* 277 */     while (iterator.hasNext()) {
/* 278 */       stringBuffer.append(iterator.next()).append(",");
/*     */     }
/* 280 */     stringBuffer.deleteCharAt(stringBuffer.length() - 1).append(" from ")
/* 281 */       .append(getTableName()).append(" where ")
/* 282 */       .append(getEntityIdName()).append("=?");
/*     */     
/* 284 */     iterator = null;
/* 285 */     Connection connection = getConnection();
/* 286 */     PreparedStatement preparedStatement = null;
/* 287 */     ResultSet resultSet = null;
/*     */     try {
/* 289 */       preparedStatement = connection.prepareStatement(stringBuffer.toString());
/* 290 */       preparedStatement.setString(1, paramString);
/* 291 */       resultSet = preparedStatement.executeQuery();
/* 292 */       if (resultSet.next()) {
/* 293 */         iterator = (Iterator<String>)this.clazz.newInstance();
/* 294 */         Iterator<String> iterator1 = map.keySet().iterator();
/* 295 */         while (iterator1.hasNext()) {
/* 296 */           String str = iterator1.next();
/* 297 */           Method method = map.get(str);
/* 298 */           Object object = getResultsetFromSetmethodParameter(method
/* 299 */               .getParameterTypes()[0], resultSet, str);
/* 300 */           ((Method)map.get(str)).invoke(iterator, new Object[] { object });
/*     */         }
/*     */       
/*     */       } 
/* 304 */     } catch (Exception exception) {
/* 305 */       exception.printStackTrace();
/*     */     } finally {
/* 307 */       closeResultSet(resultSet);
/* 308 */       closePreparedStatement(preparedStatement);
/* 309 */       ConnectionPool.getInstance().returnConnection((WeaverConnection)connection);
/*     */     } 
/* 311 */     return iterator;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getAllEntitiesToMap() {
/*     */     HashMap<Object, Object> hashMap;
/* 320 */     Map<String, Method> map = getSetMethod();
/*     */     
/* 322 */     StringBuffer stringBuffer = new StringBuffer("select ");
/* 323 */     Iterator<String> iterator = map.keySet().iterator();
/* 324 */     while (iterator.hasNext()) {
/* 325 */       stringBuffer.append(iterator.next()).append(",");
/*     */     }
/* 327 */     stringBuffer.deleteCharAt(stringBuffer.length() - 1).append(" from ")
/* 328 */       .append(getTableName());
/*     */     
/* 330 */     iterator = null;
/* 331 */     Connection connection = getConnection();
/* 332 */     PreparedStatement preparedStatement = null;
/* 333 */     ResultSet resultSet = null;
/*     */     try {
/* 335 */       preparedStatement = connection.prepareStatement(stringBuffer.toString());
/* 336 */       resultSet = preparedStatement.executeQuery();
/* 337 */       hashMap = new HashMap<>();
/* 338 */       while (resultSet.next()) {
/* 339 */         Object object = this.clazz.newInstance();
/* 340 */         Iterator<String> iterator1 = map.keySet().iterator();
/* 341 */         while (iterator1.hasNext()) {
/* 342 */           String str1 = iterator1.next();
/* 343 */           Method method = map.get(str1);
/* 344 */           Object object1 = getResultsetFromSetmethodParameter(method
/* 345 */               .getParameterTypes()[0], resultSet, str1);
/* 346 */           ((Method)map.get(str1)).invoke(object, new Object[] { object1 });
/*     */         } 
/* 348 */         String str = (String)getIdMethod().invoke(object, new Object[0]);
/* 349 */         hashMap.put(str, object);
/*     */       } 
/* 351 */     } catch (Exception exception) {
/* 352 */       exception.printStackTrace();
/*     */     } finally {
/* 354 */       closeResultSet(resultSet);
/* 355 */       closePreparedStatement(preparedStatement);
/* 356 */       ConnectionPool.getInstance().returnConnection((WeaverConnection)connection);
/*     */     } 
/* 358 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getEntitiesByConditionToMap(Condition paramCondition) {
/*     */     HashMap<Object, Object> hashMap;
/* 367 */     Map<String, Method> map = getSetMethod();
/*     */     
/* 369 */     StringBuffer stringBuffer = new StringBuffer("select ");
/* 370 */     Iterator<String> iterator = map.keySet().iterator();
/* 371 */     while (iterator.hasNext()) {
/* 372 */       stringBuffer.append(iterator.next()).append(",");
/*     */     }
/* 374 */     stringBuffer.deleteCharAt(stringBuffer.length() - 1).append(" from ")
/* 375 */       .append(getTableName());
/*     */     
/* 377 */     stringBuffer.append(paramCondition.getSqlString());
/*     */     
/* 379 */     iterator = null;
/* 380 */     Connection connection = getConnection();
/* 381 */     PreparedStatement preparedStatement = null;
/* 382 */     ResultSet resultSet = null;
/*     */     try {
/* 384 */       preparedStatement = connection.prepareStatement(stringBuffer.toString());
/* 385 */       resultSet = preparedStatement.executeQuery();
/* 386 */       hashMap = new HashMap<>();
/* 387 */       while (resultSet.next()) {
/* 388 */         Object object = this.clazz.newInstance();
/* 389 */         Iterator<String> iterator1 = map.keySet().iterator();
/* 390 */         while (iterator1.hasNext()) {
/* 391 */           String str1 = iterator1.next();
/* 392 */           Method method = map.get(str1);
/* 393 */           Object object1 = getResultsetFromSetmethodParameter(method
/* 394 */               .getParameterTypes()[0], resultSet, str1);
/* 395 */           ((Method)map.get(str1)).invoke(object, new Object[] { object1 });
/*     */         } 
/* 397 */         String str = (String)getIdMethod().invoke(object, new Object[0]);
/* 398 */         hashMap.put(str, object);
/*     */       } 
/* 400 */     } catch (Exception exception) {
/* 401 */       exception.printStackTrace();
/*     */     } finally {
/* 403 */       closeResultSet(resultSet);
/* 404 */       closePreparedStatement(preparedStatement);
/* 405 */       ConnectionPool.getInstance().returnConnection((WeaverConnection)connection);
/*     */     } 
/* 407 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<?> getAllEntitiesToList() {
/*     */     ArrayList<Object> arrayList;
/* 416 */     Map<String, Method> map = getSetMethod();
/*     */     
/* 418 */     StringBuffer stringBuffer = new StringBuffer("select ");
/* 419 */     Iterator<String> iterator = map.keySet().iterator();
/* 420 */     while (iterator.hasNext()) {
/* 421 */       stringBuffer.append(iterator.next()).append(",");
/*     */     }
/* 423 */     stringBuffer.deleteCharAt(stringBuffer.length() - 1).append(" from ")
/* 424 */       .append(getTableName());
/*     */     
/* 426 */     iterator = null;
/* 427 */     Connection connection = getConnection();
/* 428 */     PreparedStatement preparedStatement = null;
/* 429 */     ResultSet resultSet = null;
/*     */     try {
/* 431 */       preparedStatement = connection.prepareStatement(stringBuffer.toString());
/* 432 */       resultSet = preparedStatement.executeQuery();
/* 433 */       arrayList = new ArrayList();
/* 434 */       while (resultSet.next()) {
/* 435 */         Object object = this.clazz.newInstance();
/* 436 */         Iterator<String> iterator1 = map.keySet().iterator();
/* 437 */         while (iterator1.hasNext()) {
/* 438 */           String str = iterator1.next();
/* 439 */           Method method = map.get(str);
/* 440 */           Object object1 = getResultsetFromSetmethodParameter(method
/* 441 */               .getParameterTypes()[0], resultSet, str);
/* 442 */           ((Method)map.get(str)).invoke(object, new Object[] { object1 });
/*     */         } 
/* 444 */         arrayList.add(object);
/*     */       } 
/* 446 */     } catch (Exception exception) {
/* 447 */       exception.printStackTrace();
/*     */     } finally {
/* 449 */       closeResultSet(resultSet);
/* 450 */       closePreparedStatement(preparedStatement);
/* 451 */       ConnectionPool.getInstance().returnConnection((WeaverConnection)connection);
/*     */     } 
/* 453 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<?> getEntitiesByConditionToList(Condition paramCondition) {
/*     */     ArrayList<Object> arrayList;
/* 465 */     Map<String, Method> map = getSetMethod();
/*     */     
/* 467 */     StringBuffer stringBuffer = new StringBuffer("select ");
/* 468 */     Iterator<String> iterator = map.keySet().iterator();
/* 469 */     while (iterator.hasNext()) {
/* 470 */       stringBuffer.append(iterator.next()).append(",");
/*     */     }
/* 472 */     stringBuffer.deleteCharAt(stringBuffer.length() - 1).append(" from ")
/* 473 */       .append(getTableName());
/*     */     
/* 475 */     stringBuffer.append(paramCondition.getSqlString());
/*     */ 
/*     */     
/* 478 */     iterator = null;
/* 479 */     Connection connection = getConnection();
/* 480 */     PreparedStatement preparedStatement = null;
/* 481 */     ResultSet resultSet = null;
/*     */     try {
/* 483 */       preparedStatement = connection.prepareStatement(stringBuffer.toString());
/* 484 */       resultSet = preparedStatement.executeQuery();
/* 485 */       arrayList = new ArrayList();
/* 486 */       while (resultSet.next()) {
/* 487 */         Object object = this.clazz.newInstance();
/* 488 */         Iterator<String> iterator1 = map.keySet().iterator();
/* 489 */         while (iterator1.hasNext()) {
/* 490 */           String str = iterator1.next();
/* 491 */           Method method = map.get(str);
/* 492 */           Object object1 = getResultsetFromSetmethodParameter(method
/* 493 */               .getParameterTypes()[0], resultSet, str);
/* 494 */           ((Method)map.get(str)).invoke(object, new Object[] { object1 });
/*     */         } 
/* 496 */         arrayList.add(object);
/*     */       } 
/* 498 */     } catch (Exception exception) {
/* 499 */       exception.printStackTrace();
/*     */     } finally {
/* 501 */       closeResultSet(resultSet);
/* 502 */       closePreparedStatement(preparedStatement);
/* 503 */       ConnectionPool.getInstance().returnConnection((WeaverConnection)connection);
/*     */     } 
/* 505 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean deleteEntity(String paramString) {
/* 516 */     StringBuffer stringBuffer = new StringBuffer("delete from ");
/* 517 */     stringBuffer.append(getTableName()).append(" where ").append(getEntityIdName())
/* 518 */       .append(" =?");
/*     */     
/* 520 */     boolean bool = false;
/* 521 */     Connection connection = getConnection();
/* 522 */     PreparedStatement preparedStatement = null;
/*     */     try {
/* 524 */       preparedStatement = connection.prepareStatement(stringBuffer.toString());
/* 525 */       preparedStatement.setString(1, paramString);
/* 526 */       bool = (preparedStatement.executeUpdate() > 0) ? true : false;
/*     */       
/* 528 */       connection.commit();
/*     */     }
/* 530 */     catch (SQLException sQLException) {
/* 531 */       sQLException.printStackTrace();
/*     */     } finally {
/* 533 */       closePreparedStatement(preparedStatement);
/* 534 */       ConnectionPool.getInstance().returnConnection((WeaverConnection)connection);
/*     */     } 
/* 536 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteEntitiesByCondition(Condition paramCondition) {
/* 547 */     StringBuffer stringBuffer = new StringBuffer("delete from ");
/* 548 */     stringBuffer.append(getTableName()).append(paramCondition.getSqlString());
/*     */     
/* 550 */     Connection connection = getConnection();
/* 551 */     PreparedStatement preparedStatement = null;
/*     */     try {
/* 553 */       preparedStatement = connection.prepareStatement(stringBuffer.toString());
/* 554 */       preparedStatement.executeUpdate();
/* 555 */       connection.commit();
/*     */     }
/* 557 */     catch (SQLException sQLException) {
/* 558 */       sQLException.printStackTrace();
/*     */     } finally {
/* 560 */       closePreparedStatement(preparedStatement);
/* 561 */       ConnectionPool.getInstance().returnConnection((WeaverConnection)connection);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean updateEntity(Object paramObject) {
/* 573 */     if (paramObject.getClass() != this.clazz) {
/* 574 */       return false;
/*     */     }
/*     */     
/* 577 */     StringBuffer stringBuffer = new StringBuffer("update ");
/* 578 */     stringBuffer.append(getTableName()).append(" set ");
/* 579 */     Map<String, Method> map = getGetMethodNoId();
/* 580 */     Iterator<String> iterator = map.keySet().iterator();
/* 581 */     while (iterator.hasNext()) {
/* 582 */       stringBuffer.append(iterator.next()).append("=?,");
/*     */     }
/* 584 */     stringBuffer.deleteCharAt(stringBuffer.length() - 1).append(" where ")
/* 585 */       .append(getEntityIdName()).append("=?");
/*     */     
/* 587 */     boolean bool = false;
/* 588 */     Connection connection = getConnection();
/* 589 */     PreparedStatement preparedStatement = null;
/*     */     try {
/* 591 */       preparedStatement = connection.prepareStatement(stringBuffer.toString());
/* 592 */       byte b = 1;
/* 593 */       Iterator<String> iterator1 = map.keySet().iterator();
/* 594 */       while (iterator1.hasNext()) {
/* 595 */         String str = iterator1.next();
/* 596 */         Method method1 = map.get(str);
/* 597 */         Object object = method1.invoke(paramObject, new Object[0]);
/* 598 */         setPreparedstatementObjectFromGetmethodReturn(method1
/* 599 */             .getReturnType(), preparedStatement, b, object);
/* 600 */         b++;
/*     */       } 
/* 602 */       Method method = getIdMethod();
/* 603 */       setPreparedstatementObjectFromGetmethodReturn(method
/* 604 */           .getReturnType(), preparedStatement, b, method
/* 605 */           .invoke(paramObject, new Object[0]));
/* 606 */       bool = (preparedStatement.executeUpdate() > 0) ? true : false;
/*     */ 
/*     */       
/* 609 */       connection.commit();
/*     */     }
/* 611 */     catch (Exception exception) {
/* 612 */       exception.printStackTrace();
/*     */     } finally {
/* 614 */       closePreparedStatement(preparedStatement);
/* 615 */       ConnectionPool.getInstance().returnConnection((WeaverConnection)connection);
/*     */     } 
/* 617 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String addEntity(Object paramObject) {
/* 628 */     if (paramObject.getClass() != this.clazz) {
/* 629 */       return "";
/*     */     }
/*     */     
/* 632 */     Map<String, Method> map = getGetMethodNoId();
/*     */     
/* 634 */     StringBuffer stringBuffer = new StringBuffer("insert into ");
/* 635 */     stringBuffer.append(getTableName()).append("(");
/* 636 */     byte b1 = 0;
/* 637 */     Iterator<String> iterator = map.keySet().iterator();
/* 638 */     while (iterator.hasNext()) {
/* 639 */       stringBuffer.append(iterator.next()).append(",");
/* 640 */       b1++;
/*     */     } 
/* 642 */     stringBuffer.deleteCharAt(stringBuffer.length() - 1).append(") values(");
/* 643 */     for (byte b2 = 0; b2 < b1; b2++) {
/* 644 */       stringBuffer.append("?,");
/*     */     }
/* 646 */     stringBuffer.deleteCharAt(stringBuffer.length() - 1).append(")");
/*     */ 
/*     */     
/* 649 */     String str = "";
/* 650 */     Connection connection = getConnection();
/* 651 */     PreparedStatement preparedStatement = null;
/* 652 */     Object object = null;
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 658 */       preparedStatement = connection.prepareStatement(stringBuffer.toString());
/* 659 */       byte b = 1;
/* 660 */       Iterator<String> iterator1 = map.keySet().iterator();
/* 661 */       while (iterator1.hasNext()) {
/* 662 */         String str1 = iterator1.next();
/* 663 */         Method method = map.get(str1);
/* 664 */         Object object1 = method.invoke(paramObject, new Object[0]);
/* 665 */         setPreparedstatementObjectFromGetmethodReturn(method
/* 666 */             .getReturnType(), preparedStatement, b, object1);
/* 667 */         b++;
/*     */       } 
/*     */ 
/*     */       
/* 671 */       preparedStatement.executeUpdate();
/*     */ 
/*     */       
/* 674 */       connection.commit();
/*     */     }
/* 676 */     catch (Exception exception) {
/* 677 */       exception.printStackTrace();
/*     */     } finally {
/* 679 */       closePreparedStatement(preparedStatement);
/* 680 */       ConnectionPool.getInstance().returnConnection((WeaverConnection)connection);
/*     */     } 
/* 682 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String generatedId() {
/* 692 */     return UUID.randomUUID().toString();
/*     */   }
/*     */   
/*     */   private void closeResultSet(ResultSet paramResultSet) {
/* 696 */     if (paramResultSet != null) {
/*     */       try {
/* 698 */         paramResultSet.close();
/* 699 */       } catch (SQLException sQLException) {
/* 700 */         sQLException.printStackTrace();
/*     */       } 
/*     */     }
/*     */   }
/*     */   
/*     */   private void closePreparedStatement(PreparedStatement paramPreparedStatement) {
/* 706 */     if (paramPreparedStatement != null)
/*     */       try {
/* 708 */         paramPreparedStatement.close();
/* 709 */       } catch (SQLException sQLException) {
/* 710 */         sQLException.printStackTrace();
/*     */       }  
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/customfield/util/CommonEntityService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */