/*    */ package weaver.integration.customfield.service;
/*    */ 
/*    */ import java.util.List;
/*    */ import weaver.crazydream.util.Condition;
/*    */ import weaver.integration.customfield.entity.ModuleDDLDef;
/*    */ import weaver.integration.customfield.util.CommonEntityService;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ModuleDDLDefService
/*    */   implements AbstractService
/*    */ {
/*    */   public String add(Object paramObject) {
/* 17 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleDDLDef.class);
/* 18 */     commonEntityService.addEntity(paramObject);
/* 19 */     return "";
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean update(Object paramObject) {
/* 24 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleDDLDef.class);
/* 25 */     return commonEntityService.updateEntity(paramObject);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean delete(String paramString) {
/* 31 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleDDLDef.class);
/* 32 */     return commonEntityService.deleteEntity(paramString);
/*    */   }
/*    */ 
/*    */   
/*    */   public ModuleDDLDef get(String paramString) {
/* 37 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleDDLDef.class);
/* 38 */     return (ModuleDDLDef)commonEntityService.getEntity(paramString);
/*    */   }
/*    */ 
/*    */   
/*    */   public List<?> getList(Condition paramCondition) {
/* 43 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleDDLDef.class);
/* 44 */     return commonEntityService.getEntitiesByConditionToList(paramCondition);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void initDDLCustomFieldTemplateData() {
/* 52 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleDDLDef.class);
/* 53 */     ModuleDDLDef moduleDDLDef = new ModuleDDLDef();
/*    */     
/* 55 */     String str = " ALTER TABLE $tableName$ ADD $fieldName$ $fieldDbType$ ";
/*    */ 
/*    */ 
/*    */     
/* 59 */     moduleDDLDef.setDbtype("sqlserver");
/* 60 */     moduleDDLDef.setDdltype("AddField");
/* 61 */     moduleDDLDef.setDdltemplate(str);
/* 62 */     commonEntityService.addEntity(moduleDDLDef);
/*    */     
/* 64 */     moduleDDLDef.setDbtype("oracle");
/* 65 */     moduleDDLDef.setDdltemplate(str);
/* 66 */     commonEntityService.addEntity(moduleDDLDef);
/*    */     
/* 68 */     moduleDDLDef.setDbtype("mysql");
/* 69 */     moduleDDLDef.setDdltemplate(str);
/* 70 */     commonEntityService.addEntity(moduleDDLDef);
/* 71 */     moduleDDLDef.setDbtype("postgresql");
/* 72 */     moduleDDLDef.setDdltemplate(str);
/* 73 */     commonEntityService.addEntity(moduleDDLDef);
/*    */ 
/*    */     
/* 76 */     str = " ALTER TABLE $tableName$ DROP COLUMN $fieldName$ \n";
/* 77 */     moduleDDLDef.setDbtype("sqlserver");
/* 78 */     moduleDDLDef.setDdltype("DeleteField");
/* 79 */     moduleDDLDef.setDdltemplate(str);
/* 80 */     commonEntityService.addEntity(moduleDDLDef);
/*    */     
/* 82 */     moduleDDLDef.setDbtype("oracle");
/* 83 */     moduleDDLDef.setDdltemplate(str);
/* 84 */     commonEntityService.addEntity(moduleDDLDef);
/*    */     
/* 86 */     moduleDDLDef.setDbtype("mysql");
/* 87 */     moduleDDLDef.setDdltemplate(str);
/* 88 */     commonEntityService.addEntity(moduleDDLDef);
/* 89 */     moduleDDLDef.setDbtype("postgresql");
/* 90 */     moduleDDLDef.setDdltemplate(str);
/* 91 */     commonEntityService.addEntity(moduleDDLDef);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/customfield/service/ModuleDDLDefService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */