/*    */ package weaver.integration.customfield.service;
/*    */ 
/*    */ import java.util.List;
/*    */ import weaver.crazydream.util.Condition;
/*    */ import weaver.integration.customfield.entity.ModuleFuncTable;
/*    */ import weaver.integration.customfield.util.CommonEntityService;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ModuleFuncTableService
/*    */   implements AbstractService
/*    */ {
/*    */   public String add(Object paramObject) {
/* 16 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleFuncTable.class);
/* 17 */     commonEntityService.addEntity(paramObject);
/* 18 */     return "";
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean update(Object paramObject) {
/* 23 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleFuncTable.class);
/* 24 */     return commonEntityService.updateEntity(paramObject);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean delete(String paramString) {
/* 30 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleFuncTable.class);
/* 31 */     return commonEntityService.deleteEntity(paramString);
/*    */   }
/*    */ 
/*    */   
/*    */   public ModuleFuncTable get(String paramString) {
/* 36 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleFuncTable.class);
/* 37 */     return (ModuleFuncTable)commonEntityService.getEntity(paramString);
/*    */   }
/*    */ 
/*    */   
/*    */   public List<?> getList(Condition paramCondition) {
/* 42 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleFuncTable.class);
/* 43 */     return commonEntityService.getEntitiesByConditionToList(paramCondition);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/customfield/service/ModuleFuncTableService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */