package weaver.integration.customfield.service;

import java.util.List;
import weaver.crazydream.util.Condition;

public interface AbstractService {
  String add(Object paramObject);
  
  boolean update(Object paramObject);
  
  boolean delete(String paramString);
  
  Object get(String paramString);
  
  List<?> getList(Condition paramCondition);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/customfield/service/AbstractService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */