/*    */ package weaver.integration.customfield.service;
/*    */ 
/*    */ import java.util.List;
/*    */ import weaver.crazydream.util.Condition;
/*    */ import weaver.integration.customfield.entity.ModuleFunc;
/*    */ import weaver.integration.customfield.util.CommonEntityService;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ModuleFuncService
/*    */   implements AbstractService
/*    */ {
/*    */   public String add(Object paramObject) {
/* 16 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleFunc.class);
/* 17 */     commonEntityService.addEntity(paramObject);
/* 18 */     return "";
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean update(Object paramObject) {
/* 23 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleFunc.class);
/* 24 */     return commonEntityService.updateEntity(paramObject);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean delete(String paramString) {
/* 30 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleFunc.class);
/* 31 */     return commonEntityService.deleteEntity(paramString);
/*    */   }
/*    */ 
/*    */   
/*    */   public ModuleFunc get(String paramString) {
/* 36 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleFunc.class);
/* 37 */     return (ModuleFunc)commonEntityService.getEntity(paramString);
/*    */   }
/*    */ 
/*    */   
/*    */   public List<?> getList(Condition paramCondition) {
/* 42 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleFunc.class);
/* 43 */     return commonEntityService.getEntitiesByConditionToList(paramCondition);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/customfield/service/ModuleFuncService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */