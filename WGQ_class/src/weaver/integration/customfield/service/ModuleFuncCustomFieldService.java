/*     */ package weaver.integration.customfield.service;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crazydream.util.Condition;
/*     */ import weaver.integration.customfield.ddl.DDLAction;
/*     */ import weaver.integration.customfield.ddl.DDLActionImpl;
/*     */ import weaver.integration.customfield.entity.Module;
/*     */ import weaver.integration.customfield.entity.ModuleDDLDef;
/*     */ import weaver.integration.customfield.entity.ModuleFunc;
/*     */ import weaver.integration.customfield.entity.ModuleFuncCustomField;
/*     */ import weaver.integration.customfield.entity.ModuleFuncTable;
/*     */ import weaver.integration.customfield.util.CommonEntityService;
/*     */ 
/*     */ public class ModuleFuncCustomFieldService
/*     */   implements AbstractService
/*     */ {
/*  21 */   private DDLAction ddlAction = (DDLAction)new DDLActionImpl();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean insertCustomField(ModuleFuncCustomField paramModuleFuncCustomField) {
/*  30 */     boolean bool = false;
/*  31 */     String str = paramModuleFuncCustomField.getTableId();
/*  32 */     ModuleFuncTable moduleFuncTable = (ModuleFuncTable)(new CommonEntityService(ModuleFuncTable.class)).getEntity(str);
/*  33 */     if (moduleFuncTable != null) {
/*  34 */       String str1 = moduleFuncTable.getTableName();
/*  35 */       bool = executeCustomFieldDDL("AddField", str1, paramModuleFuncCustomField);
/*     */     } 
/*     */     
/*  38 */     if (bool) {
/*  39 */       CommonEntityService commonEntityService = new CommonEntityService(ModuleFuncCustomField.class);
/*  40 */       commonEntityService.addEntity(paramModuleFuncCustomField);
/*  41 */       return true;
/*     */     } 
/*     */     
/*  44 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean insertCustomField(String paramString1, String paramString2, String paramString3, ModuleFuncCustomField paramModuleFuncCustomField) {
/*  58 */     boolean bool = executeCustomFieldDDL("AddField", paramString3, paramModuleFuncCustomField);
/*     */     
/*  60 */     if (bool) {
/*  61 */       CommonEntityService commonEntityService1 = new CommonEntityService(Module.class);
/*  62 */       List<Module> list = commonEntityService1.getEntitiesByConditionToList((new Condition()).equals("moduleCode", paramString1));
/*  63 */       if (list == null || list.size() == 0) {
/*  64 */         return false;
/*     */       }
/*  66 */       String str = ((Module)list.get(0)).getId();
/*  67 */       paramModuleFuncCustomField.setModuleId(str);
/*     */ 
/*     */       
/*  70 */       commonEntityService1 = new CommonEntityService(ModuleFunc.class);
/*  71 */       list = commonEntityService1.getEntitiesByConditionToList((new Condition()).equals("functionCode", paramString2));
/*  72 */       if (list == null || list.size() == 0) {
/*  73 */         return false;
/*     */       }
/*  75 */       str = ((ModuleFunc)list.get(0)).getId();
/*  76 */       paramModuleFuncCustomField.setFuncId(str);
/*     */ 
/*     */       
/*  79 */       commonEntityService1 = new CommonEntityService(ModuleFuncTable.class);
/*  80 */       list = commonEntityService1.getEntitiesByConditionToList((new Condition()).equals("tableName", paramString3));
/*  81 */       if (list == null || list.size() == 0) {
/*  82 */         return false;
/*     */       }
/*  84 */       str = ((ModuleFunc)list.get(0)).getId();
/*  85 */       paramModuleFuncCustomField.setTableId(str);
/*     */ 
/*     */       
/*  88 */       CommonEntityService commonEntityService2 = new CommonEntityService(ModuleFuncCustomField.class);
/*  89 */       commonEntityService2.addEntity(paramModuleFuncCustomField);
/*  90 */       return true;
/*     */     } 
/*     */     
/*  93 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean updateCustomField(ModuleFuncCustomField paramModuleFuncCustomField) {
/* 112 */     boolean bool = true;
/*     */     
/* 114 */     if (bool) {
/* 115 */       CommonEntityService commonEntityService = new CommonEntityService(ModuleFuncCustomField.class);
/* 116 */       commonEntityService.updateEntity(paramModuleFuncCustomField);
/* 117 */       return true;
/*     */     } 
/*     */     
/* 120 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean updateCustomField(String paramString1, String paramString2, String paramString3, ModuleFuncCustomField paramModuleFuncCustomField) {
/* 135 */     boolean bool = true;
/*     */     
/* 137 */     if (bool) {
/* 138 */       CommonEntityService commonEntityService1 = new CommonEntityService(Module.class);
/* 139 */       List<Module> list = commonEntityService1.getEntitiesByConditionToList((new Condition()).equals("moduleCode", paramString1));
/* 140 */       if (list == null || list.size() == 0) {
/* 141 */         return false;
/*     */       }
/* 143 */       String str = ((Module)list.get(0)).getId();
/* 144 */       paramModuleFuncCustomField.setModuleId(str);
/*     */ 
/*     */       
/* 147 */       commonEntityService1 = new CommonEntityService(ModuleFunc.class);
/* 148 */       list = commonEntityService1.getEntitiesByConditionToList((new Condition()).equals("functionCode", paramString2));
/* 149 */       if (list == null || list.size() == 0) {
/* 150 */         return false;
/*     */       }
/* 152 */       str = ((ModuleFunc)list.get(0)).getId();
/* 153 */       paramModuleFuncCustomField.setFuncId(str);
/*     */ 
/*     */       
/* 156 */       commonEntityService1 = new CommonEntityService(ModuleFuncTable.class);
/* 157 */       list = commonEntityService1.getEntitiesByConditionToList((new Condition()).equals("tableName", paramString3));
/* 158 */       if (list == null || list.size() == 0) {
/* 159 */         return false;
/*     */       }
/* 161 */       str = ((ModuleFunc)list.get(0)).getId();
/* 162 */       paramModuleFuncCustomField.setTableId(str);
/*     */ 
/*     */       
/* 165 */       CommonEntityService commonEntityService2 = new CommonEntityService(ModuleFuncCustomField.class);
/* 166 */       commonEntityService2.updateEntity(paramModuleFuncCustomField);
/* 167 */       return true;
/*     */     } 
/*     */     
/* 170 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean deleteCustomField(String paramString) {
/* 181 */     boolean bool = false;
/* 182 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleFuncCustomField.class);
/* 183 */     ModuleFuncCustomField moduleFuncCustomField = (ModuleFuncCustomField)commonEntityService.getEntity(paramString);
/* 184 */     if (moduleFuncCustomField != null) {
/* 185 */       ModuleFuncTable moduleFuncTable = (ModuleFuncTable)(new CommonEntityService(ModuleFuncTable.class)).getEntity(moduleFuncCustomField.getTableId());
/* 186 */       if (moduleFuncTable != null) {
/* 187 */         bool = executeCustomFieldDDL("DeleteField", moduleFuncTable.getTableName(), moduleFuncCustomField);
/*     */       }
/*     */     } 
/*     */     
/* 191 */     if (bool) {
/* 192 */       return commonEntityService.deleteEntity(paramString);
/*     */     }
/*     */     
/* 195 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean deleteCustomField(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 209 */     boolean bool = false;
/* 210 */     List<ModuleFuncCustomField> list = (new CommonEntityService(ModuleFuncCustomField.class)).getEntitiesByConditionToList((new Condition()).equals("fieldName", paramString4));
/* 211 */     if (list != null && list.size() > 0) {
/* 212 */       ModuleFuncCustomField moduleFuncCustomField = list.get(0);
/* 213 */       bool = executeCustomFieldDDL("DeleteField", paramString3, moduleFuncCustomField);
/*     */     } 
/*     */     
/* 216 */     if (bool) {
/* 217 */       String str = "DELETE FROM Int_Module_Func_CustomField \nWHERE moduleid IN(SELECT id FROM Int_Module WHERE moduleCode=?)\nAND funcId IN (SELECT id FROM Int_Module_Func WHERE functionCode=?)\nAND tableId IN (SELECT id FROM Int_Module_Func_table WHERE tableName=?)\nAND fieldName=?";
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 222 */       RecordSet recordSet = new RecordSet();
/* 223 */       return recordSet.executeUpdate(str, new Object[] { paramString1, paramString2, paramString3, paramString4 });
/*     */     } 
/*     */     
/* 226 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ModuleFuncCustomField getCustomField(String paramString) {
/* 235 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleFuncCustomField.class);
/* 236 */     return (ModuleFuncCustomField)commonEntityService.getEntity(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ModuleFuncCustomField getCustomField(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 248 */     String str = "SELECT * FROM Int_Module_Func_CustomField \nWHERE moduleid IN(SELECT id FROM Int_Module WHERE moduleCode=?)\nAND funcId IN (SELECT id FROM Int_Module_Func WHERE functionCode=?)\nAND tableId IN (SELECT id FROM Int_Module_Func_table WHERE tableName=?)\nAND fieldName=?";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 253 */     RecordSet recordSet = new RecordSet();
/* 254 */     recordSet.executeQuery(str, new Object[] { paramString1, paramString2, paramString3, paramString4 });
/* 255 */     if (recordSet.next()) {
/* 256 */       ModuleFuncCustomField moduleFuncCustomField = new ModuleFuncCustomField();
/* 257 */       moduleFuncCustomField.setId(recordSet.getString("id"));
/* 258 */       moduleFuncCustomField.setTableId(recordSet.getString("tableId"));
/* 259 */       moduleFuncCustomField.setFuncId(recordSet.getString("funcId"));
/* 260 */       moduleFuncCustomField.setModuleId(recordSet.getString("moduleId"));
/* 261 */       moduleFuncCustomField.setFieldInfo(recordSet.getString("fieldInfo"));
/* 262 */       moduleFuncCustomField.setFieldDbType(recordSet.getString("fieldDbType"));
/* 263 */       moduleFuncCustomField.setFieldName(recordSet.getString("fieldName"));
/* 264 */       moduleFuncCustomField.setFieldShowName(recordSet.getString("fieldShowName"));
/* 265 */       moduleFuncCustomField.setCreateDate(recordSet.getString("createdate"));
/* 266 */       moduleFuncCustomField.setCreateTime(recordSet.getString("createtime"));
/* 267 */       moduleFuncCustomField.setCreator(recordSet.getString("creator"));
/* 268 */       moduleFuncCustomField.setModifier(recordSet.getString("modifier"));
/* 269 */       moduleFuncCustomField.setModifyDate(recordSet.getString("modifydate"));
/* 270 */       moduleFuncCustomField.setModifyTime(recordSet.getString("modifytime"));
/*     */       
/* 272 */       return moduleFuncCustomField;
/*     */     } 
/* 274 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<ModuleFuncCustomField> getCustomFieldList(String paramString) {
/* 283 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleFuncCustomField.class);
/* 284 */     return commonEntityService.getEntitiesByConditionToList((new Condition()).equals("tableId", paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<ModuleFuncCustomField> getCustomFieldList(String paramString1, String paramString2, String paramString3) {
/* 295 */     ArrayList<ModuleFuncCustomField> arrayList = new ArrayList();
/* 296 */     String str = "SELECT * FROM Int_Module_Func_CustomField \nWHERE moduleid IN(SELECT id FROM Int_Module WHERE moduleCode=?)\nAND funcId IN (SELECT id FROM Int_Module_Func WHERE functionCode=?)\nAND tableId IN (SELECT id FROM Int_Module_Func_table WHERE tableName=?)\n";
/*     */ 
/*     */ 
/*     */     
/* 300 */     RecordSet recordSet = new RecordSet();
/* 301 */     recordSet.executeQuery(str, new Object[] { paramString1, paramString2, paramString3 });
/* 302 */     while (recordSet.next()) {
/* 303 */       ModuleFuncCustomField moduleFuncCustomField = new ModuleFuncCustomField();
/* 304 */       moduleFuncCustomField.setId(recordSet.getString("id"));
/* 305 */       moduleFuncCustomField.setTableId(recordSet.getString("tableId"));
/* 306 */       moduleFuncCustomField.setFuncId(recordSet.getString("funcId"));
/* 307 */       moduleFuncCustomField.setModuleId(recordSet.getString("moduleId"));
/* 308 */       moduleFuncCustomField.setFieldInfo(recordSet.getString("fieldInfo"));
/* 309 */       moduleFuncCustomField.setFieldName(recordSet.getString("fieldName"));
/* 310 */       moduleFuncCustomField.setFieldShowName(recordSet.getString("fieldShowName"));
/* 311 */       moduleFuncCustomField.setCreateDate(recordSet.getString("createdate"));
/* 312 */       moduleFuncCustomField.setCreateTime(recordSet.getString("createtime"));
/* 313 */       moduleFuncCustomField.setCreator(recordSet.getString("creator"));
/* 314 */       moduleFuncCustomField.setModifier(recordSet.getString("modifier"));
/* 315 */       moduleFuncCustomField.setModifyDate(recordSet.getString("modifydate"));
/* 316 */       moduleFuncCustomField.setModifyTime(recordSet.getString("modifytime"));
/*     */       
/* 318 */       arrayList.add(moduleFuncCustomField);
/*     */     } 
/*     */     
/* 321 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean existCustomField(ModuleFuncCustomField paramModuleFuncCustomField) {
/* 330 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleFuncCustomField.class);
/* 331 */     List list = commonEntityService.getEntitiesByConditionToList((new Condition())
/* 332 */         .equals("moduleId", paramModuleFuncCustomField.getModuleId())
/* 333 */         .equals("funcId", paramModuleFuncCustomField.getFuncId())
/* 334 */         .equals("tableId", paramModuleFuncCustomField.getTableId())
/* 335 */         .equals("fieldName", paramModuleFuncCustomField.getFieldName()));
/* 336 */     if (list != null && list.size() > 0) {
/* 337 */       return true;
/*     */     }
/* 339 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean existCustomField(String paramString) {
/* 348 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleFuncCustomField.class);
/* 349 */     List list = commonEntityService.getEntitiesByConditionToList((new Condition())
/* 350 */         .equals("fieldName", paramString));
/* 351 */     if (list != null && list.size() > 0) {
/* 352 */       return true;
/*     */     }
/* 354 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean executeCustomFieldDDL(String paramString1, String paramString2, ModuleFuncCustomField paramModuleFuncCustomField) {
/* 365 */     boolean bool = false;
/* 366 */     String str = paramModuleFuncCustomField.getTableId();
/* 367 */     ModuleFuncTable moduleFuncTable = (ModuleFuncTable)(new CommonEntityService(ModuleFuncTable.class)).getEntity(str);
/* 368 */     if (moduleFuncTable != null) {
/* 369 */       String str1 = paramModuleFuncCustomField.getFieldName();
/* 370 */       String str2 = paramModuleFuncCustomField.getFieldDbType();
/* 371 */       List<ModuleDDLDef> list = (new CommonEntityService(ModuleDDLDef.class)).getEntitiesByConditionToList((new Condition())
/* 372 */           .equals("dbType", (new RecordSet()).getDBType())
/* 373 */           .equals("ddlType", paramString1));
/*     */       
/* 375 */       if (list != null && list.size() > 0) {
/* 376 */         ModuleDDLDef moduleDDLDef = list.get(0);
/* 377 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 378 */         hashMap.put("tableName", paramString2);
/* 379 */         hashMap.put("fieldName", str1);
/* 380 */         hashMap.put("fieldDbType", str2);
/* 381 */         Map map = this.ddlAction.execute(moduleDDLDef, hashMap);
/* 382 */         if (((Boolean)map.get("result")).booleanValue() == true) {
/* 383 */           bool = true;
/*     */         }
/*     */       } 
/*     */     } 
/* 387 */     return bool;
/*     */   }
/*     */ 
/*     */   
/*     */   public String add(Object paramObject) {
/* 392 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleFuncCustomField.class);
/* 393 */     commonEntityService.addEntity(paramObject);
/* 394 */     return "";
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean update(Object paramObject) {
/* 399 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleFuncCustomField.class);
/* 400 */     return commonEntityService.updateEntity(paramObject);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean delete(String paramString) {
/* 406 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleFuncCustomField.class);
/* 407 */     return commonEntityService.deleteEntity(paramString);
/*     */   }
/*     */ 
/*     */   
/*     */   public ModuleFuncCustomField get(String paramString) {
/* 412 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleFuncCustomField.class);
/* 413 */     return (ModuleFuncCustomField)commonEntityService.getEntity(paramString);
/*     */   }
/*     */ 
/*     */   
/*     */   public List<?> getList(Condition paramCondition) {
/* 418 */     CommonEntityService commonEntityService = new CommonEntityService(ModuleFuncCustomField.class);
/* 419 */     return commonEntityService.getEntitiesByConditionToList(paramCondition);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/customfield/service/ModuleFuncCustomFieldService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */