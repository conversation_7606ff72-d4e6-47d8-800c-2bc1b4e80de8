/*     */ package weaver.integration.customfield.entity;
/*     */ 
/*     */ import javax.persistence.Column;
/*     */ import javax.persistence.Table;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Table(name = "Int_Module_Func_table")
/*     */ public class ModuleFuncTable
/*     */   extends AbstractEntity
/*     */ {
/*     */   private String moduleId;
/*     */   private String funcId;
/*     */   private String tableName;
/*     */   private String tableLabel;
/*     */   private String pkFieldName;
/*     */   private String pkFieldType;
/*     */   private String parentTableId;
/*     */   private String parentFieldName;
/*     */   private String parentFieldType;
/*     */   
/*     */   public ModuleFuncTable() {}
/*     */   
/*     */   public ModuleFuncTable(User paramUser) {
/*  28 */     super(paramUser);
/*     */   }
/*     */   
/*     */   @Column(name = "moduleId")
/*     */   public String getModuleId() {
/*  33 */     return this.moduleId;
/*     */   }
/*     */   
/*     */   public void setModuleId(String paramString) {
/*  37 */     this.moduleId = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "funcId")
/*     */   public String getFuncId() {
/*  42 */     return this.funcId;
/*     */   }
/*     */   
/*     */   public void setFuncId(String paramString) {
/*  46 */     this.funcId = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "tableName")
/*     */   public String getTableName() {
/*  51 */     return this.tableName;
/*     */   }
/*     */   
/*     */   public void setTableName(String paramString) {
/*  55 */     this.tableName = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "tableLabel")
/*     */   public String getTableLabel() {
/*  60 */     return this.tableLabel;
/*     */   }
/*     */   
/*     */   public void setTableLabel(String paramString) {
/*  64 */     this.tableLabel = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "pkFieldName")
/*     */   public String getPkFieldName() {
/*  69 */     return this.pkFieldName;
/*     */   }
/*     */   
/*     */   public void setPkFieldName(String paramString) {
/*  73 */     this.pkFieldName = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "pkFieldType")
/*     */   public String getPkFieldType() {
/*  78 */     return this.pkFieldType;
/*     */   }
/*     */   
/*     */   public void setPkFieldType(String paramString) {
/*  82 */     this.pkFieldType = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "parentTableId")
/*     */   public String getParentTableId() {
/*  87 */     return this.parentTableId;
/*     */   }
/*     */   
/*     */   public void setParentTableId(String paramString) {
/*  91 */     this.parentTableId = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "parentFieldName")
/*     */   public String getParentFieldName() {
/*  96 */     return this.parentFieldName;
/*     */   }
/*     */   
/*     */   public void setParentFieldName(String paramString) {
/* 100 */     this.parentFieldName = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "parentFieldType")
/*     */   public String getParentFieldType() {
/* 105 */     return this.parentFieldType;
/*     */   }
/*     */   
/*     */   public void setParentFieldType(String paramString) {
/* 109 */     this.parentFieldType = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/customfield/entity/ModuleFuncTable.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */