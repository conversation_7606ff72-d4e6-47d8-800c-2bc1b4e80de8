/*     */ package weaver.integration.customfield.entity;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.Date;
/*     */ import javax.persistence.Column;
/*     */ import javax.persistence.Id;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class AbstractEntity
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1822271057009738491L;
/*     */   private String id;
/*     */   private String creator;
/*     */   private String createDate;
/*     */   private String createTime;
/*     */   private String modifier;
/*     */   private String modifyDate;
/*     */   private String modifyTime;
/*     */   
/*     */   @Id
/*     */   @Column(name = "id")
/*     */   public String getId() {
/*  29 */     return this.id;
/*     */   }
/*     */   
/*     */   public void setId(String paramString) {
/*  33 */     this.id = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "creater")
/*     */   public String getCreator() {
/*  38 */     return this.creator;
/*     */   }
/*     */   
/*     */   public void setCreator(String paramString) {
/*  42 */     this.creator = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "createdate")
/*     */   public String getCreateDate() {
/*  47 */     return this.createDate;
/*     */   }
/*     */   
/*     */   public void setCreateDate(String paramString) {
/*  51 */     this.createDate = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "createtime")
/*     */   public String getCreateTime() {
/*  56 */     return this.createTime;
/*     */   }
/*     */   
/*     */   public void setCreateTime(String paramString) {
/*  60 */     this.createTime = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "modifier")
/*     */   public String getModifier() {
/*  65 */     return this.modifier;
/*     */   }
/*     */   
/*     */   public void setModifier(String paramString) {
/*  69 */     this.modifier = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "modifydate")
/*     */   public String getModifyDate() {
/*  74 */     return this.modifyDate;
/*     */   }
/*     */   
/*     */   public void setModifyDate(String paramString) {
/*  78 */     this.modifyDate = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "modifytime")
/*     */   public String getModifyTime() {
/*  83 */     return this.modifyTime;
/*     */   }
/*     */   
/*     */   public void setModifyTime(String paramString) {
/*  87 */     this.modifyTime = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public int hashCode() {
/*  92 */     return (this.id == null) ? super.hashCode() : this.id.hashCode();
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean equals(Object paramObject) {
/*  97 */     if (paramObject == this) {
/*  98 */       return true;
/*     */     }
/* 100 */     if (paramObject == null) {
/* 101 */       return false;
/*     */     }
/* 103 */     if (!(paramObject instanceof AbstractEntity)) {
/* 104 */       return false;
/*     */     }
/* 106 */     if (this.id == null || ((AbstractEntity)paramObject).id == null) {
/* 107 */       return false;
/*     */     }
/*     */     
/* 110 */     return this.id.equals(((AbstractEntity)paramObject).id);
/*     */   }
/*     */   
/*     */   protected AbstractEntity() {
/* 114 */     SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("yyyy-MM-dd");
/* 115 */     SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("HH:mm:ss");
/* 116 */     Date date = new Date();
/* 117 */     this.createDate = simpleDateFormat1.format(date);
/* 118 */     this.createTime = simpleDateFormat2.format(date);
/*     */   }
/*     */   
/*     */   protected AbstractEntity(User paramUser) {
/* 122 */     this();
/* 123 */     if (paramUser != null)
/* 124 */       this.creator = "" + paramUser.getUID(); 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/customfield/entity/AbstractEntity.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */