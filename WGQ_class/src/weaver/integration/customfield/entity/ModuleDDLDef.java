/*    */ package weaver.integration.customfield.entity;
/*    */ 
/*    */ import javax.persistence.Column;
/*    */ import javax.persistence.Table;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Table(name = "Int_Module_DDL_Def")
/*    */ public class ModuleDDLDef
/*    */   extends AbstractEntity
/*    */ {
/*    */   private String dbtype;
/*    */   private String ddltype;
/*    */   private String ddltemplate;
/*    */   private String ddlclazz;
/*    */   
/*    */   public ModuleDDLDef() {}
/*    */   
/*    */   public ModuleDDLDef(User paramUser) {
/* 23 */     super(paramUser);
/*    */   }
/*    */   @Column(name = "dbtype")
/*    */   public String getDbtype() {
/* 27 */     return this.dbtype;
/*    */   }
/*    */   
/*    */   public void setDbtype(String paramString) {
/* 31 */     this.dbtype = paramString;
/*    */   }
/*    */   
/*    */   @Column(name = "ddltype")
/*    */   public String getDdltype() {
/* 36 */     return this.ddltype;
/*    */   }
/*    */   
/*    */   public void setDdltype(String paramString) {
/* 40 */     this.ddltype = paramString;
/*    */   }
/*    */   
/*    */   @Column(name = "ddltemplate")
/*    */   public String getDdltemplate() {
/* 45 */     return this.ddltemplate;
/*    */   }
/*    */   
/*    */   public void setDdltemplate(String paramString) {
/* 49 */     this.ddltemplate = paramString;
/*    */   }
/*    */   
/*    */   @Column(name = "ddlclazz")
/*    */   public String getDdlclazz() {
/* 54 */     return this.ddlclazz;
/*    */   }
/*    */   
/*    */   public void setDdlclazz(String paramString) {
/* 58 */     this.ddlclazz = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/customfield/entity/ModuleDDLDef.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */