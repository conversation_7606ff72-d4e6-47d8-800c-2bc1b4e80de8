/*    */ package weaver.integration.customfield.entity;
/*    */ 
/*    */ import javax.persistence.Column;
/*    */ import javax.persistence.Table;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Table(name = "Int_Module_Func_CustomField")
/*    */ public class ModuleFuncCustomField
/*    */   extends AbstractEntity
/*    */ {
/*    */   private String moduleId;
/*    */   private String funcId;
/*    */   private String tableId;
/*    */   private String fieldName;
/*    */   private String fieldShowName;
/*    */   private String fieldInfo;
/*    */   private String fieldDbType;
/*    */   
/*    */   public ModuleFuncCustomField() {}
/*    */   
/*    */   public ModuleFuncCustomField(User paramUser) {
/* 26 */     super(paramUser);
/*    */   }
/*    */   
/*    */   @Column(name = "moduleId")
/*    */   public String getModuleId() {
/* 31 */     return this.moduleId;
/*    */   }
/*    */   
/*    */   public void setModuleId(String paramString) {
/* 35 */     this.moduleId = paramString;
/*    */   }
/*    */   
/*    */   @Column(name = "funcId")
/*    */   public String getFuncId() {
/* 40 */     return this.funcId;
/*    */   }
/*    */   
/*    */   public void setFuncId(String paramString) {
/* 44 */     this.funcId = paramString;
/*    */   }
/*    */   
/*    */   @Column(name = "tableId")
/*    */   public String getTableId() {
/* 49 */     return this.tableId;
/*    */   }
/*    */   
/*    */   public void setTableId(String paramString) {
/* 53 */     this.tableId = paramString;
/*    */   }
/*    */   
/*    */   @Column(name = "fieldName")
/*    */   public String getFieldName() {
/* 58 */     return this.fieldName;
/*    */   }
/*    */   
/*    */   public void setFieldName(String paramString) {
/* 62 */     this.fieldName = paramString;
/*    */   }
/*    */   
/*    */   @Column(name = "fieldShowName")
/*    */   public String getFieldShowName() {
/* 67 */     return this.fieldShowName;
/*    */   }
/*    */   
/*    */   public void setFieldShowName(String paramString) {
/* 71 */     this.fieldShowName = paramString;
/*    */   }
/*    */   
/*    */   @Column(name = "fieldInfo")
/*    */   public String getFieldInfo() {
/* 76 */     return this.fieldInfo;
/*    */   }
/*    */   
/*    */   public void setFieldInfo(String paramString) {
/* 80 */     this.fieldInfo = paramString;
/*    */   }
/*    */   
/*    */   @Column(name = "fieldDbType")
/*    */   public String getFieldDbType() {
/* 85 */     return this.fieldDbType;
/*    */   }
/*    */   
/*    */   public void setFieldDbType(String paramString) {
/* 89 */     this.fieldDbType = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/customfield/entity/ModuleFuncCustomField.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */