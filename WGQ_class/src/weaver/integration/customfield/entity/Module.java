/*    */ package weaver.integration.customfield.entity;
/*    */ 
/*    */ import javax.persistence.Column;
/*    */ import javax.persistence.Table;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Table(name = "Int_Module")
/*    */ public class Module
/*    */   extends AbstractEntity
/*    */ {
/*    */   private String moduleCode;
/*    */   private String moduleName;
/*    */   
/*    */   public Module() {}
/*    */   
/*    */   public Module(User paramUser) {
/* 21 */     super(paramUser);
/*    */   }
/*    */   @Column(name = "moduleCode")
/*    */   public String getModuleCode() {
/* 25 */     return this.moduleCode;
/*    */   }
/*    */   
/*    */   public void setModuleCode(String paramString) {
/* 29 */     this.moduleCode = paramString;
/*    */   }
/*    */   
/*    */   @Column(name = "moduleName")
/*    */   public String getModuleName() {
/* 34 */     return this.moduleName;
/*    */   }
/*    */   
/*    */   public void setModuleName(String paramString) {
/* 38 */     this.moduleName = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/customfield/entity/Module.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */