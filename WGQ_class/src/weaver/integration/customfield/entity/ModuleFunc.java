/*    */ package weaver.integration.customfield.entity;
/*    */ 
/*    */ import javax.persistence.Column;
/*    */ import javax.persistence.Table;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Table(name = "Int_Module_Func")
/*    */ public class ModuleFunc
/*    */   extends AbstractEntity
/*    */ {
/*    */   private String moduleId;
/*    */   private String functionCode;
/*    */   private String functionName;
/*    */   
/*    */   public ModuleFunc() {}
/*    */   
/*    */   public ModuleFunc(User paramUser) {
/* 22 */     super(paramUser);
/*    */   }
/*    */   
/*    */   @Column(name = "moduleId")
/*    */   public String getModuleId() {
/* 27 */     return this.moduleId;
/*    */   }
/*    */   
/*    */   public void setModuleId(String paramString) {
/* 31 */     this.moduleId = paramString;
/*    */   }
/*    */   
/*    */   @Column(name = "functionCode")
/*    */   public String getFunctionCode() {
/* 36 */     return this.functionCode;
/*    */   }
/*    */   
/*    */   public void setFunctionCode(String paramString) {
/* 40 */     this.functionCode = paramString;
/*    */   }
/*    */   
/*    */   @Column(name = "functionName")
/*    */   public String getFunctionName() {
/* 45 */     return this.functionName;
/*    */   }
/*    */   
/*    */   public void setFunctionName(String paramString) {
/* 49 */     this.functionName = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/customfield/entity/ModuleFunc.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */