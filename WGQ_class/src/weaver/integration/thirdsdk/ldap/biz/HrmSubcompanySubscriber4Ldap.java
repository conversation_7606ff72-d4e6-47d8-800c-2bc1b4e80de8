/*    */ package weaver.integration.thirdsdk.ldap.biz;
/*    */ 
/*    */ import com.api.integration.util.RecordSetObj;
/*    */ import com.weaver.general.Util;
/*    */ import java.util.List;
/*    */ import weaver.integration.framework.data.field.FieldData;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ import weaver.integration.thirdsdk.ldap.api.LdapRealtimeSync;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmSubcompanySubscriber4Ldap
/*    */ {
/* 18 */   private Logger newLog = LoggerFactory.getLogger(HrmSubcompanySubscriber4Ldap.class);
/*    */   
/*    */   public Object synData(List<FieldData> paramList, String paramString) {
/* 21 */     this.newLog.info("接收的信息为：" + paramList);
/* 22 */     this.newLog.info("接收的动作为：" + paramString);
/* 23 */     String str1 = "";
/* 24 */     for (FieldData fieldData : paramList) {
/* 25 */       if ("id".equals(fieldData.getFieldName())) {
/* 26 */         str1 = Util.null2String((String)fieldData.getFieldValue());
/*    */       }
/*    */     } 
/*    */     
/* 30 */     String str2 = "SELECT LDAPID FROM LDAP_SETTING";
/* 31 */     RecordSetObj recordSetObj = new RecordSetObj();
/* 32 */     recordSetObj.executeQuery(str2, new Object[0]);
/* 33 */     while (recordSetObj.next()) {
/* 34 */       String str = recordSetObj.getString("LDAPID");
/* 35 */       LdapRealtimeSync ldapRealtimeSync = new LdapRealtimeSync(str);
/* 36 */       if (ldapRealtimeSync.init()) {
/* 37 */         this.newLog.info("开始进行分部实时同步：" + str1);
/* 38 */         if ("delete".equals(paramString)) {
/* 39 */           ldapRealtimeSync.deleteNode(str1, "1"); continue;
/*    */         } 
/* 41 */         ldapRealtimeSync.saveSubcompany(str1);
/*    */       } 
/*    */     } 
/*    */ 
/*    */     
/* 46 */     return Boolean.valueOf(true);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/ldap/biz/HrmSubcompanySubscriber4Ldap.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */