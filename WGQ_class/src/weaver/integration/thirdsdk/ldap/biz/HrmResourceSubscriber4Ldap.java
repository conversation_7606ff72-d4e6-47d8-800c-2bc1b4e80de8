/*    */ package weaver.integration.thirdsdk.ldap.biz;
/*    */ 
/*    */ import com.api.integration.util.RecordSetObj;
/*    */ import com.weaver.general.Util;
/*    */ import java.util.List;
/*    */ import weaver.integration.framework.data.field.FieldData;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ import weaver.integration.thirdsdk.ldap.api.LdapRealtimeSync;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmResourceSubscriber4Ldap
/*    */ {
/* 16 */   private Logger newLog = LoggerFactory.getLogger(HrmResourceSubscriber4Ldap.class);
/*    */   
/*    */   public Object synData(List<FieldData> paramList, String paramString) {
/* 19 */     this.newLog.info("接收的信息为：" + paramList);
/* 20 */     this.newLog.info("接收的动作为：" + paramString);
/* 21 */     String str1 = "";
/* 22 */     String str2 = "";
/* 23 */     for (FieldData fieldData : paramList) {
/* 24 */       if ("id".equals(fieldData.getFieldName())) {
/* 25 */         str1 = Util.null2String((String)fieldData.getFieldValue());
/*    */       }
/* 27 */       if ("password".equals(fieldData.getFieldName())) {
/* 28 */         str2 = Util.null2String((String)fieldData.getFieldValue());
/*    */       }
/*    */     } 
/*    */     
/* 32 */     String str3 = "SELECT LDAPID FROM LDAP_SETTING";
/* 33 */     RecordSetObj recordSetObj = new RecordSetObj();
/* 34 */     recordSetObj.executeQuery(str3, new Object[0]);
/* 35 */     while (recordSetObj.next()) {
/* 36 */       String str = recordSetObj.getString("LDAPID");
/* 37 */       LdapRealtimeSync ldapRealtimeSync = new LdapRealtimeSync(str);
/* 38 */       if (ldapRealtimeSync.init()) {
/* 39 */         this.newLog.info("开始进行人员实时同步：" + str1);
/* 40 */         ldapRealtimeSync.saveUser(str1, str2);
/*    */       } 
/*    */     } 
/*    */     
/* 44 */     return Boolean.valueOf(true);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/ldap/biz/HrmResourceSubscriber4Ldap.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */