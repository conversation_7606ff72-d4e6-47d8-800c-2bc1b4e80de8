/*    */ package weaver.integration.thirdsdk.ldap.biz;
/*    */ 
/*    */ import com.api.integration.util.RecordSetObj;
/*    */ import com.wbi.util.Util;
/*    */ import java.util.List;
/*    */ import weaver.integration.framework.data.field.FieldData;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ import weaver.integration.thirdsdk.ldap.api.LdapRealtimeSync;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmDepartmentSubscriber4Ldap
/*    */ {
/* 16 */   private Logger newLog = LoggerFactory.getLogger(HrmDepartmentSubscriber4Ldap.class);
/*    */   
/*    */   public Object synData(List<FieldData> paramList, String paramString) {
/* 19 */     this.newLog.info("接收的信息为" + paramList);
/* 20 */     this.newLog.info("接收的动作为：" + paramString);
/* 21 */     String str1 = "";
/* 22 */     for (FieldData fieldData : paramList) {
/* 23 */       if ("id".equals(fieldData.getFieldName())) {
/* 24 */         str1 = Util.null2String((String)fieldData.getFieldValue());
/*    */       }
/*    */     } 
/*    */     
/* 28 */     String str2 = "SELECT LDAPID FROM LDAP_SETTING";
/* 29 */     RecordSetObj recordSetObj = new RecordSetObj();
/* 30 */     recordSetObj.executeQuery(str2, new Object[0]);
/* 31 */     while (recordSetObj.next()) {
/* 32 */       String str = recordSetObj.getString("LDAPID");
/* 33 */       LdapRealtimeSync ldapRealtimeSync = new LdapRealtimeSync(str);
/* 34 */       if (ldapRealtimeSync.init()) {
/* 35 */         this.newLog.info("开始进行部门实时同步：" + str1);
/* 36 */         if ("delete".equals(paramString)) {
/* 37 */           ldapRealtimeSync.deleteNode(str1, "2"); continue;
/*    */         } 
/* 39 */         ldapRealtimeSync.saveDepartment(str1);
/*    */       } 
/*    */     } 
/*    */     
/* 43 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/ldap/biz/HrmDepartmentSubscriber4Ldap.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */