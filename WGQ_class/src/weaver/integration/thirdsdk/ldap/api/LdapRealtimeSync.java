/*     */ package weaver.integration.thirdsdk.ldap.api;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.api.integration.ldap.bean.LdapBaseBean;
/*     */ import com.api.integration.ldap.bean.LdapFormartBean;
/*     */ import com.api.integration.ldap.bean.LdapMappingBean;
/*     */ import com.api.integration.ldap.bean.LdapSyncBean;
/*     */ import com.api.integration.ldap.bean.LdapSyncDataBean;
/*     */ import com.api.integration.ldap.service.LdapService;
/*     */ import com.api.integration.ldap.util.MappingType;
/*     */ import com.api.integration.util.RecordSetObj;
/*     */ import com.weaver.general.Util;
/*     */ import com.weaver.integration.ldap.sync.DataBean;
/*     */ import com.weaver.integration.ldap.sync.formart.OaFormart;
/*     */ import com.weaver.integration.ldap.util.LdapOper;
/*     */ import com.weaver.integration.ldap.util.LdapSuperOper;
/*     */ import java.io.UnsupportedEncodingException;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.Prop;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ public class LdapRealtimeSync
/*     */ {
/*  30 */   private Logger newLog = LoggerFactory.getLogger(LdapRealtimeSync.class);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private LdapOper ldapOper;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String ldapId;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private LdapBaseBean ldapBase;
/*     */ 
/*     */ 
/*     */   
/*     */   private List<LdapSyncBean> ldapSyncBeans;
/*     */ 
/*     */ 
/*     */   
/*     */   private List<LdapMappingBean> subcompanyMapping;
/*     */ 
/*     */ 
/*     */   
/*     */   private List<LdapMappingBean> departmentMapping;
/*     */ 
/*     */ 
/*     */   
/*     */   private List<LdapMappingBean> userMapping;
/*     */ 
/*     */ 
/*     */   
/*     */   private List<LdapMappingBean> ouMapping;
/*     */ 
/*     */ 
/*     */   
/*  69 */   private String subCompany_languageId = Prop.getPropValue("oaSyncOuMultiLanguage", "subCompany.languageId");
/*     */ 
/*     */ 
/*     */   
/*  73 */   private String subCompany_formatString = Prop.getPropValue("oaSyncOuMultiLanguage", "subCompany.formatString");
/*     */ 
/*     */ 
/*     */   
/*  77 */   private String department_languageId = Prop.getPropValue("oaSyncOuMultiLanguage", "department.languageId");
/*     */ 
/*     */ 
/*     */   
/*  81 */   private String department_formatString = Prop.getPropValue("oaSyncOuMultiLanguage", "department.formatString");
/*     */ 
/*     */ 
/*     */   
/*  85 */   private String user_languageId = Prop.getPropValue("oaSyncOuMultiLanguage", "user.languageId");
/*     */ 
/*     */ 
/*     */   
/*  89 */   private String user_formatString = Prop.getPropValue("oaSyncOuMultiLanguage", "user.formatString");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public LdapRealtimeSync(String paramString) {
/*  96 */     this.ldapId = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean init() {
/*     */     try {
/* 104 */       LdapService ldapService = new LdapService(null);
/*     */ 
/*     */       
/* 107 */       this.ldapBase = ldapService.queryByLdapId(this.ldapId);
/*     */       
/* 109 */       if (1 != this.ldapBase.getIsUsed() || 1 != this.ldapBase.getIsPush()) {
/* 110 */         this.newLog.info("实时反向同步初始化未开启：" + this.ldapId);
/* 111 */         return false;
/*     */       } 
/*     */ 
/*     */       
/* 115 */       this.ldapOper = (LdapOper)new LdapSuperOper(this.ldapBase);
/*     */ 
/*     */       
/* 118 */       this.ldapSyncBeans = ldapService.querySyncByLdapId(this.ldapId, "push");
/* 119 */       if (this.ldapSyncBeans == null || this.ldapSyncBeans.isEmpty()) {
/* 120 */         this.newLog.error("实时反向同步初始化未配置同步节点" + this.ldapId);
/* 121 */         return false;
/*     */       } 
/*     */ 
/*     */       
/* 125 */       this.newLog.error("获取到的同步节点信息：" + JSON.toJSONString(this.ldapSyncBeans));
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/* 131 */     catch (Exception exception) {
/* 132 */       this.newLog.error("实时同步初始化异常", exception);
/* 133 */       return false;
/*     */     } 
/* 135 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean deleteNode(String paramString1, String paramString2) {
/* 144 */     LdapService ldapService = new LdapService(null);
/* 145 */     LdapSyncDataBean ldapSyncDataBean = ldapService.getSyncDataByOAID(this.ldapId, paramString1, paramString2, "2");
/*     */     try {
/* 147 */       this.newLog.info("删除节点获取的dn:" + ldapSyncDataBean.getRdn());
/* 148 */       boolean bool = this.ldapOper.isExist(ldapSyncDataBean.getRdn());
/* 149 */       if (bool) {
/* 150 */         boolean bool1 = this.ldapOper.remove(ldapSyncDataBean.getRdn());
/* 151 */         if (bool1) {
/*     */           
/* 153 */           String str = "DELETE FROM LDAP_SYNC_DATA WHERE LDAPID=? AND OAID=? AND DATATYPE=? AND SYNCTYPE=?";
/* 154 */           RecordSet recordSet = new RecordSet();
/* 155 */           recordSet.executeUpdate(str, new Object[] { this.ldapId, paramString1, paramString2, "2" });
/*     */         } 
/* 157 */         return bool1;
/*     */       } 
/*     */       
/* 160 */       this.newLog.error("节点不存在");
/* 161 */       return false;
/*     */     }
/* 163 */     catch (Exception exception) {
/* 164 */       this.newLog.error("删除Ldap节点异常：" + exception);
/* 165 */       return false;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public LdapSyncBean getLdapSync(String paramString1, String paramString2, LdapSyncBean paramLdapSyncBean) {
/*     */     try {
/* 177 */       LdapService ldapService = new LdapService(null);
/* 178 */       LdapSyncBean ldapSyncBean = paramLdapSyncBean.clone();
/* 179 */       if (paramString2 != null && !"".equals(paramString2) && !"0".equals(paramString2)) {
/*     */         
/* 181 */         LdapSyncDataBean ldapSyncDataBean = ldapService.getSyncDataByOAID(this.ldapId, paramString2, "2", "2");
/* 182 */         ldapSyncBean.setDepartmentId(ldapSyncDataBean.getOaId());
/* 183 */         ldapSyncBean.setSubcompanyId(paramString1);
/* 184 */         ldapSyncBean.setBaseDomain(ldapSyncDataBean.getRdn());
/* 185 */         return ldapSyncBean;
/* 186 */       }  if (paramString1 != null && !"".equals(paramString1) && !"0".equals(paramString1)) {
/*     */         
/* 188 */         LdapSyncDataBean ldapSyncDataBean = ldapService.getSyncDataByOAID(this.ldapId, paramString1, "1", "2");
/* 189 */         ldapSyncBean.setSubcompanyId(ldapSyncDataBean.getOaId());
/* 190 */         ldapSyncBean.setBaseDomain(ldapSyncDataBean.getRdn());
/* 191 */         return ldapSyncBean;
/*     */       } 
/* 193 */       this.newLog.error("输入的部门与分部信息均为空");
/* 194 */       return ldapSyncBean;
/*     */     }
/* 196 */     catch (Exception exception) {
/* 197 */       this.newLog.error("获取上级部门/分部的域dn异常：", exception);
/* 198 */       return null;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean saveDepartment(String paramString) {
/*     */     try {
/* 209 */       for (LdapSyncBean ldapSyncBean1 : this.ldapSyncBeans) {
/* 210 */         boolean bool2; LdapService ldapService = new LdapService(null);
/*     */         
/* 212 */         this.ouMapping = ldapService.queryMappingByLdapId(this.ldapId, MappingType.OAOU);
/*     */         
/* 214 */         this.departmentMapping = ldapService.queryMappingByLdapId(this.ldapId, MappingType.OADEPARTMENT);
/*     */         
/* 216 */         String str1 = "select * from HrmDepartment where id=?";
/* 217 */         RecordSetObj recordSetObj = new RecordSetObj();
/* 218 */         recordSetObj.executeQuery(str1, new Object[] { paramString });
/* 219 */         if (!recordSetObj.next()) {
/* 220 */           this.newLog.error("没有找到部门" + paramString);
/*     */           continue;
/*     */         } 
/* 223 */         Map<String, String> map = recordSetObj.getMap();
/* 224 */         String str2 = (String)map.get("subcompanyid1");
/*     */         
/* 226 */         if (!departmentSyncJudge(str2, paramString, ldapSyncBean1)) {
/*     */           continue;
/*     */         }
/*     */         
/* 230 */         LdapSyncBean ldapSyncBean2 = getLdapSync(str2, (String)map.get("supdepid"), ldapSyncBean1);
/* 231 */         if (ldapSyncBean2 == null) {
/* 232 */           this.newLog.error("上级部门/分部没有同步信息，同步失败：" + paramString);
/*     */         }
/*     */         
/* 235 */         String str3 = "";
/* 236 */         String str4 = "";
/* 237 */         String str5 = "";
/*     */         
/* 239 */         for (byte b = 0; b < this.ouMapping.size(); b++) {
/* 240 */           LdapMappingBean ldapMappingBean = this.ouMapping.get(b);
/* 241 */           if ("depkey".equalsIgnoreCase(ldapMappingBean.getAttributeName())) {
/* 242 */             str3 = ldapMappingBean.getAttributeValue();
/*     */           }
/* 244 */           else if ("depvalue".equalsIgnoreCase(ldapMappingBean.getAttributeName())) {
/* 245 */             str4 = formatMultiLanguage((String)map.get(ldapMappingBean.getAttributeValue()), this.department_languageId, this.department_formatString);
/*     */           }
/* 247 */           else if ("depClass".equalsIgnoreCase(ldapMappingBean.getAttributeName())) {
/* 248 */             str5 = ldapMappingBean.getAttributeValue();
/*     */           } 
/*     */         } 
/* 251 */         String str6 = str3 + "=" + str4 + "," + ldapSyncBean2.getBaseDomain();
/*     */ 
/*     */         
/* 254 */         String str7 = (String)map.get("id");
/* 255 */         LdapSyncDataBean ldapSyncDataBean = ldapService.getSyncDataByOAID(this.ldapId, str7, "2", "2");
/*     */         
/* 257 */         boolean bool1 = this.ldapOper.isExist(str6);
/* 258 */         if (ldapSyncDataBean != null && !bool1 && 
/* 259 */           !str6.equalsIgnoreCase(ldapSyncDataBean.getRdn()) && this.ldapOper.isExist(ldapSyncDataBean.getRdn())) {
/*     */           
/* 261 */           bool1 = this.ldapOper.rename(ldapSyncDataBean.getRdn(), str6);
/* 262 */           if (bool1) {
/* 263 */             String str = "update ldap_sync_data set RDN = replace(RDN,'," + ldapSyncDataBean.getRdn() + "','," + str6 + "') where ldapId = ? and syncType = '2' and rdn like '%," + ldapSyncDataBean.getRdn() + "'";
/* 264 */             RecordSetObj recordSetObj1 = new RecordSetObj();
/* 265 */             recordSetObj1.executeUpdate(str, new Object[] { ldapSyncDataBean.getLdapId() });
/*     */           } 
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 271 */         if (bool1) {
/* 272 */           bool2 = this.ldapOper.modify(str6, dataMapping(map, this.departmentMapping));
/*     */         } else {
/* 274 */           String[] arrayOfString = str5.split(";");
/* 275 */           bool2 = this.ldapOper.add(str6, arrayOfString, dataMapping(map, this.departmentMapping));
/*     */         } 
/* 277 */         if (!bool2) {
/* 278 */           this.newLog.error("同步失败，请查看系统日志");
/*     */           
/*     */           continue;
/*     */         } 
/* 282 */         ldapSyncDataBean = new LdapSyncDataBean();
/* 283 */         ldapSyncDataBean.setLdapId(this.ldapId);
/* 284 */         ldapSyncDataBean.setOaId(str7);
/* 285 */         ldapSyncDataBean.setSyncType("2");
/* 286 */         ldapSyncDataBean.setDataType("2");
/* 287 */         ldapSyncDataBean.setRdn(str6);
/* 288 */         ldapSyncDataBean.setUuid(this.ldapOper.getId(str6));
/* 289 */         ldapService.saveSyncData(ldapSyncDataBean);
/*     */       } 
/*     */       
/* 292 */       return true;
/* 293 */     } catch (Exception exception) {
/* 294 */       this.newLog.error("实时同步保存部门报错：", exception);
/* 295 */       return false;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean saveSubcompany(String paramString) {
/*     */     try {
/* 306 */       for (LdapSyncBean ldapSyncBean1 : this.ldapSyncBeans) {
/* 307 */         boolean bool2; LdapService ldapService = new LdapService(null);
/*     */         
/* 309 */         this.ouMapping = ldapService.queryMappingByLdapId(this.ldapId, MappingType.OAOU);
/*     */         
/* 311 */         this.subcompanyMapping = ldapService.queryMappingByLdapId(this.ldapId, MappingType.OASUBCOMPANY);
/*     */         
/* 313 */         String str1 = "select * from HrmSubCompany where id=?";
/* 314 */         RecordSetObj recordSetObj = new RecordSetObj();
/* 315 */         recordSetObj.executeQuery(str1, new Object[] { paramString });
/* 316 */         if (!recordSetObj.next()) {
/* 317 */           this.newLog.error("没有找到分部：" + paramString);
/*     */           continue;
/*     */         } 
/* 320 */         Map<String, String> map = recordSetObj.getMap();
/*     */ 
/*     */         
/* 323 */         if (!subcompanySyncJudge(paramString, ldapSyncBean1)) {
/*     */           continue;
/*     */         }
/*     */ 
/*     */         
/* 328 */         LdapSyncBean ldapSyncBean2 = getLdapSync((String)map.get("supsubcomid"), "", ldapSyncBean1);
/* 329 */         if (ldapSyncBean2 == null) {
/* 330 */           this.newLog.error("上级部门/分部没有同步信息，同步失败：" + paramString);
/*     */         }
/* 332 */         String str2 = "";
/* 333 */         String str3 = "";
/* 334 */         String str4 = "";
/*     */ 
/*     */         
/* 337 */         for (byte b = 0; b < this.ouMapping.size(); b++) {
/* 338 */           LdapMappingBean ldapMappingBean = this.ouMapping.get(b);
/* 339 */           if ("subkey".equalsIgnoreCase(ldapMappingBean.getAttributeName())) {
/* 340 */             str2 = ldapMappingBean.getAttributeValue();
/*     */           }
/* 342 */           else if ("subvalue".equalsIgnoreCase(ldapMappingBean.getAttributeName())) {
/* 343 */             str3 = formatMultiLanguage((String)map.get(ldapMappingBean.getAttributeValue()), this.subCompany_languageId, this.subCompany_formatString);
/*     */           }
/* 345 */           else if ("subClass".equalsIgnoreCase(ldapMappingBean.getAttributeName())) {
/* 346 */             str4 = ldapMappingBean.getAttributeValue();
/*     */           } 
/*     */         } 
/* 349 */         String str5 = str2 + "=" + str3 + "," + ldapSyncBean2.getBaseDomain();
/* 350 */         this.newLog.info("同步分部dn:" + str5);
/*     */         
/* 352 */         LdapSyncDataBean ldapSyncDataBean = ldapService.getSyncDataByOAID(this.ldapId, paramString, "1", "2");
/* 353 */         boolean bool1 = this.ldapOper.isExist(str5);
/*     */         
/* 355 */         if (ldapSyncDataBean != null && !bool1 && 
/* 356 */           !str5.equalsIgnoreCase(ldapSyncDataBean.getRdn()) && this.ldapOper.isExist(ldapSyncDataBean.getRdn())) {
/*     */           
/* 358 */           this.newLog.info("移动失败新增处理:" + str5);
/* 359 */           bool1 = this.ldapOper.rename(ldapSyncDataBean.getRdn(), str5);
/* 360 */           if (bool1) {
/* 361 */             String str = "update ldap_sync_data set RDN = replace(RDN,'," + ldapSyncDataBean.getRdn() + "','," + str5 + "') where ldapId = ? and syncType = '2' and rdn like '%," + ldapSyncDataBean.getRdn() + "'";
/* 362 */             RecordSetObj recordSetObj1 = new RecordSetObj();
/* 363 */             recordSetObj1.executeUpdate(str, new Object[] { ldapSyncDataBean.getLdapId() });
/*     */           } 
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 369 */         if (bool1) {
/* 370 */           bool2 = this.ldapOper.modify(str5, dataMapping(map, this.subcompanyMapping));
/*     */         } else {
/* 372 */           String[] arrayOfString = str4.split(";");
/* 373 */           bool2 = this.ldapOper.add(str5, arrayOfString, dataMapping(map, this.subcompanyMapping));
/*     */         } 
/*     */         
/* 376 */         if (!bool2) {
/* 377 */           this.newLog.error("实时同步分部同步失败");
/*     */           continue;
/*     */         } 
/* 380 */         ldapSyncDataBean = new LdapSyncDataBean();
/* 381 */         ldapSyncDataBean.setLdapId(this.ldapId);
/* 382 */         ldapSyncDataBean.setOaId(paramString);
/* 383 */         ldapSyncDataBean.setSyncType("2");
/* 384 */         ldapSyncDataBean.setDataType("1");
/* 385 */         ldapSyncDataBean.setRdn(str5);
/* 386 */         ldapSyncDataBean.setUuid(this.ldapOper.getId(str5));
/* 387 */         ldapService.saveSyncData(ldapSyncDataBean);
/*     */       } 
/*     */       
/* 390 */       return true;
/* 391 */     } catch (Exception exception) {
/* 392 */       this.newLog.error("实时同步保存分部报错：", exception);
/* 393 */       return false;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean saveUser(String paramString1, String paramString2) {
/*     */     try {
/* 405 */       for (LdapSyncBean ldapSyncBean1 : this.ldapSyncBeans) {
/* 406 */         boolean bool2; LdapService ldapService = new LdapService(null);
/*     */         
/* 408 */         this.ouMapping = ldapService.queryMappingByLdapId(this.ldapId, MappingType.OAOU);
/*     */         
/* 410 */         this.userMapping = ldapService.queryMappingByLdapId(this.ldapId, MappingType.OAUSER);
/*     */         
/* 412 */         int i = -1;
/* 413 */         for (LdapMappingBean ldapMappingBean : this.userMapping) {
/* 414 */           if ("password".equals(ldapMappingBean.getAttributeName())) {
/* 415 */             i = ldapMappingBean.getId();
/*     */           }
/*     */         } 
/* 418 */         if (i != -1) {
/* 419 */           this.userMapping.remove(i);
/*     */         }
/*     */         
/* 422 */         String str1 = "SELECT * FROM HRMRESOURCE WHERE ID=?";
/* 423 */         RecordSetObj recordSetObj = new RecordSetObj();
/* 424 */         recordSetObj.executeQuery(str1, new Object[] { paramString1 });
/* 425 */         if (!recordSetObj.next()) {
/* 426 */           this.newLog.error("未找到人员：" + paramString1);
/*     */           continue;
/*     */         } 
/* 429 */         Map<String, String> map = recordSetObj.getMap();
/* 430 */         if (paramString2 != null && !"".equals(paramString2)) {
/* 431 */           map.put("password", paramString2);
/*     */         }
/* 433 */         String str2 = Util.null2String(map.get("subcompanyid1"));
/* 434 */         String str3 = Util.null2String(map.get("departmentid"));
/*     */         
/* 436 */         if (!userSyncJudge(str2, str3, ldapSyncBean1)) {
/*     */           continue;
/*     */         }
/*     */         
/* 440 */         if ("".equals(Util.null2String(map.get("loginid")))) {
/* 441 */           this.newLog.error("同步人员：" + (String)map.get("lastname") + "账号为空");
/*     */         }
/*     */ 
/*     */         
/* 445 */         LdapSyncBean ldapSyncBean2 = ldapSyncBean1.clone();
/* 446 */         if (1 == ldapSyncBean1.getSyncOrg())
/* 447 */           ldapSyncBean2 = getLdapSync(str2, str3, ldapSyncBean1); 
/* 448 */         if (ldapSyncBean2 == null) {
/* 449 */           this.newLog.info("人员：" + paramString1 + "获取到的上级部门同步信息为空，不进行同步");
/*     */           continue;
/*     */         } 
/* 452 */         String str4 = "";
/* 453 */         String str5 = "";
/* 454 */         String str6 = "";
/* 455 */         String str7 = "";
/*     */         
/* 457 */         for (byte b = 0; b < this.ouMapping.size(); b++) {
/* 458 */           LdapMappingBean ldapMappingBean = this.ouMapping.get(b);
/* 459 */           if ("userkey".equalsIgnoreCase(ldapMappingBean.getAttributeName())) {
/* 460 */             str4 = ldapMappingBean.getAttributeValue();
/*     */           }
/* 462 */           else if ("uservalue".equalsIgnoreCase(ldapMappingBean.getAttributeName())) {
/* 463 */             str6 = ldapMappingBean.getAttributeValue();
/* 464 */             str5 = formatMultiLanguage(map.get(ldapMappingBean.getAttributeValue()), this.user_languageId, this.user_formatString);
/*     */           }
/* 466 */           else if ("userClass".equalsIgnoreCase(ldapMappingBean.getAttributeName())) {
/* 467 */             str7 = ldapMappingBean.getAttributeValue();
/*     */           } 
/*     */         } 
/* 470 */         String str8 = str4 + "=" + str5 + "," + ldapSyncBean2.getBaseDomain();
/* 471 */         this.newLog.info("同步人员dn为：" + str8);
/* 472 */         LdapSyncDataBean ldapSyncDataBean = ldapService.getSyncDataByOAID(this.ldapId, paramString1, "3", "2");
/* 473 */         boolean bool = false;
/*     */         
/* 475 */         if (str5 == null || str5.equals("")) {
/* 476 */           if (ldapSyncDataBean == null || ldapSyncDataBean.getRdn() == null || ldapSyncDataBean.getRdn().equals("")) {
/* 477 */             this.newLog.error("帐号值为空，不做处理.人员id为:" + paramString1);
/* 478 */             return true;
/*     */           } 
/* 480 */           str8 = ldapSyncDataBean.getRdn();
/* 481 */           bool = true;
/*     */         } 
/*     */ 
/*     */         
/* 485 */         boolean bool1 = this.ldapOper.isExist(str8);
/* 486 */         if (ldapSyncDataBean != null && !bool1 && 
/* 487 */           !str8.equalsIgnoreCase(ldapSyncDataBean.getRdn()) && this.ldapOper.isExist(ldapSyncDataBean.getRdn()))
/*     */         {
/* 489 */           bool1 = this.ldapOper.rename(ldapSyncDataBean.getRdn(), str8);
/*     */         }
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 495 */         String str9 = String.valueOf(ldapSyncBean1.getSyncPassword());
/* 496 */         this.newLog.info("获取的实时同步密码开关为：" + str9);
/* 497 */         int j = Util.getIntValue(map.get("status"), 1);
/* 498 */         List<DataBean> list = dataMapping(map, this.userMapping);
/* 499 */         if (bool1 && !bool) {
/*     */           
/* 501 */           if (j > 3) {
/* 502 */             bool = true;
/*     */             
/* 504 */             bool2 = this.ldapOper.modify(ldapSyncDataBean.getRdn(), list);
/*     */           } else {
/* 506 */             if ("1".equals(str9) && paramString2 != null && !"".equals(paramString2)) {
/*     */               
/* 508 */               boolean bool3 = false;
/* 509 */               for (DataBean dataBean : list) {
/* 510 */                 if ("unicodePwd".equals(dataBean.getAttrName())) {
/* 511 */                   String str = "\"" + paramString2 + "\"";
/* 512 */                   byte[] arrayOfByte = str.getBytes("UTF-16LE");
/* 513 */                   dataBean.setAttrValue(arrayOfByte);
/* 514 */                   dataBean.setType(0);
/* 515 */                   bool3 = true;
/*     */                 } 
/*     */               } 
/* 518 */               if (!bool3) {
/* 519 */                 this.newLog.error("Ldap实时同步人员密码未设置，同步不生效");
/*     */               }
/*     */             } 
/* 522 */             this.newLog.info("修改属性信息为：" + JSON.toJSONString(list));
/* 523 */             bool2 = this.ldapOper.modify(str8, list);
/*     */           }
/*     */         
/*     */         } else {
/*     */           
/* 528 */           String str10 = Util.null2String(map.get("id"));
/* 529 */           String str11 = Util.null2String(map.get("lastname"));
/* 530 */           if (str5 == null || str5.equals("")) {
/* 531 */             this.newLog.error("人员id:" + str10 + ",对应ad标识字段:" + str6 + "的值为空,AD里不新建!");
/*     */             continue;
/*     */           } 
/* 534 */           if (j > 3) {
/* 535 */             this.newLog.error("人员:" + str8 + ",已离职,AD里不新建!");
/*     */             continue;
/*     */           } 
/* 538 */           String[] arrayOfString = str7.split(";");
/* 539 */           List<DataBean> list1 = dataMapping(map, this.userMapping);
/* 540 */           if ("1".equals(str9) && paramString2 != null && !"".equals(paramString2)) {
/*     */             
/* 542 */             boolean bool3 = false;
/* 543 */             for (DataBean dataBean : list1) {
/* 544 */               if ("unicodePwd".equals(dataBean.getAttrName())) {
/* 545 */                 String str = "\"" + paramString2 + "\"";
/* 546 */                 byte[] arrayOfByte = str.getBytes("UTF-16LE");
/* 547 */                 dataBean.setAttrValue(arrayOfByte);
/* 548 */                 dataBean.setType(0);
/* 549 */                 bool3 = true;
/*     */               } 
/*     */             } 
/* 552 */             if (!bool3) {
/* 553 */               this.newLog.error("Ldap实时同步人员密码未设置，同步不生效");
/*     */             }
/*     */           } 
/* 556 */           bool2 = this.ldapOper.add(str8, arrayOfString, list1);
/*     */         } 
/*     */         
/* 559 */         if (!bool2) {
/*     */           continue;
/*     */         }
/*     */         
/* 563 */         if (bool) {
/* 564 */           String str = "DELETE FROM LDAP_SYNC_DATA WHERE LDAPID = ? AND OAID = ?  AND DATATYPE = ? AND SYNCTYPE = ?";
/* 565 */           RecordSetObj recordSetObj1 = new RecordSetObj();
/* 566 */           boolean bool3 = recordSetObj1.executeUpdate(str, new Object[] { this.ldapId, paramString1, "3", "2" });
/* 567 */           this.newLog.error("人员id为:" + paramString1 + ",离职操作反向同步时清LDAP_SYNC_DATA表里记录。");
/*     */           continue;
/*     */         } 
/* 570 */         ldapSyncDataBean = new LdapSyncDataBean();
/* 571 */         ldapSyncDataBean.setLdapId(this.ldapId);
/* 572 */         ldapSyncDataBean.setOaId(paramString1);
/* 573 */         ldapSyncDataBean.setSyncType("2");
/* 574 */         ldapSyncDataBean.setDataType("3");
/* 575 */         ldapSyncDataBean.setRdn(str8);
/* 576 */         ldapSyncDataBean.setUuid(this.ldapOper.getId(str8));
/* 577 */         ldapService.saveSyncData(ldapSyncDataBean);
/*     */       } 
/*     */ 
/*     */       
/* 581 */       return true;
/* 582 */     } catch (Exception exception) {
/* 583 */       this.newLog.error("实时同步保存人员报错：", exception);
/* 584 */       return true;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean modifyADPWD(Map<String, String> paramMap, String paramString) {
/* 595 */     LdapService ldapService = new LdapService(null);
/*     */     
/* 597 */     String str1 = Util.null2String(paramMap.get("userid"));
/* 598 */     String str2 = Util.null2String(paramMap.get("loginid"));
/* 599 */     String str3 = Util.null2String(paramMap.get("password"));
/*     */     
/* 601 */     this.newLog.error("修改AD密码新接口>>>>>>>>>>>帐号：" + str2 + "密码：" + str3);
/* 602 */     String str4 = "\"" + str3 + "\"";
/* 603 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 604 */     if (!this.ldapBase.getProtocol().equalsIgnoreCase("ldaps")) {
/* 605 */       this.newLog.error("LDAP集成配置需要启用SSL，才能修改密码！");
/* 606 */       return false;
/*     */     } 
/*     */     try {
/* 609 */       byte[] arrayOfByte = str4.getBytes("UTF-16LE");
/* 610 */       hashMap.put("unicodePwd", arrayOfByte);
/* 611 */       if (this.ldapOper.modifyArrts(paramString, hashMap)) {
/* 612 */         return true;
/*     */       }
/* 614 */     } catch (UnsupportedEncodingException unsupportedEncodingException) {
/* 615 */       this.newLog.error("处理AD失败！", unsupportedEncodingException);
/* 616 */       unsupportedEncodingException.printStackTrace();
/*     */     } 
/* 618 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean departmentSyncJudge(String paramString1, String paramString2, LdapSyncBean paramLdapSyncBean) {
/* 655 */     if (1 != paramLdapSyncBean.getSyncOrg()) {
/* 656 */       return false;
/*     */     }
/* 658 */     if (!userScope(paramString1, paramString2, paramLdapSyncBean))
/* 659 */       return false; 
/* 660 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean subcompanySyncJudge(String paramString, LdapSyncBean paramLdapSyncBean) {
/* 670 */     if (1 != paramLdapSyncBean.getSyncOrg()) {
/* 671 */       return false;
/*     */     }
/* 673 */     if (!userScope(paramString, "", paramLdapSyncBean))
/* 674 */       return false; 
/* 675 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean userSyncJudge(String paramString1, String paramString2, LdapSyncBean paramLdapSyncBean) {
/* 687 */     if (1 != paramLdapSyncBean.getSyncUser()) {
/* 688 */       return false;
/*     */     }
/* 690 */     if (!userScope(paramString1, paramString2, paramLdapSyncBean))
/* 691 */       return false; 
/* 692 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean userScope(String paramString1, String paramString2, LdapSyncBean paramLdapSyncBean) {
/* 703 */     String str1 = Util.null2String(paramLdapSyncBean.getSubcompanyId());
/*     */     
/* 705 */     String str2 = Util.null2String(paramLdapSyncBean.getDepartmentId());
/*     */     
/* 707 */     byte b = 50;
/*     */     
/* 709 */     if (!"".equals(str2) && !"0".equals(str2)) {
/*     */       
/* 711 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 712 */       RecordSetObj recordSetObj = new RecordSetObj();
/* 713 */       recordSetObj.executeQuery("select id,supdepid from HrmDepartment", new Object[0]);
/* 714 */       while (recordSetObj.next()) {
/* 715 */         hashMap.put(recordSetObj.getString("id"), Util.null2String(recordSetObj.getString("supdepid")));
/*     */       }
/*     */       
/* 718 */       String str = paramString2;
/* 719 */       while (b > 0) {
/* 720 */         if (str.equals(str2))
/* 721 */           return true; 
/* 722 */         if ("".equals(str) || "0".equals(str))
/* 723 */           return false; 
/* 724 */         b--;
/* 725 */         str = (String)hashMap.get(str);
/*     */       } 
/* 727 */       return false;
/*     */     } 
/*     */     
/* 730 */     if (!"".equals(str1) && !"0".equals(str1)) {
/* 731 */       b = 50;
/*     */       
/* 733 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 734 */       RecordSetObj recordSetObj = new RecordSetObj();
/* 735 */       recordSetObj.executeQuery("select id,supsubcomid from HrmSubCompany", new Object[0]);
/* 736 */       while (recordSetObj.next()) {
/* 737 */         hashMap.put(recordSetObj.getString("id"), 
/* 738 */             Util.null2String(recordSetObj.getString("supsubcomid")));
/*     */       }
/* 740 */       String str = paramString1;
/* 741 */       while (b > 0) {
/* 742 */         if (str.equals(str1))
/* 743 */           return true; 
/* 744 */         if ("".equals(str) || "0".equals(str))
/* 745 */           return false; 
/* 746 */         b--;
/* 747 */         str = (String)hashMap.get(str);
/*     */       } 
/* 749 */       return false;
/*     */     } 
/* 751 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<DataBean> dataMapping(Map<String, String> paramMap, List<LdapMappingBean> paramList) {
/* 760 */     this.newLog.info("dataMap:" + JSON.toJSONString(paramMap));
/* 761 */     this.newLog.info("mapping:" + JSON.toJSONString(paramList));
/* 762 */     ArrayList<DataBean> arrayList = new ArrayList();
/*     */ 
/*     */ 
/*     */     
/* 766 */     if (paramList != null && paramMap != null) {
/* 767 */       for (byte b = 0; b < paramList.size(); b++) {
/* 768 */         LdapMappingBean ldapMappingBean = paramList.get(b);
/* 769 */         DataBean dataBean = new DataBean();
/*     */         
/* 771 */         if ("0".equals(ldapMappingBean.getAttributeType())) {
/*     */           
/* 773 */           dataBean.setAttrName(ldapMappingBean.getAttributeName());
/* 774 */           dataBean.setAttrValue(paramMap.get(ldapMappingBean.getAttributeValue().toLowerCase()));
/* 775 */           dataBean.transOperToType(ldapMappingBean.getAttributeOperation());
/* 776 */           arrayList.add(dataBean);
/*     */         }
/* 778 */         else if ("1".equals(ldapMappingBean.getAttributeType())) {
/*     */           
/* 780 */           dataBean.setAttrName(ldapMappingBean.getAttributeName());
/* 781 */           dataBean.setAttrValue(ldapMappingBean.getAttributeValue());
/*     */ 
/*     */           
/* 784 */           dataBean.transOperToType(ldapMappingBean.getAttributeOperation());
/* 785 */           arrayList.add(dataBean);
/*     */         }
/* 787 */         else if ("2".equals(ldapMappingBean.getAttributeType())) {
/*     */ 
/*     */           
/*     */           try {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 796 */             LdapFormartBean ldapFormartBean = getFormart(ldapMappingBean.getAttributeValue());
/* 797 */             if (ldapFormartBean != null) {
/* 798 */               OaFormart oaFormart = (OaFormart)Class.forName(ldapFormartBean.getFormartClass()).newInstance();
/*     */               
/* 800 */               Object object = oaFormart.formart(this.ldapBase.clone(), paramMap, ldapFormartBean.getFormartParams());
/* 801 */               if (oaFormart.getStatus()) {
/* 802 */                 dataBean.setAttrName(ldapMappingBean.getAttributeName());
/* 803 */                 dataBean.setAttrValue(object);
/* 804 */                 dataBean.transOperToType(ldapMappingBean.getAttributeOperation());
/* 805 */                 arrayList.add(dataBean);
/*     */               } 
/*     */             } 
/* 808 */           } catch (Exception exception) {
/* 809 */             this.newLog.error(exception);
/* 810 */             exception.printStackTrace();
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     }
/* 815 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private LdapFormartBean getFormart(String paramString) {
/* 824 */     String str = "select * from ldap_formart where formartId = ? ";
/* 825 */     RecordSetObj recordSetObj = new RecordSetObj();
/* 826 */     recordSetObj.executeQuery(str, new Object[] { paramString });
/*     */     
/* 828 */     LdapFormartBean ldapFormartBean = null;
/* 829 */     if (recordSetObj.next()) {
/* 830 */       ldapFormartBean = (LdapFormartBean)recordSetObj.getBean(LdapFormartBean.class);
/*     */     }
/* 832 */     return ldapFormartBean;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String formatMultiLanguage(String paramString1, String paramString2, String paramString3) {
/* 845 */     String str = paramString1;
/* 846 */     paramString2 = Util.null2String(paramString2, "7");
/* 847 */     paramString3 = Util.null2String(paramString3);
/* 848 */     String[] arrayOfString = paramString2.split(",");
/* 849 */     if ("".equals(paramString3)) {
/* 850 */       str = Util.formatMultiLang(paramString1, arrayOfString[0]);
/*     */     } else {
/* 852 */       str = paramString3;
/* 853 */       for (String str1 : arrayOfString) {
/* 854 */         String str2 = Util.formatMultiLang(paramString1, str1);
/* 855 */         this.newLog.info(String.format("[language:%s][multiLang:%s]", new Object[] { str1, str2 }));
/* 856 */         str = str.replace("$" + str1 + "$", str2);
/*     */       } 
/*     */     } 
/* 859 */     this.newLog.info(String.format("[source:%s][languageId:%s][formatString:%s][target:%s]", new Object[] { paramString1, paramString2, paramString3, str }));
/* 860 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/ldap/api/LdapRealtimeSync.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */