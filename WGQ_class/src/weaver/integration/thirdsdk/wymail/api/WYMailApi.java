/*     */ package weaver.integration.thirdsdk.wymail.api;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.engine.integration.util.HttpsUtil;
/*     */ import java.net.URLEncoder;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.mail.WYUtil.rsa.RSASignatureToQiye;
/*     */ import weaver.interfaces.security.RSATool;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WYMailApi
/*     */ {
/*  23 */   private static Logger newlog = LoggerFactory.getLogger(WYMailApi.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject getMailUnreadCount(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/*  33 */     String str1 = paramString5 + "/mailbox/getUnreadMsg";
/*  34 */     JSONObject jSONObject = null;
/*  35 */     String str2 = System.currentTimeMillis() + "";
/*  36 */     paramString1 = paramString1.substring(0, paramString1.indexOf("@"));
/*     */     try {
/*  38 */       JSONObject jSONObject1 = new JSONObject();
/*  39 */       JSONArray jSONArray = new JSONArray();
/*  40 */       jSONArray.add(Integer.valueOf(1));
/*  41 */       jSONArray.add(Integer.valueOf(5));
/*  42 */       jSONObject1.put("fids", jSONArray);
/*  43 */       String str3 = jSONObject1.toString();
/*  44 */       String str4 = "account_name=" + paramString1 + "&domain=" + paramString3 + "&product=" + paramString4 + "&time=" + str2;
/*  45 */       String str5 = RSASignatureToQiye.generateSigature(paramString2, str4);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  53 */       str1 = str1 + "?account_name=" + paramString1 + "&domain=" + paramString3 + "&product=" + paramString4 + "&sign=" + str5 + "&time=" + str2;
/*     */       
/*  55 */       newlog.error("最终获取邮件的url是：" + str1);
/*  56 */       String str6 = HttpsUtil.httpsRequest(str1, "POST", null);
/*  57 */       jSONObject = JSONObject.parseObject(str6);
/*  58 */     } catch (Exception exception) {
/*  59 */       newlog.error("获取网易邮箱未读邮件出错：", exception);
/*     */     } 
/*     */     
/*  62 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject getUnReadList(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/*  72 */     JSONObject jSONObject = new JSONObject();
/*  73 */     RecordSet recordSet = new RecordSet();
/*  74 */     recordSet.executeQuery("select * from outter_sys where typename='11'", new Object[0]);
/*  75 */     recordSet.next();
/*     */     try {
/*  77 */       String str = mailList(paramString1, paramString4, paramString2, paramString3, paramString5);
/*  78 */       jSONObject = JSONObject.parseObject(str);
/*  79 */     } catch (Exception exception) {
/*  80 */       newlog.error("获取未读邮件列表出错", exception);
/*  81 */       throw exception;
/*     */     } 
/*  83 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String mailList(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/*  95 */     String str1 = paramString5 + "/mailbox/listMessages";
/*  96 */     String str2 = "";
/*  97 */     String str3 = System.currentTimeMillis() + "";
/*  98 */     String str4 = paramString1;
/*  99 */     str4 = str4.substring(0, str4.indexOf("@"));
/*     */     try {
/* 101 */       JSONObject jSONObject = new JSONObject();
/* 102 */       JSONArray jSONArray = new JSONArray();
/* 103 */       jSONArray.add(Integer.valueOf(1));
/* 104 */       jSONObject.put("fids", jSONArray);
/* 105 */       jSONObject.put("order", "receivedDate");
/* 106 */       jSONObject.put("desc", Boolean.valueOf(true));
/* 107 */       jSONObject.put("start", Integer.valueOf(0));
/* 108 */       jSONObject.put("limit", Integer.valueOf(1000000));
/* 109 */       jSONObject.put("returnTotal", Boolean.valueOf(true));
/* 110 */       String str5 = jSONObject.toString();
/* 111 */       String str6 = "account_name=" + str4 + "&domain=" + paramString3 + "&params=" + str5 + "&product=" + paramString4 + "&time=" + str3;
/* 112 */       String str7 = RSATool.generateSHA1withRSASigature(str6, paramString2);
/*     */       
/* 114 */       str1 = str1 + "?account_name=" + str4 + "&domain=" + paramString3 + "&params=" + URLEncoder.encode(str5, "utf8") + "&product=" + paramString4 + "&sign=" + str7 + "&time=" + str3;
/* 115 */       newlog.error("最终获取邮件的url是：" + str1);
/* 116 */       str2 = HttpsUtil.httpsRequest(str1, "POST", null);
/* 117 */       newlog.error("调用获取邮件列表得到结果是：" + str2);
/* 118 */     } catch (Exception exception) {
/* 119 */       newlog.error("获取网易邮箱的未读邮件列表出错：", exception);
/*     */     } 
/* 121 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getSingleMailUrl(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/* 133 */     RecordSet recordSet = new RecordSet();
/* 134 */     long l = System.currentTimeMillis();
/* 135 */     StringBuffer stringBuffer = new StringBuffer(paramString5);
/* 136 */     String str = paramString2;
/* 137 */     str = str.substring(0, str.indexOf("@"));
/*     */     try {
/* 139 */       RSATool rSATool = new RSATool();
/*     */       
/* 141 */       rSATool.setPrivateKey(paramString3);
/* 142 */       String str1 = RSATool.generateSHA1withRSASigature(str + paramString4 + l, rSATool.getPrivateKey());
/*     */       
/* 144 */       String str2 = "0";
/* 145 */       stringBuffer.append("?domain=")
/* 146 */         .append(paramString4)
/* 147 */         .append("&account_name=")
/* 148 */         .append(str)
/* 149 */         .append("&time=")
/* 150 */         .append(l)
/* 151 */         .append("&enc=")
/* 152 */         .append(str1)
/* 153 */         .append("&language=")
/* 154 */         .append(str2)
/* 155 */         .append("&mid=")
/* 156 */         .append(paramString1);
/* 157 */     } catch (Exception exception) {
/* 158 */       newlog.error("获取单个邮件单点登录出错", exception);
/*     */     } 
/*     */     
/* 161 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List getAllALYDeptids(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 173 */     ArrayList<String> arrayList = new ArrayList();
/* 174 */     String str1 = paramString4 + "/unit/getUnitList";
/* 175 */     String str2 = System.currentTimeMillis() + "";
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 180 */       String str3 = "domain=" + paramString1 + "&product=" + paramString2 + "&time=" + str2;
/* 181 */       String str4 = RSATool.generateSHA1withRSASigature(str3, paramString3);
/* 182 */       StringBuffer stringBuffer = new StringBuffer(str1);
/* 183 */       stringBuffer.append("?domain=")
/* 184 */         .append(paramString1)
/* 185 */         .append("&product=")
/* 186 */         .append(paramString2)
/* 187 */         .append("&time=")
/* 188 */         .append(str2)
/* 189 */         .append("&sign=")
/* 190 */         .append(str4);
/* 191 */       String str5 = HttpsUtil.httpsRequest(stringBuffer.toString(), "POST", null);
/* 192 */       newlog.error("获取网易邮箱的部门列表结果：" + str5);
/* 193 */       JSONObject jSONObject = JSONObject.parseObject(str5);
/* 194 */       JSONArray jSONArray = jSONObject.getJSONArray("con");
/* 195 */       for (byte b = 0; b < jSONArray.size(); b++) {
/* 196 */         JSONObject jSONObject1 = jSONArray.getJSONObject(b);
/* 197 */         String str = jSONObject1.getString("unit_id");
/* 198 */         arrayList.add(str);
/*     */       } 
/* 200 */     } catch (Exception exception) {
/* 201 */       newlog.error("获取网易邮箱部门id列表出错", exception);
/*     */     } 
/* 203 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject testWY(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 212 */     JSONObject jSONObject = new JSONObject();
/* 213 */     String str1 = paramString4 + "/unit/getUnitList";
/* 214 */     String str2 = System.currentTimeMillis() + "";
/*     */     try {
/* 216 */       String str3 = "domain=" + paramString1 + "&product=" + paramString2 + "&time=" + str2;
/* 217 */       String str4 = RSATool.generateSHA1withRSASigature(str3, paramString3);
/* 218 */       StringBuffer stringBuffer = new StringBuffer(str1);
/* 219 */       stringBuffer.append("?domain=")
/* 220 */         .append(paramString1)
/* 221 */         .append("&product=")
/* 222 */         .append(paramString2)
/* 223 */         .append("&time=")
/* 224 */         .append(str2)
/* 225 */         .append("&sign=")
/* 226 */         .append(str4);
/* 227 */       String str5 = HttpsUtil.httpsRequest(stringBuffer.toString(), "POST", null);
/* 228 */       newlog.error("测试网易邮箱结果：" + str5);
/* 229 */       jSONObject = JSONObject.parseObject(str5);
/* 230 */     } catch (Exception exception) {
/* 231 */       newlog.error("测试网易邮箱结果出错", exception);
/*     */     } 
/* 233 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Map<String, Object> checkUser(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/* 243 */     String str1 = paramString5 + "/unit/getAccountList";
/* 244 */     String str2 = System.currentTimeMillis() + "";
/* 245 */     String str3 = paramString1;
/* 246 */     if (str3.indexOf("@") > 0) {
/* 247 */       str3 = str3.substring(0, str3.indexOf("@"));
/*     */     }
/* 249 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 250 */     boolean bool = false;
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 255 */       String str4 = "account_name=" + str3 + "&domain=" + paramString2 + "&product=" + paramString3 + "&time=" + str2;
/* 256 */       String str5 = RSATool.generateSHA1withRSASigature(str4, paramString4);
/* 257 */       StringBuffer stringBuffer = new StringBuffer(str1);
/* 258 */       stringBuffer.append("?account_name=")
/* 259 */         .append(str3)
/* 260 */         .append("&domain=")
/* 261 */         .append(paramString2)
/* 262 */         .append("&product=")
/* 263 */         .append(paramString3)
/* 264 */         .append("&time=")
/* 265 */         .append(str2)
/* 266 */         .append("&sign=")
/* 267 */         .append(str5);
/* 268 */       String str6 = HttpsUtil.httpsRequest(stringBuffer.toString(), "POST", null);
/* 269 */       newlog.error("判断网易邮箱人员" + paramString1 + "是否存在返回结果：" + str6);
/* 270 */       JSONObject jSONObject = JSONObject.parseObject(str6);
/* 271 */       if (jSONObject.getJSONObject("con").getInteger("count").intValue() == 1) {
/* 272 */         bool = true;
/* 273 */         JSONObject jSONObject1 = (JSONObject)jSONObject.getJSONObject("con").getJSONArray("list").get(0);
/* 274 */         String str7 = jSONObject1.getString("unit_id");
/*     */         
/* 276 */         String str8 = jSONObject1.getString("status");
/*     */         
/* 278 */         hashMap.put("wyUserDept", str7);
/* 279 */         hashMap.put("status", str8);
/*     */       } 
/* 281 */       hashMap.put("checkUser", Boolean.valueOf(bool));
/* 282 */     } catch (Exception exception) {
/* 283 */       newlog.error("判断网易邮箱人员" + paramString1 + "是否存在出错", exception);
/*     */     } 
/* 285 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject createDepartment(String paramString1, String paramString2) {
/* 295 */     newlog.error("创建部门传参：" + paramString1);
/* 296 */     String str1 = paramString2 + "/unit/createUnit";
/* 297 */     String str2 = HttpsUtil.httpsRequest(str1 + "?" + paramString1, "POST", null);
/* 298 */     JSONObject jSONObject = JSONObject.parseObject(str2);
/* 299 */     newlog.error("创建部门返回数据：" + str2);
/* 300 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject updateDepartment(String paramString1, String paramString2) {
/* 311 */     newlog.error("更新部门传参：" + paramString1);
/* 312 */     String str1 = paramString2 + "/unit/updateUnit";
/* 313 */     String str2 = HttpsUtil.httpsRequest(str1 + "?" + paramString1, "POST", null);
/* 314 */     JSONObject jSONObject = JSONObject.parseObject(str2);
/* 315 */     newlog.error("更新部门返回数据：" + str2);
/* 316 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject updateDepartmentSupid(String paramString1, String paramString2) {
/* 325 */     newlog.error("更新部门上下级传参：" + paramString1);
/* 326 */     String str1 = paramString2 + "/unit/moveUnit";
/* 327 */     String str2 = HttpsUtil.httpsRequest(str1 + "?" + paramString1, "POST", null);
/* 328 */     JSONObject jSONObject = JSONObject.parseObject(str2);
/* 329 */     newlog.error("更新部门上下级返回数据：" + str2);
/* 330 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject deleteDepartment(String paramString1, String paramString2) {
/* 341 */     newlog.error("删除部门传参：" + paramString1);
/* 342 */     String str1 = paramString2 + "/unit/deleteUnit";
/* 343 */     String str2 = HttpsUtil.httpsRequest(str1 + "?" + paramString1, "POST", null);
/* 344 */     JSONObject jSONObject = JSONObject.parseObject(str2);
/* 345 */     newlog.error("删除部门返回数据：" + str2);
/* 346 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject createHrmresource(String paramString1, String paramString2) {
/* 357 */     newlog.error("创建人员传参：" + paramString1);
/* 358 */     String str1 = paramString2 + "/account/createAccount";
/* 359 */     String str2 = HttpsUtil.httpsRequest(str1 + "?" + paramString1, "POST", null);
/* 360 */     JSONObject jSONObject = JSONObject.parseObject(str2);
/* 361 */     newlog.error("创建人员返回数据：" + str2);
/* 362 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject updateHrmresource(String paramString1, String paramString2) {
/* 373 */     newlog.error("更新人员传参：" + paramString1);
/* 374 */     String str1 = paramString2 + "/account/updateAccount";
/* 375 */     String str2 = HttpsUtil.httpsRequest(str1 + "?" + paramString1, "POST", null);
/* 376 */     JSONObject jSONObject = JSONObject.parseObject(str2);
/* 377 */     newlog.error("更新人员返回数据：" + str2);
/* 378 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject updateHrmToDept(String paramString1, String paramString2) {
/* 385 */     newlog.error("更新人员所属部门传参：" + paramString1);
/* 386 */     String str1 = paramString2 + "/account/moveUnit";
/* 387 */     String str2 = HttpsUtil.httpsRequest(str1 + "?" + paramString1, "POST", null);
/* 388 */     JSONObject jSONObject = JSONObject.parseObject(str2);
/* 389 */     newlog.error("更新人员所属部门返回数据：" + str2);
/* 390 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject jyHrmresource(String paramString1, String paramString2) {
/* 400 */     newlog.error("冻结人员传参：" + paramString1);
/* 401 */     String str1 = paramString2 + "/account/suspendAccount";
/* 402 */     String str2 = HttpsUtil.httpsRequest(str1 + "?" + paramString1, "POST", null);
/* 403 */     JSONObject jSONObject = JSONObject.parseObject(str2);
/* 404 */     newlog.error("冻结人员返回数据：" + str2);
/* 405 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject hfHrmresource(String paramString1, String paramString2) {
/* 416 */     newlog.error("恢复人员传参：" + paramString1);
/* 417 */     String str1 = paramString2 + "/account/recoverAccount";
/* 418 */     String str2 = HttpsUtil.httpsRequest(str1 + "?" + paramString1, "POST", null);
/* 419 */     JSONObject jSONObject = JSONObject.parseObject(str2);
/* 420 */     newlog.error("恢复人员返回数据：" + str2);
/* 421 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject changeUserPsw(String paramString1, String paramString2) {
/* 433 */     newlog.error("更改人员密码传参：" + paramString1);
/* 434 */     String str1 = paramString2 + "/account/updatePassword";
/* 435 */     String str2 = HttpsUtil.httpsRequest(str1 + "?" + paramString1, "POST", null);
/* 436 */     JSONObject jSONObject = JSONObject.parseObject(str2);
/* 437 */     newlog.error("更改人员密码返回数据：" + str2);
/* 438 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject updateUnitRank(String paramString1, String paramString2) {
/* 450 */     newlog.error("设置部门排序传参：" + paramString1);
/* 451 */     String str1 = paramString2 + "/unit/updateUnitRank?";
/* 452 */     String str2 = HttpsUtil.httpsRequest(str1 + "?" + paramString1, "POST", null);
/* 453 */     JSONObject jSONObject = JSONObject.parseObject(str2);
/* 454 */     newlog.error("设置部门排序传参返回数据：" + str2);
/* 455 */     return jSONObject;
/*     */   }
/*     */ 
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/* 460 */     String str = "<EMAIL>";
/* 461 */     str = str.substring(0, str.indexOf("@"));
/* 462 */     System.out.println(str);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/wymail/api/WYMailApi.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */