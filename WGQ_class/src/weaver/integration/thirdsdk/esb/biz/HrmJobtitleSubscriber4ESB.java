/*    */ package weaver.integration.thirdsdk.esb.biz;
/*    */ 
/*    */ import com.alibaba.fastjson.JSON;
/*    */ import com.weaver.esb.client.EsbClient;
/*    */ import com.weaver.esb.spi.EsbService;
/*    */ import com.weaver.file.Prop;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import org.apache.commons.lang3.StringUtils;
/*    */ import weaver.general.Util;
/*    */ import weaver.integration.framework.data.field.FieldData;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmJobtitleSubscriber4ESB
/*    */ {
/* 31 */   private Logger log = LoggerFactory.getLogger(HrmJobtitleSubscriber4ESB.class);
/*    */ 
/*    */   
/*    */   public Object synData(List<FieldData> paramList, String paramString) {
/* 35 */     if (paramList != null) {
/*    */       
/* 37 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*    */       
/* 39 */       for (FieldData fieldData : paramList) {
/* 40 */         Object object = fieldData.getFieldValue();
/* 41 */         String str = fieldData.getFieldName();
/*    */         
/* 43 */         if (StringUtils.isNotEmpty(str)) {
/* 44 */           hashMap.put(str, Util.null2String(object));
/*    */         }
/*    */       } 
/*    */       
/* 48 */       this.log.info("action: " + paramString);
/* 49 */       String str1 = "";
/* 50 */       if ("insert".equalsIgnoreCase(paramString)) {
/* 51 */         str1 = "hrm_organization_newjobtitle_after";
/* 52 */       } else if ("update".equalsIgnoreCase(paramString)) {
/* 53 */         str1 = "hrm_organization_editjobtitle_after";
/* 54 */       } else if ("delete".equalsIgnoreCase(paramString)) {
/* 55 */         str1 = "hrm_organization_deletejobtitle_after";
/*    */       } else {
/* 57 */         this.log.error("无法识别action参数：" + paramString);
/* 58 */         return null;
/*    */       } 
/*    */ 
/*    */ 
/*    */       
/* 63 */       EsbService esbService = EsbClient.getService();
/* 64 */       Prop.getInstance(); String str2 = Prop.getPropValue("esbProxy", str1);
/* 65 */       String str3 = esbService.execute(str2, JSON.toJSONString(hashMap));
/* 66 */       this.log.info("execute esb event result: " + str3);
/*    */     } 
/* 68 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/esb/biz/HrmJobtitleSubscriber4ESB.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */