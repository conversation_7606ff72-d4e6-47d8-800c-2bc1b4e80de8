/*    */ package weaver.integration.thirdsdk.esb.biz;
/*    */ 
/*    */ import com.alibaba.fastjson.JSON;
/*    */ import com.weaver.esb.client.EsbClient;
/*    */ import com.weaver.esb.spi.EsbService;
/*    */ import com.weaver.file.Prop;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import org.apache.commons.lang3.StringUtils;
/*    */ import weaver.general.Util;
/*    */ import weaver.integration.framework.data.field.FieldData;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmResourceSubscriber4ESB
/*    */ {
/* 31 */   private Logger log = LoggerFactory.getLogger(HrmResourceSubscriber4ESB.class);
/*    */ 
/*    */   
/*    */   public Object synData(List<FieldData> paramList, String paramString) {
/* 35 */     if (paramList != null) {
/* 36 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*    */       
/* 38 */       for (FieldData fieldData : paramList) {
/* 39 */         Object object = fieldData.getFieldValue();
/* 40 */         String str = fieldData.getFieldName();
/*    */         
/* 42 */         if (StringUtils.isNotEmpty(str)) {
/* 43 */           hashMap.put(str, Util.null2String(object));
/*    */         }
/*    */       } 
/*    */       
/* 47 */       this.log.info("action: " + paramString);
/* 48 */       String str1 = "";
/* 49 */       if ("insert".equalsIgnoreCase(paramString)) {
/* 50 */         str1 = "hrm_organization_newresource_after";
/* 51 */       } else if ("update".equalsIgnoreCase(paramString)) {
/* 52 */         str1 = "hrm_organization_editresource_after";
/* 53 */       } else if ("delete".equalsIgnoreCase(paramString)) {
/* 54 */         str1 = "hrm_organization_deleteresource_after";
/*    */       } else {
/* 56 */         this.log.error("无法识别action参数：" + paramString);
/* 57 */         return null;
/*    */       } 
/*    */ 
/*    */ 
/*    */       
/* 62 */       EsbService esbService = EsbClient.getService();
/* 63 */       Prop.getInstance(); String str2 = Prop.getPropValue("esbProxy", str1);
/* 64 */       String str3 = esbService.execute(str2, JSON.toJSONString(hashMap));
/* 65 */       this.log.info("execute esb event result: " + str3);
/*    */     } 
/* 67 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/esb/biz/HrmResourceSubscriber4ESB.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */