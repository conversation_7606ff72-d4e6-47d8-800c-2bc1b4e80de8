/*    */ package weaver.integration.thirdsdk.esb.biz;
/*    */ 
/*    */ import com.alibaba.fastjson.JSON;
/*    */ import com.weaver.esb.client.EsbClient;
/*    */ import com.weaver.esb.spi.EsbService;
/*    */ import com.weaver.file.Prop;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import org.apache.commons.lang3.StringUtils;
/*    */ import weaver.general.Util;
/*    */ import weaver.integration.framework.data.field.FieldData;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmSubcompanySubscriber4ESB
/*    */ {
/* 31 */   private Logger log = LoggerFactory.getLogger(HrmSubcompanySubscriber4ESB.class);
/*    */ 
/*    */   
/*    */   public Object synData(List<FieldData> paramList, String paramString) {
/* 35 */     if (paramList != null) {
/*    */       
/* 37 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 38 */       for (FieldData fieldData : paramList) {
/* 39 */         Object object = fieldData.getFieldValue();
/* 40 */         String str = fieldData.getFieldName();
/* 41 */         if (StringUtils.isNotEmpty(str)) {
/* 42 */           hashMap.put(str, Util.null2String(object));
/*    */         }
/*    */       } 
/*    */       
/* 46 */       this.log.info("action: " + paramString);
/* 47 */       String str1 = "";
/* 48 */       if ("insert".equalsIgnoreCase(paramString)) {
/* 49 */         str1 = "hrm_organization_newsubcompany_after";
/* 50 */       } else if ("update".equalsIgnoreCase(paramString)) {
/* 51 */         str1 = "hrm_organization_editsubcompany_after";
/* 52 */       } else if ("delete".equalsIgnoreCase(paramString)) {
/* 53 */         str1 = "hrm_organization_deletesubcompany_after";
/*    */       } else {
/* 55 */         this.log.error("无法识别action参数：" + paramString);
/* 56 */         return null;
/*    */       } 
/*    */ 
/*    */ 
/*    */       
/* 61 */       EsbService esbService = EsbClient.getService();
/* 62 */       Prop.getInstance(); String str2 = Prop.getPropValue("esbProxy", str1);
/* 63 */       String str3 = esbService.execute(str2, JSON.toJSONString(hashMap));
/* 64 */       this.log.info("execute esb event result: " + str3);
/*    */     } 
/* 66 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/esb/biz/HrmSubcompanySubscriber4ESB.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */