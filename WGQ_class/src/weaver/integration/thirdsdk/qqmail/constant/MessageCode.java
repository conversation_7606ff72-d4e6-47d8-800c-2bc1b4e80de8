/*     */ package weaver.integration.thirdsdk.qqmail.constant;
/*     */ 
/*     */ import java.util.Map;
/*     */ import java.util.concurrent.ConcurrentHashMap;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MessageCode
/*     */ {
/*  13 */   public static Map<String, String> RETURN_CODE = new ConcurrentHashMap<>();
/*     */   
/*     */   static {
/*  16 */     RETURN_CODE.put("-1", SystemEnv.getHtmlLabelName(524359, 7));
/*  17 */     RETURN_CODE.put("0", SystemEnv.getHtmlLabelName(524360, 7));
/*  18 */     RETURN_CODE.put("40001", SystemEnv.getHtmlLabelName(524361, 7));
/*  19 */     RETURN_CODE.put("40003", SystemEnv.getHtmlLabelName(524362, 7));
/*  20 */     RETURN_CODE.put("40013", SystemEnv.getHtmlLabelName(524363, 7));
/*  21 */     RETURN_CODE.put("40014", SystemEnv.getHtmlLabelName(524364, 7));
/*  22 */     RETURN_CODE.put("40057", SystemEnv.getHtmlLabelName(524365, 7));
/*  23 */     RETURN_CODE.put("40091", SystemEnv.getHtmlLabelName(524366, 7));
/*  24 */     RETURN_CODE.put("45009", SystemEnv.getHtmlLabelName(524367, 7));
/*  25 */     RETURN_CODE.put("45024", SystemEnv.getHtmlLabelName(524368, 7));
/*  26 */     RETURN_CODE.put("50005", SystemEnv.getHtmlLabelName(524369, 7));
/*  27 */     RETURN_CODE.put("60001", SystemEnv.getHtmlLabelName(524370, 7));
/*  28 */     RETURN_CODE.put("60002", SystemEnv.getHtmlLabelName(524371, 7));
/*  29 */     RETURN_CODE.put("60003", SystemEnv.getHtmlLabelName(21159, 7));
/*  30 */     RETURN_CODE.put("60004", SystemEnv.getHtmlLabelName(524373, 7));
/*  31 */     RETURN_CODE.put("60005", SystemEnv.getHtmlLabelName(524374, 7));
/*  32 */     RETURN_CODE.put("60006", SystemEnv.getHtmlLabelName(524375, 7));
/*  33 */     RETURN_CODE.put("60007", SystemEnv.getHtmlLabelName(524376, 7));
/*  34 */     RETURN_CODE.put("60008", SystemEnv.getHtmlLabelName(524377, 7));
/*  35 */     RETURN_CODE.put("60009", SystemEnv.getHtmlLabelName(524378, 7));
/*  36 */     RETURN_CODE.put("60010", SystemEnv.getHtmlLabelName(524379, 7));
/*  37 */     RETURN_CODE.put("60102", SystemEnv.getHtmlLabelName(524380, 7));
/*  38 */     RETURN_CODE.put("60103", SystemEnv.getHtmlLabelName(524381, 7));
/*  39 */     RETURN_CODE.put("60111", SystemEnv.getHtmlLabelName(524382, 7));
/*  40 */     RETURN_CODE.put("60112", SystemEnv.getHtmlLabelName(524383, 7));
/*  41 */     RETURN_CODE.put("60114", SystemEnv.getHtmlLabelName(524384, 7));
/*  42 */     RETURN_CODE.put("60123", SystemEnv.getHtmlLabelName(524385, 7));
/*  43 */     RETURN_CODE.put("60124", SystemEnv.getHtmlLabelName(524386, 7));
/*  44 */     RETURN_CODE.put("60125", SystemEnv.getHtmlLabelName(524387, 7));
/*  45 */     RETURN_CODE.put("60126", SystemEnv.getHtmlLabelName(524388, 7));
/*  46 */     RETURN_CODE.put("60127", SystemEnv.getHtmlLabelName(524389, 7));
/*  47 */     RETURN_CODE.put("600001", SystemEnv.getHtmlLabelName(524390, 7));
/*  48 */     RETURN_CODE.put("600002", SystemEnv.getHtmlLabelName(524391, 7));
/*  49 */     RETURN_CODE.put("600003", SystemEnv.getHtmlLabelName(524392, 7));
/*  50 */     RETURN_CODE.put("600004", SystemEnv.getHtmlLabelName(524393, 7));
/*  51 */     RETURN_CODE.put("600005", SystemEnv.getHtmlLabelName(524394, 7));
/*  52 */     RETURN_CODE.put("600006", SystemEnv.getHtmlLabelName(524395, 7));
/*  53 */     RETURN_CODE.put("600007", SystemEnv.getHtmlLabelName(524396, 7));
/*  54 */     RETURN_CODE.put("600008", SystemEnv.getHtmlLabelName(524397, 7));
/*  55 */     RETURN_CODE.put("600009", SystemEnv.getHtmlLabelName(524398, 7));
/*  56 */     RETURN_CODE.put("600010", SystemEnv.getHtmlLabelName(524399, 7));
/*  57 */     RETURN_CODE.put("600011", SystemEnv.getHtmlLabelName(524400, 7));
/*  58 */     RETURN_CODE.put("600012", SystemEnv.getHtmlLabelName(524401, 7));
/*  59 */     RETURN_CODE.put("600013", SystemEnv.getHtmlLabelName(524402, 7));
/*  60 */     RETURN_CODE.put("600014", SystemEnv.getHtmlLabelName(524403, 7));
/*  61 */     RETURN_CODE.put("600015", SystemEnv.getHtmlLabelName(524404, 7));
/*  62 */     RETURN_CODE.put("600016", SystemEnv.getHtmlLabelName(524405, 7));
/*  63 */     RETURN_CODE.put("600017", SystemEnv.getHtmlLabelName(524406, 7));
/*  64 */     RETURN_CODE.put("600018", SystemEnv.getHtmlLabelName(524407, 7));
/*  65 */     RETURN_CODE.put("600019", SystemEnv.getHtmlLabelName(524408, 7));
/*  66 */     RETURN_CODE.put("600020", SystemEnv.getHtmlLabelName(524409, 7));
/*  67 */     RETURN_CODE.put("600021", SystemEnv.getHtmlLabelName(524410, 7));
/*  68 */     RETURN_CODE.put("600023", SystemEnv.getHtmlLabelName(524411, 7));
/*  69 */     RETURN_CODE.put("600024", SystemEnv.getHtmlLabelName(524412, 7));
/*  70 */     RETURN_CODE.put("600025", SystemEnv.getHtmlLabelName(524413, 7));
/*  71 */     RETURN_CODE.put("600026", SystemEnv.getHtmlLabelName(524414, 7));
/*  72 */     RETURN_CODE.put("600027", SystemEnv.getHtmlLabelName(524415, 7));
/*  73 */     RETURN_CODE.put("601001", SystemEnv.getHtmlLabelName(524416, 7));
/*  74 */     RETURN_CODE.put("601002", SystemEnv.getHtmlLabelName(524417, 7));
/*  75 */     RETURN_CODE.put("601003", SystemEnv.getHtmlLabelName(524418, 7));
/*  76 */     RETURN_CODE.put("601004", SystemEnv.getHtmlLabelName(524419, 7));
/*  77 */     RETURN_CODE.put("602005", SystemEnv.getHtmlLabelName(524420, 7));
/*     */ 
/*     */     
/*  80 */     RETURN_CODE.put("100", SystemEnv.getHtmlLabelName(524360, 7));
/*  81 */     RETURN_CODE.put("201", "SYS_REQ_SERVICE_PROHIBITED");
/*  82 */     RETURN_CODE.put("202", "SYS_REQ_SERVICE_NOT_FOUND");
/*  83 */     RETURN_CODE.put("203", "SYS_REQ_METHOD_NOT_FOUND");
/*  84 */     RETURN_CODE.put("204", "SYS_REQ_METHOD_INVALID_DEFINITION");
/*  85 */     RETURN_CODE.put("206", "SYS_REQ_PARAM_INVALID_JSON");
/*  86 */     RETURN_CODE.put("207", "SYS_REQ_PARAM_INVALID");
/*  87 */     RETURN_CODE.put("208", "SYS_REQ_PARAM_STRING_LENGTH_OVERFLOW");
/*  88 */     RETURN_CODE.put("209", "SYS_UNKNOWN_ERROR");
/*  89 */     RETURN_CODE.put("210", "SYS_DATA_ERROR");
/*  90 */     RETURN_CODE.put("211", "SYS_REQ_TIMEOUT");
/*  91 */     RETURN_CODE.put("301", "SYS_HTTP_INVALID_METHOD");
/*  92 */     RETURN_CODE.put("302", "SYS_HTTP_POSTSIZE_OVERFLOW");
/*  93 */     RETURN_CODE.put("303", "SYS_HTTP_HTTPS_ONLY");
/*  94 */     RETURN_CODE.put("304", "SYS_HTTP_INVALID_URL");
/*  95 */     RETURN_CODE.put("407", "SYS_REQ_ACCESSTARGET_INVALID");
/*  96 */     RETURN_CODE.put("408", "SYS_REQ_ACCESSTOKEN_INVALID");
/*  97 */     RETURN_CODE.put("501", "SYS_RESP_INVALID_JSON");
/*  98 */     RETURN_CODE.put("505", "SYS_RESP_LIST_LENGTH_OVERFLOW");
/*  99 */     RETURN_CODE.put("1100", "APP_ANTIVIRUS_ATTACHMENT_NOT_EXIST");
/* 100 */     RETURN_CODE.put("1101", "APP_ANTIVIRUS_MESSAGE_NOT_EXIST");
/* 101 */     RETURN_CODE.put("1202", "APP_MBOX_FILTER_LIST_FULL");
/* 102 */     RETURN_CODE.put("1204", "APP_MBOX_FILTER_NOT_EXIST");
/* 103 */     RETURN_CODE.put("1205", "APP_MBOX_FILTER_OFFSET_OUT_OF_RANGE");
/* 104 */     RETURN_CODE.put("1206", "APP_MBOX_FILTER_FOLDER_ID_NOT_EXIST");
/* 105 */     RETURN_CODE.put("1207", "APP_MBOX_FILTER_AUTOREPLY_NOTEXIST");
/* 106 */     RETURN_CODE.put("1208", "APP_MBOX_FILTER_AUTOFORWARD_NOTEXIST");
/* 107 */     RETURN_CODE.put("1209", "APP_MBOX_FILTER_AUTOFORWARD_LISTFULL");
/* 108 */     RETURN_CODE.put("1300", "APP_MBOX_FOLDER_NAME_EXIST");
/* 109 */     RETURN_CODE.put("1301", "APP_MBOX_FOLDER_CANNT_BE_REMOVED");
/* 110 */     RETURN_CODE.put("1302", "APP_MBOX_FOLDER_FULL");
/* 111 */     RETURN_CODE.put("1303", "APP_MBOX_FOLDER_ID_NOT_EXIST");
/* 112 */     RETURN_CODE.put("1304", "APP_MBOX_FOLDER_NOT_EMPTY");
/* 113 */     RETURN_CODE.put("1305", "APP_MBOX_FOLDER_HAS_EMPTY");
/* 114 */     RETURN_CODE.put("1306", "APP_MBOX_PARENT_FOLDER_NOT_EXISTS");
/* 115 */     RETURN_CODE.put("1307", "APP_MBOX_ FOLDER_LAYER_LIMIT");
/* 116 */     RETURN_CODE.put("1400", "APP_MBOX_RECYCLE_MAIL_ADMIN_ERROR");
/* 117 */     RETURN_CODE.put("1401", "APP_MBOX_MAIL_EXCEED_LIMITATION");
/* 118 */     RETURN_CODE.put("1402", "APP_MBOX_MAIL_EXTEND_KEY_FULL");
/* 119 */     RETURN_CODE.put("1407", "APP_MBOX_ MAIL_ID_NOT_EXIST");
/* 120 */     RETURN_CODE.put("1408", "APP_MBOX_MAIL_MAILBOX_FULL");
/* 121 */     RETURN_CODE.put("1410", "APP_MBOX_MAIL_RECIPIENT_INVALID");
/* 122 */     RETURN_CODE.put("1411", "APP_MBOX_RECYCLE_MAIL_ID_NOT_EXIST");
/* 123 */     RETURN_CODE.put("1412", "APP_MBOX_MAIL_NOT_IN_SEND_OUT");
/* 124 */     RETURN_CODE.put("1413", "APP_MBOX_MAIL_ATTACH_TOO_MANY");
/* 125 */     RETURN_CODE.put("1414", "APP_MBOX_MAIL_LOAD_ATTACH_ERROR");
/* 126 */     RETURN_CODE.put("1415", "APP_MBOX_MAIL_IP_IS_SPAM");
/* 127 */     RETURN_CODE.put("1416", "APP_MBOX_MAIL_IS_NOT_DRAFT");
/* 128 */     RETURN_CODE.put("1417", "APP_MBOX_MAIL_SEARCH_SYSTEM_BUSY");
/* 129 */     RETURN_CODE.put("1418", "APP_MBOX_MAIL_ATTACH_EXCEED_LIMITATION");
/* 130 */     RETURN_CODE.put("1419", "APP_MBOX_MAIL_CONTENT_TO_LARGE");
/* 131 */     RETURN_CODE.put("1420", "APP_MBOX_MAIL_FROM_INVALID");
/* 132 */     RETURN_CODE.put("1421", "APP_MBOX_MAIL_TRANSACTION_BUSY");
/* 133 */     RETURN_CODE.put("1500", "APP_MBOX_SETTING_SIGNATURE_ID_NOT_EXIST");
/* 134 */     RETURN_CODE.put("1501", "APP_MBOX_SETTING_SIGNATURE_FULL");
/* 135 */     RETURN_CODE.put("1502", "APP_MBOX_SETTING_NO_ACTIVE_SIGNATURE");
/* 136 */     RETURN_CODE.put("1600", "APP_UD_ACCOUNT_NUMBER_OVERFLOW");
/* 137 */     RETURN_CODE.put("1601", "APP_UD_ACCOUNT_EMAIL_EXIST");
/* 138 */     RETURN_CODE.put("1602", "APP_UD_ACCOUNT_NOT_EXIST");
/* 139 */     RETURN_CODE.put("1603", "APP_UD_ACCOUNT_EXTEND_FIELDS_OVERFLOW");
/* 140 */     RETURN_CODE.put("1604", "APP_UD_DOMAIN_NOT_EXIST");
/* 141 */     RETURN_CODE.put("1605", "APP_UD_ACCOUNT_NOT_ACTIVE");
/* 142 */     RETURN_CODE.put("1606", "APP_UD_DOMAIN_DUPLICATE");
/* 143 */     RETURN_CODE.put("1607", "APP_UD_ACCOUNT_OPERATOR_NOT_EXIST");
/* 144 */     RETURN_CODE.put("1608", "APP_UD_ACCOUNT_OUT_OF_DOMAIN");
/* 145 */     RETURN_CODE.put("1609", "APP_UD_DOMAIN_FREEZE");
/* 146 */     RETURN_CODE.put("1700", "APP_UD_CONTACT_GROUP_ID_NOT_EXIST");
/* 147 */     RETURN_CODE.put("1701", "APP_UD_CONTACT_GROUP_NAME_EXIST");
/* 148 */     RETURN_CODE.put("1702", "APP_UD_CONTACT_EMAIL_EXIST");
/* 149 */     RETURN_CODE.put("1703", "APP_UD_CONTACT_ID_NOT_EXIST");
/* 150 */     RETURN_CODE.put("1704", "APP_UD_CONTACT_EXTEND_FIELDS_OVERFLOW");
/* 151 */     RETURN_CODE.put("1705", "APP_UD_CONTACT_NUMBER_OVERFLOW");
/* 152 */     RETURN_CODE.put("1706", "APP_UD_CONTACT_GROUP_NUMBER_OVERFLOW");
/* 153 */     RETURN_CODE.put("1707", "APP_UD_CONTACT_GROUP_SYSTEM_DELETE_ERROR");
/* 154 */     RETURN_CODE.put("1708", "APP_UD_CONTACT_DELETE_NOT_EMPTY_GROUP_ERROR");
/* 155 */     RETURN_CODE.put("1709", "APP_UD_CONTACT_OFFSET_OVERFLOW");
/* 156 */     RETURN_CODE.put("1710", "APP_UD_CONTACT_SYSTEM_GROUP_CHANGE_ERROR");
/* 157 */     RETURN_CODE.put("1800", "APP_UD_DEPARTMENTS_OVERFLOW");
/* 158 */     RETURN_CODE.put("1801", "APP_UD_DEPARTMENT_NOT_EMPTY");
/* 159 */     RETURN_CODE.put("1802", "APP_UD_DEPARTMENT_PARENT_ID_NOT_EXIST");
/* 160 */     RETURN_CODE.put("1803", "APP_UD_DEPARTMENT_NAME_EXIST");
/* 161 */     RETURN_CODE.put("1804", "APP_UD_DEPARTMENT_ID_NOT_EXIST");
/* 162 */     RETURN_CODE.put("1805", "APP_UD_DEPARTMENT_ACCOUNT_NOT_EXIST");
/* 163 */     RETURN_CODE.put("1806", "APP_UD_DEPARTMENT_HAS_SUB_DEPT");
/* 164 */     RETURN_CODE.put("1900", "APP_NETFS_TARGET_EXIST");
/* 165 */     RETURN_CODE.put("1901", "APP_NETFS_TARGET_NOT_EXIST");
/* 166 */     RETURN_CODE.put("1902", "APP_NETFS_PERMISSION_DENIED");
/* 167 */     RETURN_CODE.put("1903", "APP_NETFS_USER_NOT_EXIST");
/* 168 */     RETURN_CODE.put("1904", "APP_NETFS_EXCEED_LIMITATION");
/* 169 */     RETURN_CODE.put("1905", "APP_NETFS_NAME_EXIST");
/* 170 */     RETURN_CODE.put("1906", "APP_NETFS_NODEID_NOT_EXIST");
/* 171 */     RETURN_CODE.put("1907", "APP_NETFS_EXCEED_CHILDREN_NUMBER");
/* 172 */     RETURN_CODE.put("1908", "APP_NETFS_DIRECTORY_NOT_EMPTY");
/* 173 */     RETURN_CODE.put("1909", "APP_NETFS_NODEID_NOT_DIRECTORY");
/* 174 */     RETURN_CODE.put("1910", "APP_NETFS_NODEID_NOT_FILE");
/* 175 */     RETURN_CODE.put("1911", "APP_NETFS_NODEID_IS_ROOT");
/* 176 */     RETURN_CODE.put("1912", "APP_NETFS_CANNOT_MOVETO_DESCEND");
/* 177 */     RETURN_CODE.put("1913", "APP_NETFS_PATH_NOT_EXIST");
/* 178 */     RETURN_CODE.put("1914", "APP_NETFS_SHAREDLIST_EXCEED");
/* 179 */     RETURN_CODE.put("1915", "APP_NETFS_MYSHAREDFILES_EXCEED");
/* 180 */     RETURN_CODE.put("1916", "APP_NETFS_SHARED_USER_EXIST");
/* 181 */     RETURN_CODE.put("1917", "APP_NETFS_SHARED_ALIAS_ACCOUNT");
/* 182 */     RETURN_CODE.put("1918", "APP_NETFS_SHARED_FOREIGN_ACCOUNT");
/* 183 */     RETURN_CODE.put("1919", "APP_NETFS_SHARED_MAILGROUP_ACCOUNT");
/* 184 */     RETURN_CODE.put("1920", "APP_NETFS_SHARED_IS_SELF");
/* 185 */     RETURN_CODE.put("1921", "APP_NETFS_ SHAREDFILES2ME_EXCEED");
/* 186 */     RETURN_CODE.put("2000", "APP_YUNSTORE_TABLE_NOT_EXIST");
/* 187 */     RETURN_CODE.put("2001", "APP_YUNSTORE_TABLE_EXIST");
/* 188 */     RETURN_CODE.put("2002", "APP_YUNSTORE_TABLE_RECORD_NOT_EXIST");
/* 189 */     RETURN_CODE.put("2003", "APP_YUNSTORE_DUPICATE_ID");
/* 190 */     RETURN_CODE.put("2100", "APP_UD_ALIAS_ACCOUNT_ALIAS_ALREADY_BIND");
/* 191 */     RETURN_CODE.put("2101", "APP_UD_ALIAS_ACCOUNT_NOT_EXIST");
/* 192 */     RETURN_CODE.put("2102", "APP_UD_ALIAS_ACCOUNT_ALIAS_NOT_BIND");
/* 193 */     RETURN_CODE.put("2103", "APP_UD_ALIAS_ACCOUNT_OUT_OF_DOMAIN");
/* 194 */     RETURN_CODE.put("2104", "APP_UD_ALIAS_DOMAIN_ALIAS_OUT_OF_RANGE");
/* 195 */     RETURN_CODE.put("2105", "APP_UD_ALIAS_DOMAIN_ALIAS_ALREADY_EXIST");
/* 196 */     RETURN_CODE.put("2106", "APP_UD_ALIAS_DOMAIN_ALIAS_ILLEGAL");
/* 197 */     RETURN_CODE.put("2107", "APP_UD_ALIAS_ACCOUNT_ALIAS_OUT_OF_RANGE");
/* 198 */     RETURN_CODE.put("2108", "APP_UD_ALIAS_ACCOUNT_ALIAS_ILLEGAL");
/* 199 */     RETURN_CODE.put("2109", "APP_UD_ALIAS_DOMAIN_NOT_EXIST");
/* 200 */     RETURN_CODE.put("2110", "APP_UD_ALIAS_DOMAIN_ALIAS_EXIST");
/* 201 */     RETURN_CODE.put("2200", "APP_MBOX_TAG_ID_NOT_EXSIT");
/* 202 */     RETURN_CODE.put("2201", "APP_MBOX_TAG_EXISTS");
/* 203 */     RETURN_CODE.put("2202", "APP_MBOX_TAG_SYSTEM_TAG_CAN_NOT_DELETED");
/* 204 */     RETURN_CODE.put("2203", "APP_MBOX_TAG_PARENT_ID_NOT_EXIST");
/* 205 */     RETURN_CODE.put("2204", "APP_MBOX_TAG_TO_MANY");
/* 206 */     RETURN_CODE.put("2300", "APP_UD_MAILGROUP_DOMAIN_NOT_EXIST");
/* 207 */     RETURN_CODE.put("2301", "APP_UD_MAILGROUP_ALREADY_EXIST");
/* 208 */     RETURN_CODE.put("2302", "APP_UD_MAILGROUP_NOT_EXIST");
/* 209 */     RETURN_CODE.put("2303", "APP_UD_MAILGROUP_ MEMBER _DUPLICATE");
/* 210 */     RETURN_CODE.put("2304", "APP_UD_MAILGROUP_ MEMBER _NOT_EXIST");
/* 211 */     RETURN_CODE.put("2305", "APP_UD_MAILGROUP_MEMBER_OVERFLOW");
/* 212 */     RETURN_CODE.put("2306", "APP_UD_MAILGROUP_OVERFLOW");
/* 213 */     RETURN_CODE.put("2307", "APP_UD_MAILGROUP_WHITELIST_OVERFLOW");
/* 214 */     RETURN_CODE.put("2308", "APP_UD_MAILGROUP_ILLEGAL_STATUS");
/* 215 */     RETURN_CODE.put("2309", "APP_UD_MAILGROUP_WHITELIST_MEMBER_NOT_EXIST");
/* 216 */     RETURN_CODE.put("2400", "APP_MBOX_SESSION_ID_NOT_EXSIT");
/* 217 */     RETURN_CODE.put("2500", "APP_MBOX_POP_REACH_MAX_LIMIT");
/* 218 */     RETURN_CODE.put("2501", "APP_MBOX_POP_ACCOUNT_NOT_EXIST");
/* 219 */     RETURN_CODE.put("2502", "APP_MBOX_POP_ACCOUNT_EXISTS");
/* 220 */     RETURN_CODE.put("2503", "APP_MBOX_POP_ACCOUNT_AUTH_FAILED");
/* 221 */     RETURN_CODE.put("2504", "APP_MBOX_POP_SERVER_INVALID");
/* 222 */     RETURN_CODE.put("2505", "APP_MBOX_POP_PROCESSING");
/* 223 */     RETURN_CODE.put("2506", "APP_MBOX_POP_ADD_ACCOUNT_TIMEOUT");
/* 224 */     RETURN_CODE.put("2507", "APP_MBOX_POP_TASK_EXCEED_TIME");
/* 225 */     RETURN_CODE.put("2508", "APP_MBOX_POP_CONNECT_OVERFLOW_TOTAL");
/* 226 */     RETURN_CODE.put("2509", "APP_MBOX_POP_CONNECT_OVERFLOW_DOMAIN");
/* 227 */     RETURN_CODE.put("2510", "APP_MBOX_POP_CONNECT_OVERFLOW_ACCOUNT");
/* 228 */     RETURN_CODE.put("2511", "APP_MBOX_POP_RECEIEVAL_TIMEOUT");
/* 229 */     RETURN_CODE.put("2512", "APP_MBOX_POP_ACCOUNT_ILLEGAL");
/* 230 */     RETURN_CODE.put("2513", "APP_MBOX_POP_ACCOUNT_RETRIEVALING");
/* 231 */     RETURN_CODE.put("2514", "APP_MBOX_POP_ACCOUNT_PASSWORD_IS_NULL");
/* 232 */     RETURN_CODE.put("2515", "APP_MBOX_POP_ACCOUNT_NOT_AUTH");
/* 233 */     RETURN_CODE.put("2516", "APP_MBOX_POP_RECEIEVAL_SYS_BUSY");
/* 234 */     RETURN_CODE.put("2601", "APP_UD_DOMAIN_INIT_PROHIBITED");
/* 235 */     RETURN_CODE.put("2602", "APP_UD_DOMAIN_BLACKLIST_OUT_OF_RANGE");
/* 236 */     RETURN_CODE.put("2603", "APP_UD_DOMAIN_WHITELIST_OUT_OF_RANGE");
/* 237 */     RETURN_CODE.put("2700", "APP_MBOX_FILTER_MONITOR_ID_NOT_EXIST");
/* 238 */     RETURN_CODE.put("2701", "APP_MBOX_FILTER_MONITOR_HAS_ADDRESS");
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/qqmail/constant/MessageCode.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */