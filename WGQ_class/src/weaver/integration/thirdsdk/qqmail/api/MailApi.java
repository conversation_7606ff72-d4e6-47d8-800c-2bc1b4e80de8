/*     */ package weaver.integration.thirdsdk.qqmail.api;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.engine.integration.util.HttpsUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MailApi
/*     */ {
/*  16 */   private static Logger logger = LoggerFactory.getLogger(MailApi.class); public static final String GET_ACCESS_TOKEN = "https://api.exmail.qq.com/cgi-bin/gettoken?corpid=id&corpsecret=secrect"; public static final String POST_CREATE_DEPARTMENT = "https://api.exmail.qq.com/cgi-bin/department/create?access_token=ACCESS_TOKEN"; public static final String POST_UPDATE_DEPARTMENT = "https://api.exmail.qq.com/cgi-bin/department/update?access_token=ACCESS_TOKEN"; public static final String GET_DELETE_DEPARTMENT = "https://api.exmail.qq.com/cgi-bin/department/delete?access_token=ACCESS_TOKEN&id=ID"; public static final String GET_LIST_DEPARTMENT = "https://api.exmail.qq.com/cgi-bin/department/list?access_token=ACCESS_TOKEN&id=ID"; public static final String POST_SEARCH_DEPARTMENT = "https://api.exmail.qq.com/cgi-bin/department/search?access_token=ACCESS_TOKEN"; public static final String POST_CREATE_USER = "https://api.exmail.qq.com/cgi-bin/user/create?access_token=ACCESS_TOKEN";
/*     */   public static final String POST_UPDATE_USER = "https://api.exmail.qq.com/cgi-bin/user/update?access_token=ACCESS_TOKEN";
/*     */   public static final String GET_DELETE_USER = "https://api.exmail.qq.com/cgi-bin/user/delete?access_token=ACCESS_TOKEN&userid=USERID";
/*     */   public static final String GET_GET_USER = "https://api.exmail.qq.com/cgi-bin/user/get?access_token=ACCESS_TOKEN&userid=USERID";
/*     */   public static final String GET_SIMPLE_LIST_USER = "https://api.exmail.qq.com/cgi-bin/user/simplelist?access_token=ACCESS_TOKEN&department_id=DEPARTMENT_ID&fetch_child=FETCH_CHILD";
/*     */   public static final String GET_LIST_USER = "https://api.exmail.qq.com/cgi-bin/user/list?access_token=ACCESS_TOKEN&department_id=DEPARTMENT_ID&fetch_child=FETCH_CHILD";
/*     */   public static final String POST_BATCH_CHECK = "https://api.exmail.qq.com/cgi-bin/user/batchcheck?access_token=ACCESS_TOKEN";
/*     */   
/*     */   public static boolean emailAvailable(String paramString) {
/*  25 */     if (!"".equals(Util.null2String(paramString))) {
/*  26 */       RecordSet recordSet = new RecordSet();
/*  27 */       recordSet.executeQuery("SELECT * FROM QQMailSetting WHERE isuse='1'", new Object[0]);
/*  28 */       if (recordSet.next()) {
/*  29 */         String str1 = recordSet.getString("corpid");
/*  30 */         String str2 = recordSet.getString("corpsecret");
/*  31 */         JSONObject jSONObject = getAccessToken(str1, str2);
/*  32 */         String str3 = "";
/*  33 */         if (jSONObject.containsKey("access_token")) {
/*  34 */           str3 = jSONObject.getString("access_token");
/*  35 */           JSONObject jSONObject1 = getUser(str3, paramString);
/*  36 */           if (jSONObject1 != null && "0".equals(jSONObject1.getString("errcode"))) {
/*  37 */             return false;
/*     */           }
/*  39 */           return true;
/*     */         } 
/*  41 */         logger.error("=======================腾讯邮箱接口获取token失败!!!");
/*  42 */         return false;
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/*  47 */     return false;
/*     */   }
/*     */   public static final String POST_CREATE_GROUP = "https://api.exmail.qq.com/cgi-bin/group/create?access_token=ACCESS_TOKEN"; public static final String POST_UPDATE_GROUP = "https://api.exmail.qq.com/cgi-bin/group/update?access_token=ACCESS_TOKEN"; public static final String GET_DELETE_GROUP = "https://api.exmail.qq.com/cgi-bin/group/delete?access_token=ACCESS_TOKEN&groupid=ID"; public static final String GET_GET_GROUP = "https://api.exmail.qq.com/cgi-bin/group/get?access_token=ACCESS_TOKEN&groupid=ID";
/*     */   public static final String POST_GET_FUNC = "https://api.exmail.qq.com/cgi-bin/useroption/get?access_token=ACCESS_TOKEN";
/*     */   public static final String POST_UPDATE_FUNC = "https://api.exmail.qq.com/cgi-bin/useroption/update?access_token=ACCESS_TOKEN";
/*     */   
/*     */   public static boolean isOpenTencentMail() {
/*  54 */     RecordSet recordSet = new RecordSet();
/*  55 */     recordSet.executeQuery("SELECT * FROM QQMailSetting WHERE isuse='1'", new Object[0]);
/*  56 */     if (recordSet.next()) {
/*  57 */       return true;
/*     */     }
/*  59 */     return false;
/*     */   }
/*     */   public static final String POST_MAIL_STATUS = "https://api.exmail.qq.com/cgi-bin/log/mailstatus?access_token=ACCESS_TOKEN"; public static final String POST_SEARCH_MAIL = "https://api.exmail.qq.com/cgi-bin/log/mail?access_token=ACCESS_TOKEN"; public static final String POST_SEARCH_LOGIN = "https://api.exmail.qq.com/cgi-bin/log/login?access_token=ACCESS_TOKEN"; public static final String POST_BATCH_JOB = " https://api.exmail.qq.com/cgi-bin/log/batchjob?access_token=ACCESS_TOKEN"; public static final String POST_SEARCH_OPEARATION = "https://api.exmail.qq.com/cgi-bin/log/operation?access_token=ACCESS_TOKEN"; public static final String GET_NEW_COUNT = "https://api.exmail.qq.com/cgi-bin/mail/newcount?access_token=ACCESS_TOKEN&userid=USERID"; public static final String GET_GET_LOGIN_URL = "https://api.exmail.qq.com/cgi-bin/service/get_login_url?access_token=ACCESS_TOKEN&userid=USERID";
/*     */   public static JSONObject getAccessToken(String paramString1, String paramString2) {
/*  63 */     String str1 = "https://api.exmail.qq.com/cgi-bin/gettoken?corpid=id&corpsecret=secrect".replace("=id", "=" + paramString1).replace("=secrect", "=" + paramString2);
/*  64 */     String str2 = HttpsUtil.httpsRequest(str1, "GET", null);
/*  65 */     return JSONObject.parseObject(str2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject createDepartment(String paramString1, String paramString2) {
/*  77 */     logger.info("================MAILAPI 创建部门参数:" + paramString2);
/*  78 */     String str1 = "https://api.exmail.qq.com/cgi-bin/department/create?access_token=ACCESS_TOKEN".replace("=ACCESS_TOKEN", "=" + paramString1);
/*  79 */     String str2 = HttpsUtil.httpsRequest(str1, "POST", paramString2);
/*  80 */     return JSONObject.parseObject(str2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject updateDepartment(String paramString1, String paramString2) {
/*  93 */     String str1 = "https://api.exmail.qq.com/cgi-bin/department/update?access_token=ACCESS_TOKEN".replace("=ACCESS_TOKEN", "=" + paramString1);
/*  94 */     String str2 = HttpsUtil.httpsRequest(str1, "POST", paramString2);
/*  95 */     return JSONObject.parseObject(str2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject deleteDepartment(String paramString1, String paramString2) {
/* 104 */     String str1 = "https://api.exmail.qq.com/cgi-bin/department/delete?access_token=ACCESS_TOKEN&id=ID".replace("=ACCESS_TOKEN", "=" + paramString1).replace("=ID", "=" + paramString2);
/* 105 */     String str2 = HttpsUtil.httpsRequest(str1, "GET", null);
/* 106 */     return JSONObject.parseObject(str2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject listDepartment(String paramString1, String paramString2) {
/* 115 */     String str1 = "https://api.exmail.qq.com/cgi-bin/department/list?access_token=ACCESS_TOKEN&id=ID".replace("=ACCESS_TOKEN", "=" + paramString1).replace("=ID", "=" + paramString2);
/* 116 */     String str2 = HttpsUtil.httpsRequest(str1, "GET", null);
/* 117 */     return JSONObject.parseObject(str2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject searchDepartment(String paramString1, String paramString2) {
/* 128 */     String str1 = "https://api.exmail.qq.com/cgi-bin/department/search?access_token=ACCESS_TOKEN".replace("=ACCESS_TOKEN", "=" + paramString1);
/* 129 */     String str2 = HttpsUtil.httpsRequest(str1, "POST", paramString2);
/* 130 */     return JSONObject.parseObject(str2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject createUser(String paramString1, String paramString2) {
/* 150 */     String str1 = "https://api.exmail.qq.com/cgi-bin/user/create?access_token=ACCESS_TOKEN".replace("=ACCESS_TOKEN", "=" + paramString1);
/* 151 */     String str2 = HttpsUtil.httpsRequest(str1, "POST", paramString2);
/* 152 */     return JSONObject.parseObject(str2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject updateUser(String paramString1, String paramString2) {
/* 170 */     String str1 = "https://api.exmail.qq.com/cgi-bin/user/update?access_token=ACCESS_TOKEN".replace("=ACCESS_TOKEN", "=" + paramString1);
/* 171 */     String str2 = HttpsUtil.httpsRequest(str1, "POST", paramString2);
/* 172 */     return JSONObject.parseObject(str2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject deleteUser(String paramString1, String paramString2) {
/* 183 */     String str1 = "https://api.exmail.qq.com/cgi-bin/user/delete?access_token=ACCESS_TOKEN&userid=USERID".replace("=ACCESS_TOKEN", "=" + paramString1).replace("=USERID", "=" + paramString2);
/* 184 */     String str2 = HttpsUtil.httpsRequest(str1, "GET", null);
/* 185 */     return JSONObject.parseObject(str2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject getUser(String paramString1, String paramString2) {
/* 195 */     String str1 = "https://api.exmail.qq.com/cgi-bin/user/get?access_token=ACCESS_TOKEN&userid=USERID".replace("=ACCESS_TOKEN", "=" + paramString1).replace("=USERID", "=" + paramString2);
/* 196 */     String str2 = HttpsUtil.httpsRequest(str1, "GET", null);
/* 197 */     return JSONObject.parseObject(str2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject simpleListUser(String paramString1, String paramString2, String paramString3) {
/* 209 */     String str1 = "https://api.exmail.qq.com/cgi-bin/user/simplelist?access_token=ACCESS_TOKEN&department_id=DEPARTMENT_ID&fetch_child=FETCH_CHILD".replace("=ACCESS_TOKEN", "=" + paramString1).replace("=DEPARTMENT_ID", "=" + paramString2).replace("=FETCH_CHILD", "=" + paramString3);
/* 210 */     String str2 = HttpsUtil.httpsRequest(str1, "GET", null);
/* 211 */     return JSONObject.parseObject(str2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject listUser(String paramString1, String paramString2, String paramString3) {
/* 222 */     String str1 = "https://api.exmail.qq.com/cgi-bin/user/list?access_token=ACCESS_TOKEN&department_id=DEPARTMENT_ID&fetch_child=FETCH_CHILD".replace("=ACCESS_TOKEN", "=" + paramString1).replace("=DEPARTMENT_ID", "=" + paramString2).replace("=FETCH_CHILD", "=" + paramString3);
/* 223 */     String str2 = HttpsUtil.httpsRequest(str1, "GET", null);
/* 224 */     return JSONObject.parseObject(str2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject batchCheck(String paramString1, String paramString2) {
/* 234 */     String str1 = "https://api.exmail.qq.com/cgi-bin/user/batchcheck?access_token=ACCESS_TOKEN".replace("=ACCESS_TOKEN", "=" + paramString1);
/* 235 */     String str2 = HttpsUtil.httpsRequest(str1, "POST", paramString2);
/* 236 */     return JSONObject.parseObject(str2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject newCount(String paramString1, String paramString2, String paramString3) {
/* 249 */     String str1 = "https://api.exmail.qq.com/cgi-bin/mail/newcount?access_token=ACCESS_TOKEN&userid=USERID".replace("=ACCESS_TOKEN", "=" + paramString1).replace("=USERID", "=" + paramString2);
/* 250 */     String str2 = HttpsUtil.httpsRequest(str1, "GET", paramString3);
/* 251 */     return JSONObject.parseObject(str2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject getLoginUrl(String paramString1, String paramString2) {
/* 263 */     String str1 = "https://api.exmail.qq.com/cgi-bin/service/get_login_url?access_token=ACCESS_TOKEN&userid=USERID".replace("=ACCESS_TOKEN", "=" + paramString1).replace("=USERID", "=" + paramString2);
/* 264 */     String str2 = HttpsUtil.httpsRequest(str1, "POST", null);
/* 265 */     return JSONObject.parseObject(str2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject searchNewMail(String paramString1, String paramString2, String paramString3) {
/* 277 */     String str1 = "https://api.exmail.qq.com/cgi-bin/log/mail?access_token=ACCESS_TOKEN".replace("=ACCESS_TOKEN", "=" + paramString1);
/* 278 */     String str2 = HttpsUtil.httpsRequest(str1, "POST", paramString3);
/* 279 */     return JSONObject.parseObject(str2);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/qqmail/api/MailApi.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */