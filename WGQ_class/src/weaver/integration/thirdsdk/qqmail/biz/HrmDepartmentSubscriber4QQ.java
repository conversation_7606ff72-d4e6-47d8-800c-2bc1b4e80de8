/*     */ package weaver.integration.thirdsdk.qqmail.biz;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.framework.data.field.FieldData;
/*     */ import weaver.integration.hrm.util.ValidResult;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.thirdsdk.qqmail.api.MailApi;
/*     */ import weaver.integration.thirdsdk.qqmail.constant.MessageCode;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmDepartmentSubscriber4QQ
/*     */ {
/*     */   public Object synData(List<FieldData> paramList, String paramString) {
/*  22 */     ValidResult validResult = new ValidResult();
/*  23 */     String str = getAccessToken();
/*  24 */     if (!"".equals(str) && 
/*  25 */       paramList != null) {
/*  26 */       String str1 = "";
/*  27 */       String str2 = "";
/*  28 */       String str3 = "";
/*  29 */       String str4 = "";
/*  30 */       String str5 = "";
/*  31 */       int i = 0;
/*  32 */       for (FieldData fieldData : paramList) {
/*  33 */         Object object = fieldData.getFieldValue();
/*  34 */         String str8 = fieldData.getFieldName();
/*  35 */         this.newlog.info("============fieldName:" + str8 + ",fieldValue:" + object);
/*  36 */         if ("canceled".equalsIgnoreCase(str8)) {
/*  37 */           str1 = object.toString(); continue;
/*  38 */         }  if ("id".equalsIgnoreCase(str8)) {
/*  39 */           str2 = object.toString(); continue;
/*  40 */         }  if ("departmentname".equalsIgnoreCase(str8)) {
/*  41 */           str3 = object.toString(); continue;
/*  42 */         }  if ("subcompanyid1".equalsIgnoreCase(str8)) {
/*  43 */           str4 = object.toString(); continue;
/*  44 */         }  if ("supdepid".equalsIgnoreCase(str8)) {
/*  45 */           str5 = object.toString(); continue;
/*     */         } 
/*  47 */         i = Util.getIntValue(object.toString(), 0);
/*  48 */         if ("showorder".equalsIgnoreCase(str8) && !validResult.getValidResult("showorder", i + "")) {
/*  49 */           this.newlog.error("部门：" + str4 + "(" + str3 + ")，部门排序不能为负数置为0！");
/*  50 */           i = 0;
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/*  56 */       RecordSet recordSet1 = new RecordSet();
/*  57 */       RecordSet recordSet2 = new RecordSet();
/*     */       
/*  59 */       if (str5 == null || "".equals(str5)) {
/*  60 */         str5 = "0";
/*     */       }
/*     */       
/*  63 */       String str6 = "INSERT INTO QQMailLog\n        ( LogDate ,\n          LogTime ,\n          operateRemark ,\n          datatype ,\n          operateType ,\n          operateResult ,\n          fail_summary\n        )\nVALUES  ( ? , \n          ? ,\n          ? , \n          ? , \n          ? , \n          ? , \n          ?\n        )";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  81 */       String str7 = "SELECT * FROM QQMailMap WHERE weavertype='2' AND weaverid=?";
/*  82 */       recordSet1.executeQuery(str7, new Object[] { str2 });
/*  83 */       boolean bool = recordSet1.next();
/*  84 */       JSONObject jSONObject = new JSONObject();
/*  85 */       if (bool) {
/*     */         
/*  87 */         String str8 = recordSet1.getString("qqid");
/*  88 */         String str9 = recordSet1.getString("qqpid");
/*     */         
/*  90 */         boolean bool1 = false;
/*  91 */         JSONObject jSONObject1 = MailApi.listDepartment(str, str8);
/*  92 */         if (jSONObject1 != null && "0".equals(jSONObject1.getString("errcode"))) {
/*  93 */           JSONArray jSONArray = jSONObject1.getJSONArray("department");
/*  94 */           if (jSONArray != null && jSONArray.size() > 0) {
/*  95 */             bool1 = true;
/*     */           }
/*     */         } 
/*  98 */         if (bool1) {
/*     */           
/* 100 */           if ("delete".equalsIgnoreCase(paramString)) {
/* 101 */             jSONObject = MailApi.deleteDepartment(str, str8);
/* 102 */             jSONObject.put("operateType", "3");
/* 103 */             if ("0".equals(jSONObject.getString("errcode"))) {
/* 104 */               recordSet2.executeUpdate("delete from QQMailMap where weavertype='1' and qqid=?", new Object[] { str8 });
/*     */             }
/*     */           }
/* 107 */           else if (!"1".equals(str1)) {
/*     */ 
/*     */             
/* 110 */             RecordSet recordSet = new RecordSet();
/* 111 */             if (!"0".equals(str5)) {
/* 112 */               recordSet.executeQuery("SELECT * FROM QQMailMap WHERE weaverid=? AND weavertype IN(1,2) ", new Object[] { str5 });
/* 113 */               while (recordSet.next()) {
/* 114 */                 String str10 = recordSet.getString("weavertype");
/* 115 */                 String str11 = recordSet.getString("qqid");
/* 116 */                 if ("2".equals(str10)) {
/*     */                   
/* 118 */                   str9 = str11;
/*     */                   break;
/*     */                 } 
/* 121 */                 str9 = str11;
/*     */               }
/*     */             
/*     */             }
/* 125 */             else if ("0".equals(str5)) {
/* 126 */               recordSet.executeQuery("select * from QQMailMap qm WHERE weaverid = ? AND weavertype = 1", new Object[] { str4 });
/* 127 */               while (recordSet.next()) {
/* 128 */                 String str10 = recordSet.getString("weavertype");
/* 129 */                 String str11 = recordSet.getString("qqid");
/* 130 */                 if ("1".equals(str10)) {
/*     */                   
/* 132 */                   str9 = str11;
/*     */                   break;
/*     */                 } 
/* 135 */                 str9 = str11;
/*     */               } 
/*     */             } 
/*     */             
/* 139 */             JSONObject jSONObject2 = new JSONObject();
/* 140 */             jSONObject2.put("parentid", Long.valueOf(getLong(str9)));
/* 141 */             jSONObject2.put("id", Long.valueOf(getLong(str8)));
/* 142 */             jSONObject2.put("name", str3);
/* 143 */             jSONObject2.put("order", Integer.valueOf(i));
/* 144 */             jSONObject = MailApi.updateDepartment(str, jSONObject2.toJSONString());
/* 145 */             jSONObject.put("operateType", "2");
/* 146 */             if ("0".equals(jSONObject.getString("errcode"))) {
/* 147 */               recordSet2.executeUpdate("update QQMailMap set name=?,qqpid=? where weavertype='2' and qqid=?", new Object[] { str3, str9, str8 });
/*     */             }
/*     */           } else {
/* 150 */             jSONObject = MailApi.deleteDepartment(str, str8);
/* 151 */             jSONObject.put("operateType", "3");
/* 152 */             if ("0".equals(jSONObject.getString("errcode"))) {
/* 153 */               recordSet2.executeUpdate("delete from QQMailMap where weavertype='1' and qqid=?", new Object[] { str8 });
/*     */             
/*     */             }
/*     */           
/*     */           }
/*     */         
/*     */         }
/* 160 */         else if (!"delete".equalsIgnoreCase(paramString)) {
/* 161 */           JSONObject jSONObject2 = new JSONObject();
/* 162 */           String str10 = "SELECT * FROM QQMailMap WHERE weavertype='2' AND weaverid=?";
/* 163 */           recordSet2.executeQuery(str10, new Object[] { str5 });
/* 164 */           if (recordSet2.next()) {
/* 165 */             str9 = recordSet2.getString("qqid");
/* 166 */             jSONObject2.put("parentid", Long.valueOf(getLong(recordSet2.getString("qqid"))));
/*     */           } else {
/* 168 */             str10 = "SELECT * FROM QQMailMap WHERE weavertype='1' AND weaverid=?";
/* 169 */             recordSet2.executeQuery(str10, new Object[] { str4 });
/* 170 */             if (recordSet2.next()) {
/* 171 */               str9 = recordSet2.getString("qqid");
/* 172 */               jSONObject2.put("parentid", Long.valueOf(getLong(recordSet2.getString("qqid"))));
/*     */             } 
/*     */           } 
/* 175 */           jSONObject2.put("name", str3);
/* 176 */           jSONObject2.put("order", Integer.valueOf(i));
/*     */           
/* 178 */           jSONObject = MailApi.createDepartment(str, jSONObject2.toJSONString());
/* 179 */           jSONObject.put("operateType", "1");
/* 180 */           if ("0".equals(jSONObject.getString("errcode"))) {
/* 181 */             recordSet2.executeUpdate("insert into QQMailMap(qqid,qqpid,weaverid,weavertype,name) values (?,?,?,?,?)", new Object[] { jSONObject.getString("id"), str9, str2, "2", str3 });
/*     */           }
/*     */         } else {
/*     */           
/* 185 */           jSONObject.put("operateType", "3");
/* 186 */           jSONObject.put("errcode", "60003");
/* 187 */           jSONObject.put("errmsg", "");
/* 188 */           recordSet2.executeUpdate(str6, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str3, "2", jSONObject.getString("operateType"), "0", MessageCode.RETURN_CODE.get(jSONObject.getString("errcode")) });
/* 189 */           this.newlog.error("有部门映射但是腾讯邮箱没有该分部(删除动作),部门：" + str4 + "(" + str3 + ")，上级部门：" + str5 + "，同步失败，code=" + jSONObject.getString("errcode") + "，msg=" + jSONObject.getString("errmsg"));
/* 190 */           return null;
/*     */ 
/*     */         
/*     */         }
/*     */ 
/*     */ 
/*     */       
/*     */       }
/* 198 */       else if (!"delete".equalsIgnoreCase(paramString)) {
/* 199 */         if (!"1".equals(str1)) {
/*     */           
/* 201 */           String str8 = "1";
/* 202 */           JSONObject jSONObject1 = new JSONObject();
/* 203 */           String str9 = "SELECT * FROM QQMailMap WHERE weavertype='2' AND weaverid=?";
/* 204 */           recordSet2.executeQuery(str9, new Object[] { str5 });
/* 205 */           if (recordSet2.next()) {
/* 206 */             str8 = recordSet2.getString("qqid");
/* 207 */             jSONObject1.put("parentid", Long.valueOf(getLong(recordSet2.getString("qqid"))));
/*     */           } else {
/* 209 */             str9 = "SELECT * FROM QQMailMap WHERE weavertype='1' AND weaverid=?";
/* 210 */             recordSet2.executeQuery(str9, new Object[] { str4 });
/* 211 */             if (recordSet2.next()) {
/* 212 */               str8 = recordSet2.getString("qqid");
/* 213 */               jSONObject1.put("parentid", Long.valueOf(getLong(recordSet2.getString("qqid"))));
/*     */             } 
/*     */           } 
/* 216 */           jSONObject1.put("name", str3);
/* 217 */           jSONObject1.put("order", Integer.valueOf(i));
/*     */           
/* 219 */           jSONObject = MailApi.createDepartment(str, jSONObject1.toJSONString());
/* 220 */           jSONObject.put("operateType", "1");
/* 221 */           if ("0".equals(jSONObject.getString("errcode"))) {
/* 222 */             recordSet2.executeUpdate("insert into QQMailMap(qqid,qqpid,weaverid,weavertype,name) values (?,?,?,?,?)", new Object[] { jSONObject.getString("id"), str8, str2, "2", str3 });
/*     */           }
/*     */         } else {
/*     */           
/* 226 */           jSONObject.put("operateType", "3");
/* 227 */           jSONObject.put("errcode", "60003");
/* 228 */           jSONObject.put("errmsg", "");
/* 229 */           recordSet2.executeUpdate(str6, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str3, "2", jSONObject.getString("operateType"), "0", MessageCode.RETURN_CODE.get(jSONObject.getString("errcode")) });
/* 230 */           this.newlog.error("没有部门映射,封存动作,部门：" + str4 + "(" + str3 + ")，上级部门：" + str5 + "，同步失败，code=" + jSONObject.getString("errcode") + "，msg=" + jSONObject.getString("errmsg"));
/* 231 */           return null;
/*     */         } 
/*     */       } else {
/*     */         
/* 235 */         jSONObject.put("operateType", "3");
/* 236 */         jSONObject.put("errcode", "60003");
/* 237 */         jSONObject.put("errmsg", "");
/* 238 */         recordSet2.executeUpdate(str6, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str3, "2", jSONObject.getString("operateType"), "0", MessageCode.RETURN_CODE.get(jSONObject.getString("errcode")) });
/* 239 */         this.newlog.error("没有部门映射,腾讯邮箱没有该分部(删除动作),部门：" + str4 + "(" + str3 + ")，上级部门：" + str5 + "，同步失败，code=" + jSONObject.getString("errcode") + "，msg=" + jSONObject.getString("errmsg"));
/* 240 */         return null;
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 247 */       if (jSONObject != null && "0".equals(jSONObject.getString("errcode"))) {
/* 248 */         recordSet2.executeUpdate(str6, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str3, "2", jSONObject.getString("operateType"), "1", "" });
/* 249 */         this.newlog.error("部门：" + str4 + "(" + str3 + ")，上级部门：" + str5 + "，同步成功！");
/* 250 */       } else if (jSONObject != null) {
/* 251 */         recordSet2.executeUpdate(str6, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str3, "2", jSONObject.getString("operateType"), "0", MessageCode.RETURN_CODE.get(jSONObject.getString("errcode")) });
/* 252 */         this.newlog.error("部门：" + str4 + "(" + str3 + ")，上级部门：" + str5 + "，同步失败，code=" + jSONObject.getString("errcode") + "，msg=" + jSONObject.getString("errmsg"));
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 258 */     return null;
/*     */   }
/*     */   
/* 261 */   private Logger newlog = LoggerFactory.getLogger(HrmDepartmentSubscriber4QQ.class);
/*     */   
/*     */   private long getLong(String paramString) {
/*     */     try {
/* 265 */       return Long.parseLong(paramString);
/* 266 */     } catch (Exception exception) {
/* 267 */       return -1L;
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private String getAccessToken() {
/* 273 */     RecordSet recordSet = new RecordSet();
/* 274 */     recordSet.executeQuery("SELECT corpid,corpsecret FROM QQMailSetting WHERE isuse='1'", new Object[0]);
/* 275 */     if (recordSet.next()) {
/*     */       
/* 277 */       JSONObject jSONObject = MailApi.getAccessToken(recordSet.getString("corpid"), recordSet.getString("corpsecret"));
/* 278 */       if (jSONObject.containsKey("access_token")) {
/* 279 */         return jSONObject.getString("access_token");
/*     */       }
/*     */     } 
/*     */     
/* 283 */     return "";
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/qqmail/biz/HrmDepartmentSubscriber4QQ.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */