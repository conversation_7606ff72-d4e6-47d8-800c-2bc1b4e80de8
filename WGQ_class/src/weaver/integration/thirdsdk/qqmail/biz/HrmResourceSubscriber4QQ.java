/*     */ package weaver.integration.thirdsdk.qqmail.biz;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.integration.framework.data.field.FieldData;
/*     */ import weaver.integration.hrm.util.ValidResult;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.thirdsdk.qqmail.api.MailApi;
/*     */ import weaver.integration.thirdsdk.qqmail.constant.MessageCode;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmResourceSubscriber4QQ
/*     */ {
/*     */   public Object synData(List<FieldData> paramList, String paramString) {
/*  27 */     ValidResult validResult = new ValidResult();
/*  28 */     String str = getAccessToken();
/*  29 */     if (!"".equals(str) && 
/*  30 */       paramList != null) {
/*  31 */       String str1 = "";
/*  32 */       String str2 = "";
/*  33 */       String str3 = "";
/*  34 */       String str4 = "";
/*  35 */       String str5 = "";
/*  36 */       String str6 = "";
/*  37 */       String str7 = "";
/*  38 */       String str8 = "";
/*  39 */       String str9 = "";
/*  40 */       String str10 = "";
/*  41 */       String str11 = "";
/*  42 */       String str12 = getRandomPassword(18);
/*  43 */       for (FieldData fieldData : paramList) {
/*  44 */         Object object = fieldData.getFieldValue();
/*  45 */         String str14 = fieldData.getFieldName();
/*     */         
/*  47 */         if ("loginid".equalsIgnoreCase(str14)) {
/*  48 */           str2 = object.toString(); continue;
/*  49 */         }  if ("id".equalsIgnoreCase(str14)) {
/*  50 */           str1 = object.toString(); continue;
/*  51 */         }  if ("lastname".equalsIgnoreCase(str14)) {
/*  52 */           str3 = object.toString(); continue;
/*  53 */         }  if ("sex".equalsIgnoreCase(str14)) {
/*  54 */           str4 = object.toString(); continue;
/*  55 */         }  if ("birthday".equalsIgnoreCase(str14)) {
/*  56 */           str5 = object.toString(); continue;
/*  57 */         }  if ("telephone".equalsIgnoreCase(str14)) {
/*  58 */           str6 = object.toString();
/*  59 */           if (!validResult.getValidResult("telephone", str6)) {
/*  60 */             this.newlog.error("人员：ID=" + str1 + "，loginid=" + str2 + "，email=" + str8 + "，所属部门：" + str9 + "，座机号码校验不通过置为空！");
/*  61 */             str6 = "";
/*     */           }  continue;
/*  63 */         }  if ("mobile".equalsIgnoreCase(str14)) {
/*  64 */           str7 = object.toString();
/*  65 */           if (!validResult.getValidResult("mobile", str7)) {
/*  66 */             str7 = "";
/*  67 */             this.newlog.error("人员：ID=" + str1 + "，loginid=" + str2 + "，email=" + str8 + "，所属部门：" + str9 + "，手机号码校验不通过置为空！");
/*     */           }  continue;
/*  69 */         }  if ("email".equalsIgnoreCase(str14)) {
/*  70 */           str8 = object.toString(); continue;
/*  71 */         }  if ("departmentid".equalsIgnoreCase(str14)) {
/*  72 */           str9 = object.toString(); continue;
/*  73 */         }  if ("status".equalsIgnoreCase(str14)) {
/*  74 */           str10 = object.toString(); continue;
/*  75 */         }  if ("jobtitle".equalsIgnoreCase(str14)) {
/*  76 */           str11 = object.toString();
/*     */         }
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  83 */       RecordSet recordSet1 = new RecordSet();
/*  84 */       RecordSet recordSet2 = new RecordSet();
/*  85 */       if ("".equals(str8)) {
/*  86 */         return null;
/*     */       }
/*     */       
/*  89 */       JSONObject jSONObject = MailApi.getUser(str, str8);
/*  90 */       String str13 = "INSERT INTO QQMailLog\n        ( LogDate ,\n          LogTime ,\n          operateRemark ,\n          datatype ,\n          operateType ,\n          operateResult ,\n          fail_summary\n        )\nVALUES  ( ? , \n          ? ,\n          ? , \n          ? , \n          ? , \n          ? , \n          ?\n        )";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 107 */       if (jSONObject != null && "0".equals(jSONObject.getString("errcode"))) {
/* 108 */         if ("insert".equalsIgnoreCase(paramString)) {
/* 109 */           jSONObject.put("operateType", "1");
/*     */           
/* 111 */           recordSet2.executeUpdate(str13, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str3, "3", jSONObject.getString("operateType"), "0", "邮箱账号重复" });
/* 112 */           this.newlog.error("人员：ID=" + str1 + "，loginid=" + str2 + "，email=" + str8 + "，所属部门：" + str9 + "，同步失败，code=-1，msg=email account invalid");
/* 113 */           return null;
/*     */         } 
/* 115 */         if (str10.equals("0") || str10.equals("1") || str10.equals("2") || str10.equals("3"))
/*     */         {
/* 117 */           JSONObject jSONObject1 = new JSONObject();
/* 118 */           jSONObject1.put("userid", str8);
/* 119 */           jSONObject1.put("name", str3);
/* 120 */           jSONObject1.put("department", new long[] { getLong(getQQDeptId(str9)) });
/* 121 */           jSONObject1.put("position", str11);
/* 122 */           jSONObject1.put("mobile", str7);
/* 123 */           jSONObject1.put("tel", str6);
/* 124 */           jSONObject1.put("gender", "0".equals(str4) ? "1" : "2");
/*     */ 
/*     */           
/* 127 */           jSONObject1.put("enable", Integer.valueOf(1));
/* 128 */           jSONObject1.put("cpwd_login", Integer.valueOf(0));
/* 129 */           jSONObject = MailApi.updateUser(str, jSONObject1.toJSONString());
/* 130 */           jSONObject.put("operateType", "2");
/*     */         
/*     */         }
/*     */         else
/*     */         {
/*     */           
/* 136 */           JSONObject jSONObject1 = new JSONObject();
/* 137 */           jSONObject1.put("userid", str8);
/* 138 */           jSONObject1.put("name", str3);
/* 139 */           jSONObject1.put("department", new long[] { getLong(getQQDeptId(str9)) });
/* 140 */           jSONObject1.put("position", str11);
/* 141 */           jSONObject1.put("mobile", str7);
/* 142 */           jSONObject1.put("tel", str6);
/* 143 */           jSONObject1.put("gender", "0".equals(str4) ? "1" : "2");
/*     */           
/* 145 */           jSONObject1.put("enable", Integer.valueOf(0));
/* 146 */           jSONObject1.put("cpwd_login", Integer.valueOf(0));
/* 147 */           jSONObject = MailApi.updateUser(str, jSONObject1.toJSONString());
/*     */           
/* 149 */           jSONObject.put("operateType", "3");
/*     */         }
/*     */       
/* 152 */       } else if (str10.equals("0") || str10.equals("1") || str10.equals("2") || str10.equals("3")) {
/*     */         
/* 154 */         if (str8.indexOf("@") > -1) {
/* 155 */           JSONObject jSONObject1 = new JSONObject();
/* 156 */           jSONObject1.put("userid", str8);
/* 157 */           jSONObject1.put("name", str3);
/* 158 */           jSONObject1.put("department", new long[] { getLong(getQQDeptId(str9)) });
/* 159 */           jSONObject1.put("position", str11);
/* 160 */           jSONObject1.put("mobile", str7);
/* 161 */           jSONObject1.put("tel", str6);
/* 162 */           jSONObject1.put("gender", "0".equals(str4) ? "1" : "2");
/* 163 */           jSONObject1.put("password", str12);
/*     */           
/* 165 */           jSONObject1.put("cpwd_login", Integer.valueOf(0));
/*     */           
/* 167 */           jSONObject = MailApi.createUser(str, jSONObject1.toJSONString());
/* 168 */           if (jSONObject != null && "0".equals(jSONObject.getString("errcode"))) {
/*     */             
/* 170 */             String str14 = "INSERT INTO QQMailDefaultPwd\n        ( userid ,\n          loginid ,\n          lastname ,\n          defaultpwd ,\n          createtime\n        )\nVALUES  ( ? ,\n          ? ,\n          ? ,\n          ? ,\n          ?  \n        )";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 183 */             recordSet2.executeUpdate(str14, new Object[] { str1, str2, str3, str12, TimeUtil.getCurrentTimeString() });
/*     */           } 
/* 185 */           jSONObject.put("operateType", "1");
/*     */         } else {
/*     */           
/* 188 */           jSONObject.put("operateType", "1");
/* 189 */           jSONObject.put("errcode", "60003");
/* 190 */           jSONObject.put("errmsg", "");
/* 191 */           recordSet2.executeUpdate(str13, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str3, "3", jSONObject.getString("operateType"), "0", "邮箱地址错误" });
/* 192 */           this.newlog.error("人员：ID=" + str1 + "，loginid=" + str2 + "，email=" + str8 + "，所属部门：" + str9 + "，邮箱地址错误！");
/* 193 */           return null;
/*     */         } 
/*     */       } else {
/*     */         
/* 197 */         jSONObject.put("operateType", "3");
/* 198 */         jSONObject.put("errcode", "60003");
/* 199 */         jSONObject.put("errmsg", "");
/* 200 */         recordSet2.executeUpdate(str13, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str3, "3", jSONObject.getString("operateType"), "0", "邮箱账号不存在,禁用错误" });
/* 201 */         this.newlog.error("邮箱账号不存在,禁用错误,人员：ID=" + str1 + "，loginid=" + str2 + "，email=" + str8 + "，所属部门：" + str9 + "，邮箱账号不存在,禁用错误！");
/* 202 */         return null;
/*     */       } 
/*     */ 
/*     */       
/* 206 */       if (jSONObject != null && "0".equals(jSONObject.getString("errcode"))) {
/* 207 */         recordSet2.executeUpdate(str13, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str3, "3", jSONObject.getString("operateType"), "1", "" });
/* 208 */         this.newlog.error("人员：ID=" + str1 + "，loginid=" + str2 + "，email=" + str8 + "，所属部门：" + str9 + "，同步成功！");
/* 209 */       } else if (jSONObject != null) {
/* 210 */         recordSet2.executeUpdate(str13, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str3, "3", jSONObject.getString("operateType"), "0", MessageCode.RETURN_CODE.get(jSONObject.getString("errcode")) });
/* 211 */         this.newlog.error("人员：ID=" + str1 + "，loginid=" + str2 + "，email=" + str8 + "，所属部门：" + str9 + "，同步失败，code=" + jSONObject.getString("errcode") + "，msg=" + jSONObject.getString("errmsg"));
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 218 */     return null;
/*     */   }
/*     */   
/* 221 */   private Logger newlog = LoggerFactory.getLogger(HrmResourceSubscriber4QQ.class);
/*     */   
/*     */   private long getLong(String paramString) {
/*     */     try {
/* 225 */       return Long.parseLong(paramString);
/* 226 */     } catch (Exception exception) {
/* 227 */       return -1L;
/*     */     } 
/*     */   }
/*     */   
/*     */   private String getQQDeptId(String paramString) {
/* 232 */     RecordSet recordSet = new RecordSet();
/* 233 */     recordSet.executeQuery("SELECT qqid FROM QQMailMap WHERE weavertype='2' AND weaverid=?", new Object[] { paramString });
/* 234 */     if (recordSet.next()) {
/* 235 */       return recordSet.getString("qqid");
/*     */     }
/* 237 */     return "1";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getRandomPassword(int paramInt) {
/* 245 */     char[] arrayOfChar = new char[paramInt];
/* 246 */     byte b = 0;
/* 247 */     while (b < paramInt) {
/* 248 */       int i = (int)(Math.random() * 3.0D);
/* 249 */       if (i == 0) {
/* 250 */         arrayOfChar[b] = (char)(int)(65.0D + Math.random() * 26.0D);
/* 251 */       } else if (i == 1) {
/* 252 */         arrayOfChar[b] = (char)(int)(97.0D + Math.random() * 26.0D);
/*     */       } else {
/* 254 */         arrayOfChar[b] = (char)(int)(48.0D + Math.random() * 10.0D);
/* 255 */       }  b++;
/*     */     } 
/* 257 */     return new String(arrayOfChar);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private String getAccessToken() {
/* 263 */     RecordSet recordSet = new RecordSet();
/* 264 */     recordSet.executeQuery("SELECT corpid,corpsecret FROM QQMailSetting WHERE isuse='1'", new Object[0]);
/* 265 */     if (recordSet.next()) {
/*     */       
/* 267 */       JSONObject jSONObject = MailApi.getAccessToken(recordSet.getString("corpid"), recordSet.getString("corpsecret"));
/* 268 */       if (jSONObject != null && 
/* 269 */         jSONObject.containsKey("access_token")) {
/* 270 */         return jSONObject.getString("access_token");
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 275 */     return "";
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/qqmail/biz/HrmResourceSubscriber4QQ.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */