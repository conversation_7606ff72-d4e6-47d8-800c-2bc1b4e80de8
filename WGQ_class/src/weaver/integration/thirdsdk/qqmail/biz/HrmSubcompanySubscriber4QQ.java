/*     */ package weaver.integration.thirdsdk.qqmail.biz;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.framework.data.field.FieldData;
/*     */ import weaver.integration.hrm.util.ValidResult;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.thirdsdk.qqmail.api.MailApi;
/*     */ import weaver.integration.thirdsdk.qqmail.constant.MessageCode;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmSubcompanySubscriber4QQ
/*     */ {
/*     */   public Object synData(List<FieldData> paramList, String paramString) {
/*  22 */     ValidResult validResult = new ValidResult();
/*  23 */     String str = getAccessToken();
/*  24 */     if (!"".equals(str) && 
/*  25 */       paramList != null) {
/*  26 */       String str1 = "";
/*  27 */       String str2 = "";
/*  28 */       String str3 = "";
/*  29 */       String str4 = "";
/*  30 */       int i = 0;
/*  31 */       for (FieldData fieldData : paramList) {
/*  32 */         Object object = fieldData.getFieldValue();
/*  33 */         String str7 = fieldData.getFieldName();
/*  34 */         this.newlog.info("============fieldName:" + str7 + ",fieldValue:" + object);
/*  35 */         if ("canceled".equalsIgnoreCase(str7)) {
/*  36 */           str1 = object.toString(); continue;
/*  37 */         }  if ("id".equalsIgnoreCase(str7)) {
/*  38 */           str2 = object.toString(); continue;
/*  39 */         }  if ("subcompanyname".equalsIgnoreCase(str7)) {
/*  40 */           str3 = object.toString(); continue;
/*  41 */         }  if ("supsubcomid".equalsIgnoreCase(str7)) {
/*  42 */           str4 = object.toString(); continue;
/*     */         } 
/*  44 */         i = Util.getIntValue(object.toString(), 0);
/*  45 */         if ("showorder".equalsIgnoreCase(str7) && !validResult.getValidResult("showorder", i + "")) {
/*  46 */           this.newlog.error("分部：" + str2 + "(" + str3 + ")，分部排序不能为负数置为0！");
/*  47 */           i = 0;
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/*  53 */       RecordSet recordSet1 = new RecordSet();
/*  54 */       RecordSet recordSet2 = new RecordSet();
/*     */       
/*  56 */       if (str4 == null || "".equals(str4)) {
/*  57 */         str4 = "0";
/*     */       }
/*     */       
/*  60 */       String str5 = "INSERT INTO QQMailLog\n        ( LogDate ,\n          LogTime ,\n          operateRemark ,\n          datatype ,\n          operateType ,\n          operateResult ,\n          fail_summary\n        )\nVALUES  ( ? , \n          ? ,\n          ? , \n          ? , \n          ? , \n          ? , \n          ?\n        )";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  79 */       String str6 = "SELECT * FROM QQMailMap WHERE weavertype='1' AND weaverid=?";
/*  80 */       recordSet1.executeQuery(str6, new Object[] { str2 });
/*  81 */       boolean bool = recordSet1.next();
/*  82 */       JSONObject jSONObject = new JSONObject();
/*  83 */       if (bool) {
/*     */         
/*  85 */         String str7 = recordSet1.getString("qqid");
/*  86 */         String str8 = recordSet1.getString("qqpid");
/*     */         
/*  88 */         boolean bool1 = false;
/*  89 */         JSONObject jSONObject1 = MailApi.listDepartment(str, str7);
/*  90 */         if (jSONObject1 != null && "0".equals(jSONObject1.getString("errcode"))) {
/*  91 */           JSONArray jSONArray = jSONObject1.getJSONArray("department");
/*  92 */           if (jSONArray != null && jSONArray.size() > 0) {
/*  93 */             bool1 = true;
/*     */           }
/*     */         } 
/*  96 */         if (bool1) {
/*     */           
/*  98 */           if ("delete".equalsIgnoreCase(paramString)) {
/*     */             
/* 100 */             jSONObject = MailApi.deleteDepartment(str, str7);
/* 101 */             jSONObject.put("operateType", "3");
/* 102 */             if ("0".equals(jSONObject.getString("errcode"))) {
/* 103 */               recordSet2.executeUpdate("delete from QQMailMap where weavertype='1' and qqid=?", new Object[] { str7 });
/*     */             }
/*     */           }
/* 106 */           else if (!"1".equals(str1)) {
/*     */             
/* 108 */             JSONObject jSONObject2 = new JSONObject();
/* 109 */             if ("0".equals(str4)) {
/*     */               
/* 111 */               jSONObject2.put("parentid", Integer.valueOf(1));
/*     */             } else {
/* 113 */               String str9 = "SELECT * FROM QQMailMap WHERE weavertype='1' AND weaverid=?";
/* 114 */               recordSet2.executeQuery(str9, new Object[] { str4 });
/* 115 */               if (recordSet2.next()) {
/* 116 */                 str8 = recordSet2.getString("qqid");
/* 117 */                 jSONObject2.put("parentid", Long.valueOf(getLong(recordSet2.getString("qqid"))));
/*     */               } else {
/* 119 */                 jSONObject2.put("parentid", Integer.valueOf(1));
/*     */               } 
/*     */             } 
/*     */             
/* 123 */             jSONObject2.put("id", Long.valueOf(getLong(str7)));
/* 124 */             jSONObject2.put("name", str3);
/* 125 */             jSONObject2.put("order", Integer.valueOf(i));
/* 126 */             jSONObject = MailApi.updateDepartment(str, jSONObject2.toJSONString());
/* 127 */             jSONObject.put("operateType", "2");
/* 128 */             if ("0".equals(jSONObject.getString("errcode"))) {
/* 129 */               recordSet2.executeUpdate("update QQMailMap set name=?,qqpid=? where weavertype='1' and qqid=?", new Object[] { str3, str8, str7 });
/*     */             }
/*     */           } else {
/*     */             
/* 133 */             jSONObject = MailApi.deleteDepartment(str, str7);
/* 134 */             jSONObject.put("operateType", "3");
/* 135 */             if ("0".equals(jSONObject.getString("errcode"))) {
/* 136 */               recordSet2.executeUpdate("delete from QQMailMap where weavertype='1' and qqid=?", new Object[] { str7 });
/*     */             
/*     */             }
/*     */           }
/*     */         
/*     */         }
/* 142 */         else if (!"delete".equalsIgnoreCase(paramString)) {
/* 143 */           JSONObject jSONObject2 = new JSONObject();
/* 144 */           if ("0".equals(str4)) {
/* 145 */             jSONObject2.put("parentid", Integer.valueOf(1));
/*     */           } else {
/* 147 */             String str9 = "SELECT * FROM QQMailMap WHERE weavertype='1' AND weaverid=?";
/* 148 */             recordSet2.executeQuery(str9, new Object[] { str4 });
/* 149 */             if (recordSet2.next()) {
/* 150 */               str8 = recordSet2.getString("qqid");
/* 151 */               jSONObject2.put("parentid", Long.valueOf(getLong(recordSet2.getString("qqid"))));
/*     */             } else {
/* 153 */               jSONObject2.put("parentid", Integer.valueOf(1));
/*     */             } 
/*     */           } 
/* 156 */           jSONObject2.put("name", str3);
/* 157 */           jSONObject2.put("order", Integer.valueOf(i));
/*     */           
/* 159 */           jSONObject = MailApi.createDepartment(str, jSONObject2.toJSONString());
/* 160 */           jSONObject.put("operateType", "1");
/* 161 */           if ("0".equals(jSONObject.getString("errcode"))) {
/* 162 */             recordSet2.executeUpdate("insert into QQMailMap(qqid,qqpid,weaverid,weavertype,name) values (?,?,?,?,?)", new Object[] { jSONObject.getString("id"), str8, str2, "1", str3 });
/*     */           }
/*     */         } else {
/*     */           
/* 166 */           jSONObject.put("operateType", "3");
/* 167 */           jSONObject.put("errcode", "60003");
/* 168 */           jSONObject.put("errmsg", "");
/* 169 */           recordSet2.executeUpdate(str5, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str3, "1", jSONObject.getString("operateType"), "0", MessageCode.RETURN_CODE.get("60003") });
/* 170 */           this.newlog.error("有分部映射但是腾讯邮箱没有该分部(删除动作),分部：" + str2 + "(" + str3 + ")，上级分部：" + str4 + "，同步失败，code=" + jSONObject.getString("errcode") + "，msg=" + jSONObject.getString("errmsg"));
/* 171 */           return null;
/*     */         
/*     */         }
/*     */ 
/*     */       
/*     */       }
/* 177 */       else if (!"delete".equalsIgnoreCase(paramString)) {
/* 178 */         if (!"1".equals(str1)) {
/*     */           
/* 180 */           String str7 = "1";
/* 181 */           JSONObject jSONObject1 = new JSONObject();
/* 182 */           if ("0".equals(str4)) {
/* 183 */             jSONObject1.put("parentid", Integer.valueOf(1));
/*     */           } else {
/* 185 */             String str8 = "SELECT * FROM QQMailMap WHERE weavertype='1' AND weaverid=?";
/* 186 */             recordSet2.executeQuery(str8, new Object[] { str4 });
/* 187 */             if (recordSet2.next()) {
/* 188 */               str7 = recordSet2.getString("qqid");
/* 189 */               jSONObject1.put("parentid", Long.valueOf(getLong(recordSet2.getString("qqid"))));
/*     */             } else {
/* 191 */               jSONObject1.put("parentid", Integer.valueOf(1));
/*     */             } 
/*     */           } 
/* 194 */           jSONObject1.put("name", str3);
/* 195 */           jSONObject1.put("order", Integer.valueOf(i));
/*     */           
/* 197 */           jSONObject = MailApi.createDepartment(str, jSONObject1.toJSONString());
/* 198 */           jSONObject.put("operateType", "1");
/* 199 */           if ("0".equals(jSONObject.getString("errcode"))) {
/* 200 */             recordSet2.executeUpdate("insert into QQMailMap(qqid,qqpid,weaverid,weavertype,name) values (?,?,?,?,?)", new Object[] { jSONObject.getString("id"), str7, str2, "1", str3 });
/*     */           }
/*     */         } else {
/*     */           
/* 204 */           jSONObject.put("operateType", "3");
/* 205 */           jSONObject.put("errcode", "60003");
/* 206 */           jSONObject.put("errmsg", "");
/* 207 */           recordSet2.executeUpdate(str5, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str3, "1", jSONObject.getString("operateType"), "0", MessageCode.RETURN_CODE.get("60003") });
/* 208 */           this.newlog.error("没有分部映射,封存动作,分部：" + str2 + "(" + str3 + ")，上级分部：" + str4 + "，同步失败，code=" + jSONObject.getString("errcode") + "，msg=" + jSONObject.getString("errmsg"));
/* 209 */           return null;
/*     */         } 
/*     */       } else {
/*     */         
/* 213 */         jSONObject.put("operateType", "3");
/* 214 */         jSONObject.put("errcode", "60003");
/* 215 */         jSONObject.put("errmsg", "");
/* 216 */         recordSet2.executeUpdate(str5, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str3, "1", jSONObject.getString("operateType"), "0", MessageCode.RETURN_CODE.get("60003") });
/* 217 */         this.newlog.error("没有分部映射,腾讯邮箱没有该分部(删除动作),分部：" + str2 + "(" + str3 + ")，上级分部：" + str4 + "，同步失败，code=" + jSONObject.getString("errcode") + "，msg=" + jSONObject.getString("errmsg"));
/* 218 */         return null;
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 225 */       if (jSONObject != null && "0".equals(jSONObject.getString("errcode"))) {
/* 226 */         recordSet2.executeUpdate(str5, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str3, "1", jSONObject.getString("operateType"), "1", "" });
/* 227 */         this.newlog.error("分部：" + str2 + "(" + str3 + ")，上级分部：" + str4 + "，同步成功！");
/* 228 */       } else if (jSONObject != null) {
/* 229 */         recordSet2.executeUpdate(str5, new Object[] { TimeUtil.getCurrentDateString(), TimeUtil.getOnlyCurrentTimeString(), str3, "1", jSONObject.getString("operateType"), "0", MessageCode.RETURN_CODE.get(jSONObject.getString("errcode")) });
/* 230 */         this.newlog.error("分部：" + str2 + "(" + str3 + ")，上级分部：" + str4 + "，同步失败，code=" + jSONObject.getString("errcode") + "，msg=" + jSONObject.getString("errmsg"));
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 236 */     return null;
/*     */   }
/*     */   
/* 239 */   private Logger newlog = LoggerFactory.getLogger(HrmSubcompanySubscriber4QQ.class);
/*     */   
/*     */   private long getLong(String paramString) {
/*     */     try {
/* 243 */       return Long.parseLong(paramString);
/* 244 */     } catch (Exception exception) {
/* 245 */       return -1L;
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private String getAccessToken() {
/* 251 */     RecordSet recordSet = new RecordSet();
/* 252 */     recordSet.executeQuery("SELECT corpid,corpsecret FROM QQMailSetting WHERE isuse='1'", new Object[0]);
/* 253 */     if (recordSet.next()) {
/*     */       
/* 255 */       JSONObject jSONObject = MailApi.getAccessToken(recordSet.getString("corpid"), recordSet.getString("corpsecret"));
/* 256 */       if (jSONObject.containsKey("access_token")) {
/* 257 */         return jSONObject.getString("access_token");
/*     */       }
/*     */     } 
/*     */     
/* 261 */     return "";
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/thirdsdk/qqmail/biz/HrmSubcompanySubscriber4QQ.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */