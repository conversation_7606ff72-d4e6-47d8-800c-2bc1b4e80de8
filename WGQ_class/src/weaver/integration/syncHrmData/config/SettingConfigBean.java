/*     */ package weaver.integration.syncHrmData.config;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SettingConfigBean
/*     */ {
/*   9 */   private String isuselhr = "";
/*     */   
/*  11 */   private String hourTime = "";
/*     */   
/*  13 */   private String intetype = "";
/*     */   
/*  15 */   private String dbsource = "";
/*     */   
/*  17 */   private String webserviceurl = "";
/*     */   
/*  19 */   private String invoketype = "";
/*     */   
/*  21 */   private String customparams = "";
/*     */   
/*  23 */   private String custominterface = "";
/*     */   
/*  25 */   private String hrmethod = "";
/*     */   
/*  27 */   private String TimeModul = "0";
/*     */   
/*  29 */   private String Frequency = "";
/*     */   
/*  31 */   private String frequencyy = "";
/*     */   
/*  33 */   private String createType = "";
/*     */   
/*  35 */   private String createTime = "";
/*     */   
/*  37 */   private String depttable = "";
/*     */   
/*  39 */   private String subcomtable = "";
/*     */   
/*  41 */   private String jobtable = "";
/*     */   
/*  43 */   private String hrmtable = "";
/*     */   
/*  45 */   private String jobmothod = "";
/*     */   
/*  47 */   private String jobparam = "";
/*     */   
/*  49 */   private String deptmothod = "";
/*     */   
/*  51 */   private String deptparam = "";
/*     */   
/*  53 */   private String subcommothod = "";
/*     */   
/*  55 */   private String subcomparam = "";
/*     */   
/*  57 */   private String hrmmethod = "";
/*     */   
/*  59 */   private String hrmparam = "";
/*     */   
/*  61 */   private String defaultPwd = "";
/*     */   
/*  63 */   private String pwdSyncType = "";
/*     */   
/*  65 */   private String subcomouternew = "";
/*     */   
/*  67 */   private String deptouternew = "";
/*     */   
/*  69 */   private String jobouternew = "";
/*     */   
/*  71 */   private String hrmouternew = "";
/*     */   
/*  73 */   private String issynrtx = "";
/*     */   
/*     */   private boolean useMultiLang = false;
/*  76 */   private String lang_ = "";
/*  77 */   private String after_clazz_ = "";
/*     */ 
/*     */   
/*     */   public String getIsuselhr() {
/*  81 */     return this.isuselhr;
/*     */   }
/*     */   
/*     */   public void setIsuselhr(String paramString) {
/*  85 */     this.isuselhr = paramString;
/*     */   }
/*     */   
/*     */   public String getHourTime() {
/*  89 */     return this.hourTime;
/*     */   }
/*     */   
/*     */   public void setHourTime(String paramString) {
/*  93 */     this.hourTime = paramString;
/*     */   }
/*     */   
/*     */   public String getIntetype() {
/*  97 */     return this.intetype;
/*     */   }
/*     */   
/*     */   public void setIntetype(String paramString) {
/* 101 */     this.intetype = paramString;
/*     */   }
/*     */   
/*     */   public String getDbsource() {
/* 105 */     return this.dbsource;
/*     */   }
/*     */   
/*     */   public void setDbsource(String paramString) {
/* 109 */     this.dbsource = paramString;
/*     */   }
/*     */   
/*     */   public String getWebserviceurl() {
/* 113 */     return this.webserviceurl;
/*     */   }
/*     */   
/*     */   public void setWebserviceurl(String paramString) {
/* 117 */     this.webserviceurl = paramString;
/*     */   }
/*     */   
/*     */   public String getInvoketype() {
/* 121 */     return this.invoketype;
/*     */   }
/*     */   
/*     */   public void setInvoketype(String paramString) {
/* 125 */     this.invoketype = paramString;
/*     */   }
/*     */   
/*     */   public String getCustomparams() {
/* 129 */     return this.customparams;
/*     */   }
/*     */   
/*     */   public void setCustomparams(String paramString) {
/* 133 */     this.customparams = paramString;
/*     */   }
/*     */   
/*     */   public String getCustominterface() {
/* 137 */     return this.custominterface;
/*     */   }
/*     */   
/*     */   public void setCustominterface(String paramString) {
/* 141 */     this.custominterface = paramString;
/*     */   }
/*     */   
/*     */   public String getHrmethod() {
/* 145 */     return this.hrmethod;
/*     */   }
/*     */   
/*     */   public void setHrmethod(String paramString) {
/* 149 */     this.hrmethod = paramString;
/*     */   }
/*     */   
/*     */   public String getTimeModul() {
/* 153 */     return this.TimeModul;
/*     */   }
/*     */   
/*     */   public void setTimeModul(String paramString) {
/* 157 */     this.TimeModul = paramString;
/*     */   }
/*     */   
/*     */   public String getFrequency() {
/* 161 */     return this.Frequency;
/*     */   }
/*     */   
/*     */   public void setFrequency(String paramString) {
/* 165 */     this.Frequency = paramString;
/*     */   }
/*     */   
/*     */   public String getFrequencyy() {
/* 169 */     return this.frequencyy;
/*     */   }
/*     */   
/*     */   public void setFrequencyy(String paramString) {
/* 173 */     this.frequencyy = paramString;
/*     */   }
/*     */   
/*     */   public String getCreateType() {
/* 177 */     return this.createType;
/*     */   }
/*     */   
/*     */   public void setCreateType(String paramString) {
/* 181 */     this.createType = paramString;
/*     */   }
/*     */   
/*     */   public String getCreateTime() {
/* 185 */     return this.createTime;
/*     */   }
/*     */   
/*     */   public void setCreateTime(String paramString) {
/* 189 */     this.createTime = paramString;
/*     */   }
/*     */   
/*     */   public String getDepttable() {
/* 193 */     return this.depttable;
/*     */   }
/*     */   
/*     */   public void setDepttable(String paramString) {
/* 197 */     this.depttable = paramString;
/*     */   }
/*     */   
/*     */   public String getSubcomtable() {
/* 201 */     return this.subcomtable;
/*     */   }
/*     */   
/*     */   public void setSubcomtable(String paramString) {
/* 205 */     this.subcomtable = paramString;
/*     */   }
/*     */   
/*     */   public String getJobtable() {
/* 209 */     return this.jobtable;
/*     */   }
/*     */   
/*     */   public void setJobtable(String paramString) {
/* 213 */     this.jobtable = paramString;
/*     */   }
/*     */   
/*     */   public String getHrmtable() {
/* 217 */     return this.hrmtable;
/*     */   }
/*     */   
/*     */   public void setHrmtable(String paramString) {
/* 221 */     this.hrmtable = paramString;
/*     */   }
/*     */   
/*     */   public String getJobmothod() {
/* 225 */     return this.jobmothod;
/*     */   }
/*     */   
/*     */   public void setJobmothod(String paramString) {
/* 229 */     this.jobmothod = paramString;
/*     */   }
/*     */   
/*     */   public String getJobparam() {
/* 233 */     return this.jobparam;
/*     */   }
/*     */   
/*     */   public void setJobparam(String paramString) {
/* 237 */     this.jobparam = paramString;
/*     */   }
/*     */   
/*     */   public String getDeptmothod() {
/* 241 */     return this.deptmothod;
/*     */   }
/*     */   
/*     */   public void setDeptmothod(String paramString) {
/* 245 */     this.deptmothod = paramString;
/*     */   }
/*     */   
/*     */   public String getDeptparam() {
/* 249 */     return this.deptparam;
/*     */   }
/*     */   
/*     */   public void setDeptparam(String paramString) {
/* 253 */     this.deptparam = paramString;
/*     */   }
/*     */   
/*     */   public String getSubcommothod() {
/* 257 */     return this.subcommothod;
/*     */   }
/*     */   
/*     */   public void setSubcommothod(String paramString) {
/* 261 */     this.subcommothod = paramString;
/*     */   }
/*     */   
/*     */   public String getSubcomparam() {
/* 265 */     return this.subcomparam;
/*     */   }
/*     */   
/*     */   public void setSubcomparam(String paramString) {
/* 269 */     this.subcomparam = paramString;
/*     */   }
/*     */   
/*     */   public String getHrmmethod() {
/* 273 */     return this.hrmmethod;
/*     */   }
/*     */   
/*     */   public void setHrmmethod(String paramString) {
/* 277 */     this.hrmmethod = paramString;
/*     */   }
/*     */   
/*     */   public String getHrmparam() {
/* 281 */     return this.hrmparam;
/*     */   }
/*     */   
/*     */   public void setHrmparam(String paramString) {
/* 285 */     this.hrmparam = paramString;
/*     */   }
/*     */   
/*     */   public String getDefaultPwd() {
/* 289 */     return this.defaultPwd;
/*     */   }
/*     */   
/*     */   public void setDefaultPwd(String paramString) {
/* 293 */     this.defaultPwd = paramString;
/*     */   }
/*     */   
/*     */   public String getPwdSyncType() {
/* 297 */     return this.pwdSyncType;
/*     */   }
/*     */   
/*     */   public void setPwdSyncType(String paramString) {
/* 301 */     this.pwdSyncType = paramString;
/*     */   }
/*     */   
/*     */   public String getSubcomouternew() {
/* 305 */     return this.subcomouternew;
/*     */   }
/*     */   
/*     */   public void setSubcomouternew(String paramString) {
/* 309 */     this.subcomouternew = paramString;
/*     */   }
/*     */   
/*     */   public String getDeptouternew() {
/* 313 */     return this.deptouternew;
/*     */   }
/*     */   
/*     */   public void setDeptouternew(String paramString) {
/* 317 */     this.deptouternew = paramString;
/*     */   }
/*     */   
/*     */   public String getJobouternew() {
/* 321 */     return this.jobouternew;
/*     */   }
/*     */   
/*     */   public void setJobouternew(String paramString) {
/* 325 */     this.jobouternew = paramString;
/*     */   }
/*     */   
/*     */   public String getHrmouternew() {
/* 329 */     return this.hrmouternew;
/*     */   }
/*     */   
/*     */   public void setHrmouternew(String paramString) {
/* 333 */     this.hrmouternew = paramString;
/*     */   }
/*     */   
/*     */   public String getIssynrtx() {
/* 337 */     return this.issynrtx;
/*     */   }
/*     */   
/*     */   public void setIssynrtx(String paramString) {
/* 341 */     this.issynrtx = paramString;
/*     */   }
/*     */   
/*     */   public boolean isUseMultiLang() {
/* 345 */     return this.useMultiLang;
/*     */   }
/*     */   
/*     */   public void setUseMultiLang(boolean paramBoolean) {
/* 349 */     this.useMultiLang = paramBoolean;
/*     */   }
/*     */   
/*     */   public String getLang_() {
/* 353 */     return this.lang_;
/*     */   }
/*     */   
/*     */   public void setLang_(String paramString) {
/* 357 */     this.lang_ = paramString;
/*     */   }
/*     */   
/*     */   public String getAfter_clazz_() {
/* 361 */     return this.after_clazz_;
/*     */   }
/*     */   
/*     */   public void setAfter_clazz_(String paramString) {
/* 365 */     this.after_clazz_ = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/config/SettingConfigBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */