/*     */ package weaver.integration.syncHrmData.config;
/*     */ 
/*     */ import java.util.Map;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MappingBean
/*     */ {
/*     */   private Map<String, String> fieldMap;
/*     */   private Map<String, String> langMap;
/*     */   private Map<String, String> transMap;
/*     */   private Map<String, String> tranTypeMap;
/*  18 */   String key_oa = "";
/*     */   
/*  20 */   String key = "";
/*     */ 
/*     */   
/*  23 */   String ts = "";
/*     */   
/*  25 */   private String parentkey_oa = "";
/*     */   
/*  27 */   private String parentkey = "";
/*     */   
/*  29 */   private String subcomkey_oa = "";
/*     */   
/*  31 */   private String subcomkey = "";
/*     */   
/*  33 */   private String deptkey_oa = "";
/*     */   
/*  35 */   private String deptkey = "";
/*     */   
/*  37 */   private String jobkey_oa = "";
/*     */   
/*  39 */   private String jobkey = "";
/*     */   
/*     */   public Map<String, String> getFieldMap() {
/*  42 */     return this.fieldMap;
/*     */   }
/*     */   
/*     */   public void setFieldMap(Map<String, String> paramMap) {
/*  46 */     this.fieldMap = paramMap;
/*     */   }
/*     */   
/*     */   public Map<String, String> getLangMap() {
/*  50 */     return this.langMap;
/*     */   }
/*     */   
/*     */   public void setLangMap(Map<String, String> paramMap) {
/*  54 */     this.langMap = paramMap;
/*     */   }
/*     */   
/*     */   public Map<String, String> getTransMap() {
/*  58 */     return this.transMap;
/*     */   }
/*     */   
/*     */   public void setTransMap(Map<String, String> paramMap) {
/*  62 */     this.transMap = paramMap;
/*     */   }
/*     */   
/*     */   public String getKey() {
/*  66 */     return this.key;
/*     */   }
/*     */   
/*     */   public void setKey(String paramString) {
/*  70 */     this.key = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getKey_oa() {
/*  75 */     return this.key_oa;
/*     */   }
/*     */   
/*     */   public void setKey_oa(String paramString) {
/*  79 */     this.key_oa = paramString;
/*     */   }
/*     */   
/*     */   public String getParentkey_oa() {
/*  83 */     return this.parentkey_oa;
/*     */   }
/*     */   
/*     */   public void setParentkey_oa(String paramString) {
/*  87 */     this.parentkey_oa = paramString;
/*     */   }
/*     */   
/*     */   public String getParentkey() {
/*  91 */     return this.parentkey;
/*     */   }
/*     */   
/*     */   public void setParentkey(String paramString) {
/*  95 */     this.parentkey = paramString;
/*     */   }
/*     */   
/*     */   public String getSubcomkey_oa() {
/*  99 */     return this.subcomkey_oa;
/*     */   }
/*     */   
/*     */   public void setSubcomkey_oa(String paramString) {
/* 103 */     this.subcomkey_oa = paramString;
/*     */   }
/*     */   
/*     */   public String getSubcomkey() {
/* 107 */     return this.subcomkey;
/*     */   }
/*     */   
/*     */   public void setSubcomkey(String paramString) {
/* 111 */     this.subcomkey = paramString;
/*     */   }
/*     */   
/*     */   public String getDeptkey_oa() {
/* 115 */     return this.deptkey_oa;
/*     */   }
/*     */   
/*     */   public void setDeptkey_oa(String paramString) {
/* 119 */     this.deptkey_oa = paramString;
/*     */   }
/*     */   
/*     */   public String getDeptkey() {
/* 123 */     return this.deptkey;
/*     */   }
/*     */   
/*     */   public void setDeptkey(String paramString) {
/* 127 */     this.deptkey = paramString;
/*     */   }
/*     */   
/*     */   public String getJobkey_oa() {
/* 131 */     return this.jobkey_oa;
/*     */   }
/*     */   
/*     */   public void setJobkey_oa(String paramString) {
/* 135 */     this.jobkey_oa = paramString;
/*     */   }
/*     */   
/*     */   public String getJobkey() {
/* 139 */     return this.jobkey;
/*     */   }
/*     */   
/*     */   public void setJobkey(String paramString) {
/* 143 */     this.jobkey = paramString;
/*     */   }
/*     */   
/*     */   public String getTs() {
/* 147 */     return this.ts;
/*     */   }
/*     */   
/*     */   public void setTs(String paramString) {
/* 151 */     this.ts = paramString;
/*     */   }
/*     */   
/*     */   public Map<String, String> getTranTypeMap() {
/* 155 */     return this.tranTypeMap;
/*     */   }
/*     */   
/*     */   public void setTranTypeMap(Map<String, String> paramMap) {
/* 159 */     this.tranTypeMap = paramMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/config/MappingBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */