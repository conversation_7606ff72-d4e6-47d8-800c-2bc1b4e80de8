/*     */ package weaver.integration.syncHrmData.config;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.syncHrmData.util.SyncStringUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MappingBeanExecuter
/*     */ {
/*     */   public static MappingBean getSetMap(String paramString) throws Exception {
/*  16 */     MappingBean mappingBean = new MappingBean();
/*  17 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*     */     
/*  19 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */     
/*  21 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  22 */     HashMap<Object, Object> hashMap4 = new HashMap<>();
/*     */     
/*  24 */     String str1 = "";
/*     */     
/*  26 */     String str2 = "";
/*     */     
/*  28 */     String str3 = "";
/*     */     
/*  30 */     String str4 = "";
/*     */     
/*  32 */     String str5 = "";
/*     */     
/*  34 */     String str6 = "";
/*     */     
/*  36 */     String str7 = "";
/*     */     
/*  38 */     String str8 = "";
/*     */     
/*  40 */     String str9 = "";
/*     */     
/*  42 */     String str10 = "";
/*     */     
/*  44 */     String str11 = "";
/*  45 */     RecordSet recordSet = new RecordSet();
/*  46 */     recordSet.executeQuery("SELECT * FROM hrsyncsetparam where type = ? order by type,id", new Object[] { paramString });
/*  47 */     while (recordSet.next()) {
/*  48 */       String str12 = Util.null2String(recordSet.getString("oafield"));
/*  49 */       String str13 = Util.null2String(recordSet.getString("outfield"));
/*  50 */       String str14 = Util.null2String(recordSet.getString("iskeyfield"));
/*  51 */       String str15 = Util.null2String(recordSet.getString("isnewfield"));
/*     */ 
/*     */       
/*  54 */       String str16 = Util.null2String(recordSet.getString("isparentfield"));
/*     */       
/*  56 */       String str17 = Util.null2String(recordSet.getString("issubcomfield"));
/*     */       
/*  58 */       String str18 = Util.null2String(recordSet.getString("isdeptfield"));
/*     */       
/*  60 */       String str19 = Util.null2String(recordSet.getString("ishrmdeptfield"));
/*  61 */       String str20 = Util.null2String(recordSet.getString("ishrmjobfield"));
/*     */       
/*  63 */       String str21 = Util.null2String(recordSet.getString("transtype"));
/*     */       
/*  65 */       String str22 = Util.null2String(recordSet.getString("transql"));
/*  66 */       String str23 = "" + Util.getIntValue(recordSet.getString("ismulti_lang_"), 0);
/*  67 */       hashMap1.put(str12.toLowerCase(), str13);
/*  68 */       hashMap2.put(str12.toLowerCase(), str23);
/*  69 */       hashMap3.put(SyncStringUtil.getTransqlMapKey(str12, str13), str22);
/*  70 */       hashMap4.put(SyncStringUtil.getTransqlMapKey(str12, str13), str21);
/*  71 */       if ("1".equals(str14)) {
/*  72 */         str2 = str12.toLowerCase();
/*  73 */         str3 = str13;
/*     */       } 
/*  75 */       if ("1".equals(str15)) {
/*  76 */         str1 = str13;
/*     */       }
/*  78 */       if ("1".equals(str16)) {
/*  79 */         str4 = str12.toLowerCase();
/*  80 */         str5 = str13;
/*     */       } 
/*     */       
/*  83 */       if (paramString.equals("2")) {
/*  84 */         if ("1".equals(str17)) {
/*  85 */           str6 = str12.toLowerCase();
/*  86 */           str7 = str13;
/*     */         }  continue;
/*  88 */       }  if (paramString.equals("3")) {
/*  89 */         if ("1".equals(str18)) {
/*  90 */           str8 = str12.toLowerCase();
/*  91 */           str9 = str13;
/*     */         }  continue;
/*  93 */       }  if (paramString.equals("4")) {
/*  94 */         if ("1".equals(str19)) {
/*  95 */           str8 = str12.toLowerCase();
/*  96 */           str9 = str13;
/*     */         } 
/*  98 */         if ("1".equals(str20)) {
/*  99 */           str10 = str12.toLowerCase();
/* 100 */           str11 = str13;
/*     */         } 
/*     */       } 
/*     */     } 
/* 104 */     mappingBean.setFieldMap((Map)hashMap1);
/* 105 */     mappingBean.setLangMap((Map)hashMap2);
/* 106 */     mappingBean.setTransMap((Map)hashMap3);
/* 107 */     mappingBean.setTranTypeMap((Map)hashMap4);
/* 108 */     mappingBean.setTs(str1);
/* 109 */     mappingBean.setKey(str3);
/* 110 */     mappingBean.setKey_oa(str2);
/* 111 */     mappingBean.setParentkey(str5);
/* 112 */     mappingBean.setParentkey_oa(str4);
/* 113 */     mappingBean.setDeptkey(str9);
/* 114 */     mappingBean.setDeptkey_oa(str8);
/* 115 */     mappingBean.setSubcomkey(str7);
/* 116 */     mappingBean.setSubcomkey_oa(str6);
/* 117 */     mappingBean.setJobkey(str11);
/* 118 */     mappingBean.setJobkey_oa(str10);
/*     */     
/* 120 */     return mappingBean;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/config/MappingBeanExecuter.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */