package weaver.integration.syncHrmData.Const;

public class HrmSynConstValue {
  public static final String INTETYPE_DB = "1";
  
  public static final String INTETYPE_WS = "2";
  
  public static final String INTETYPE_ESB = "4";
  
  public static final String INTETYPE_CUSTOM = "3";
  
  public static final String CUSTOM_TYPE_OLD = "0";
  
  public static final String CUSTOM_TYPE_NEW = "1";
  
  public static final int CONST_PAGE_SIZE = 1000;
  
  public static final String OUTPK = "OUTPK";
  
  public static final String PK = "PK";
  
  public static final String Memo = "Memo";
  
  public static final String Success = "Success";
  
  public static final String ErrorMessage = "ErrorMessage";
  
  public static final String CusSign = "#cus#";
  
  public static final String CusFieldInfo = "cusFieldInfo";
  
  public static final String FIELD_CMD_SUBCOMPANY = "subcom";
  
  public static final String FIELD_CMD_DEPARTMENT = "dept";
  
  public static final String FIELD_CMD_JOBTITLE = "jobtitle";
  
  public static final String FIELD_CMD_RESOURCE = "hrm";
  
  public static final String CUSTOM_INF_LEVEL_SYSTEM = "1";
  
  public static final String CUSTOM_INF_LEVEL_USER = "2";
  
  public static final String PWD_SYNC_TYPE_MD5 = "1";
  
  public static final String PWD_SYNC_TYPE_COPY = "2";
  
  public static final String DATA_RANGE_ALL = "0";
  
  public static final String DATA_RANGE_SUBCOMPANY = "1";
  
  public static final String DATA_RANGE_DEPARTMENT = "2";
  
  public static final String DATA_RANGE_JOBTITLE = "3";
  
  public static final String DATA_RANGE_RESOURCE = "4";
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/Const/HrmSynConstValue.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */