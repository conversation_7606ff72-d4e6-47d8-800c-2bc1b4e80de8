/*    */ package weaver.integration.syncHrmData.output.bean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RecordData
/*    */ {
/*    */   private String action;
/*    */   private String outKey;
/*    */   private String outVlue;
/*    */   
/*    */   public String getAction() {
/* 26 */     return this.action;
/*    */   }
/*    */   
/*    */   public void setAction(String paramString) {
/* 30 */     this.action = paramString;
/*    */   }
/*    */   
/*    */   public String getOutKey() {
/* 34 */     return this.outKey;
/*    */   }
/*    */   
/*    */   public void setOutKey(String paramString) {
/* 38 */     this.outKey = paramString;
/*    */   }
/*    */   
/*    */   public String getOutVlue() {
/* 42 */     return this.outVlue;
/*    */   }
/*    */   
/*    */   public void setOutVlue(String paramString) {
/* 46 */     this.outVlue = paramString;
/*    */   }
/*    */   
/*    */   public RecordData(String paramString1, String paramString2, String paramString3) {
/* 50 */     this.action = paramString1;
/* 51 */     this.outKey = paramString2;
/* 52 */     this.outVlue = paramString3;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 57 */     return "RecordData{action='" + this.action + '\'' + ", outKey='" + this.outKey + '\'' + ", outVlue='" + this.outVlue + '\'' + '}';
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/output/bean/RecordData.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */