/*     */ package weaver.integration.syncHrmData.trans;
/*     */ 
/*     */ import com.api.integration.util.RecordSetObj;
/*     */ import java.lang.reflect.InvocationTargetException;
/*     */ import java.lang.reflect.Method;
/*     */ import java.util.ArrayList;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.syncHrmData.trans.formart.TransInterface;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FieldValueTrans
/*     */ {
/*  22 */   private static Logger newlog = LoggerFactory.getLogger(FieldValueTrans.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getTranValue(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) throws Exception {
/*     */     try {
/*  34 */       newlog.info("转换参数为：type:" + paramString1 + " |transString:" + paramString2 + " |value:" + paramString3 + " |dataSourceId:" + paramString4 + " |oaField:" + paramString5 + " |outField:" + paramString6);
/*  35 */       switch (paramString1) {
/*     */         case "0":
/*  37 */           return doGetTransSqlValue(paramString2, paramString3);
/*     */         case "1":
/*  39 */           return doGetTransClassValue(paramString2, paramString3, paramString4, paramString5, paramString6);
/*     */         case "2":
/*  41 */           return paramString2;
/*     */       } 
/*     */ 
/*     */     
/*  45 */     } catch (Exception exception) {
/*  46 */       newlog.error("第一次执行转换sql失败 sql->" + paramString2 + " value->" + paramString3, exception);
/*  47 */       if (StringUtils.isBlank(paramString3)) {
/*  48 */         newlog.error("value为空 准备替换为 null 再次执行");
/*     */         try {
/*  50 */           return doGetTransSqlValue(paramString2, "null");
/*  51 */         } catch (Exception exception1) {
/*  52 */           newlog.error("第二次执行转换sql仍然失败 sql->" + paramString2 + " value->\"null\"", exception);
/*     */         } 
/*     */       } 
/*     */     } 
/*  56 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String doGetTransClassValue(String paramString1, String paramString2) {
/*  66 */     String str1 = paramString1.substring(0, paramString1.lastIndexOf("."));
/*  67 */     String str2 = paramString1.substring(paramString1.lastIndexOf(".") + 1, paramString1.length());
/*     */     try {
/*  69 */       Class<?> clazz = Class.forName(str1);
/*  70 */       Object object = clazz.newInstance();
/*  71 */       Method method = clazz.getMethod(str2, new Class[] { String.class });
/*  72 */       return Util.null2String(method.invoke(object, new Object[] { paramString2 }));
/*     */     }
/*  74 */     catch (ClassNotFoundException classNotFoundException) {
/*  75 */       classNotFoundException.printStackTrace();
/*  76 */     } catch (InstantiationException instantiationException) {
/*  77 */       instantiationException.printStackTrace();
/*  78 */     } catch (IllegalAccessException illegalAccessException) {
/*  79 */       illegalAccessException.printStackTrace();
/*  80 */     } catch (InvocationTargetException invocationTargetException) {
/*  81 */       invocationTargetException.printStackTrace();
/*  82 */     } catch (NoSuchMethodException noSuchMethodException) {
/*  83 */       noSuchMethodException.printStackTrace();
/*     */     } 
/*  85 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String doGetTransClassValue(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/*  94 */     String str1 = "";
/*  95 */     String str2 = "";
/*  96 */     String str3 = "";
/*  97 */     RecordSetObj recordSetObj = new RecordSetObj();
/*  98 */     boolean bool = recordSetObj.executeQuery("select * from hrsync_formart where formartid=?", new Object[] { paramString1 });
/*  99 */     if (bool && recordSetObj.next()) {
/* 100 */       str1 = recordSetObj.getString("FORMARTCLASS");
/* 101 */       str2 = recordSetObj.getString("FORMARTPARAMS");
/*     */     } 
/*     */     try {
/* 104 */       TransInterface transInterface = (TransInterface)Class.forName(str1).newInstance();
/* 105 */       str3 = transInterface.getTransdata(paramString3, paramString4, paramString5, paramString2, str2);
/* 106 */     } catch (InstantiationException instantiationException) {
/* 107 */       instantiationException.printStackTrace();
/* 108 */       newlog.error(instantiationException);
/* 109 */     } catch (IllegalAccessException illegalAccessException) {
/* 110 */       illegalAccessException.printStackTrace();
/* 111 */       newlog.error(illegalAccessException);
/* 112 */     } catch (ClassNotFoundException classNotFoundException) {
/* 113 */       classNotFoundException.printStackTrace();
/* 114 */       newlog.error("自定义转换类（" + str1 + ")在OA环境中未正确部署");
/*     */     } 
/* 116 */     return str3;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String doGetTransSqlValue(String paramString1, String paramString2) {
/*     */     try {
/* 127 */       paramString1 = Util.replace(paramString1, "\\{\\?currentvalue\\}", paramString2, 0, false);
/* 128 */     } catch (Exception exception) {
/* 129 */       exception.printStackTrace();
/*     */     } 
/* 131 */     RecordSet recordSet = new RecordSet();
/* 132 */     recordSet.executeQuery(paramString1, new Object[0]);
/* 133 */     ArrayList<String> arrayList = new ArrayList();
/* 134 */     while (recordSet.next()) {
/* 135 */       if (StringUtils.isNotBlank(recordSet.getString(1))) {
/* 136 */         arrayList.add(recordSet.getString(1));
/*     */       }
/*     */     } 
/* 139 */     String str = arrayList.toString();
/* 140 */     str = str.substring(1, str.length() - 1).replace(", ", ",");
/* 141 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/trans/FieldValueTrans.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */