/*    */ package weaver.integration.syncHrmData.trans.formart.impl;
/*    */ 
/*    */ import weaver.integration.syncHrmData.trans.formart.TransInterface;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FixedTransImpl
/*    */   implements TransInterface
/*    */ {
/*    */   public String getTransdata(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/* 16 */     return paramString5;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/trans/formart/impl/FixedTransImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */