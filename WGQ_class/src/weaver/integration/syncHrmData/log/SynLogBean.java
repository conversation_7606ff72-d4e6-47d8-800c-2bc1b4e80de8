/*    */ package weaver.integration.syncHrmData.log;
/*    */ 
/*    */ public class SynLogBean
/*    */ {
/*    */   private int temprownumber;
/*    */   private int tempcolumn;
/*    */   private int id;
/*    */   private String startdate;
/*    */   private String enddate;
/*    */   private int synresult;
/*    */   private String synoperator;
/*    */   private String syndetail;
/*    */   private int synmethod;
/*    */   private int syntype;
/*    */   
/*    */   public int getTemprownumber() {
/* 17 */     return this.temprownumber;
/*    */   }
/*    */   
/*    */   public void setTemprownumber(int paramInt) {
/* 21 */     this.temprownumber = paramInt;
/*    */   }
/*    */   
/*    */   public int getTempcolumn() {
/* 25 */     return this.tempcolumn;
/*    */   }
/*    */   
/*    */   public void setTempcolumn(int paramInt) {
/* 29 */     this.tempcolumn = paramInt;
/*    */   }
/*    */   
/*    */   public int getId() {
/* 33 */     return this.id;
/*    */   }
/*    */   
/*    */   public void setId(int paramInt) {
/* 37 */     this.id = paramInt;
/*    */   }
/*    */   
/*    */   public String getStartdate() {
/* 41 */     return this.startdate;
/*    */   }
/*    */   
/*    */   public void setStartdate(String paramString) {
/* 45 */     this.startdate = paramString;
/*    */   }
/*    */   
/*    */   public String getEnddate() {
/* 49 */     return this.enddate;
/*    */   }
/*    */   
/*    */   public void setEnddate(String paramString) {
/* 53 */     this.enddate = paramString;
/*    */   }
/*    */   
/*    */   public int getSynresult() {
/* 57 */     return this.synresult;
/*    */   }
/*    */   
/*    */   public void setSynresult(int paramInt) {
/* 61 */     this.synresult = paramInt;
/*    */   }
/*    */   
/*    */   public String getSynoperator() {
/* 65 */     return this.synoperator;
/*    */   }
/*    */   
/*    */   public void setSynoperator(String paramString) {
/* 69 */     this.synoperator = paramString;
/*    */   }
/*    */   
/*    */   public String getSyndetail() {
/* 73 */     return this.syndetail;
/*    */   }
/*    */   
/*    */   public void setSyndetail(String paramString) {
/* 77 */     this.syndetail = paramString;
/*    */   }
/*    */   
/*    */   public int getSynmethod() {
/* 81 */     return this.synmethod;
/*    */   }
/*    */   
/*    */   public void setSynmethod(int paramInt) {
/* 85 */     this.synmethod = paramInt;
/*    */   }
/*    */   
/*    */   public int getSyntype() {
/* 89 */     return this.syntype;
/*    */   }
/*    */   
/*    */   public void setSyntype(int paramInt) {
/* 93 */     this.syntype = paramInt;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/log/SynLogBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */