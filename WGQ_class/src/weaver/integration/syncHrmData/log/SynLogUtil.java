/*     */ package weaver.integration.syncHrmData.log;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.LogMan;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ public class SynLogUtil
/*     */ {
/*  18 */   private static Logger newlog = LoggerFactory.getLogger(SynLogUtil.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int startInitLogAndRtnID(SynLogBean paramSynLogBean) {
/*  26 */     RecordSet recordSet = new RecordSet();
/*  27 */     int i = -1;
/*  28 */     String str = "insert into hrSynLog (startdate,synoperator,syntype,synMethod) values(?,?,?,?)";
/*     */     
/*  30 */     boolean bool = recordSet.executeUpdate(str, new Object[] { paramSynLogBean.getStartdate(), paramSynLogBean.getSynoperator(), Integer.valueOf(paramSynLogBean.getSyntype()), Integer.valueOf(paramSynLogBean.getSynmethod()) });
/*  31 */     if (bool) {
/*  32 */       recordSet.executeQuery("select max(id) from hrSynLog", new Object[0]);
/*  33 */       recordSet.next();
/*  34 */       i = recordSet.getInt(1);
/*  35 */       if (i > 0) {
/*  36 */         recordSet.executeUpdate("update hrSynLog set syndetail=" + i + " where id=" + i, new Object[0]);
/*     */       }
/*     */     } 
/*  39 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean insertDetailBatch(List<SyncLogDetailBean> paramList) {
/*  48 */     ArrayList<ArrayList<String>> arrayList = new ArrayList();
/*  49 */     if (paramList.size() > 0) {
/*  50 */       for (SyncLogDetailBean syncLogDetailBean : paramList) {
/*  51 */         ArrayList<String> arrayList1 = new ArrayList();
/*  52 */         String str1 = Util.null2String(Integer.valueOf(syncLogDetailBean.getSynid()));
/*  53 */         String str2 = Util.null2String(Integer.valueOf(syncLogDetailBean.getSyntype()));
/*  54 */         String str3 = Util.null2String(syncLogDetailBean.getOutpk());
/*  55 */         String str4 = Util.null2String(syncLogDetailBean.getPk());
/*  56 */         String str5 = Util.null2String(syncLogDetailBean.getMemo());
/*  57 */         String str6 = Util.null2String(Integer.valueOf(syncLogDetailBean.getSynstate()));
/*  58 */         String str7 = Util.null2String(syncLogDetailBean.getError());
/*  59 */         String str8 = Util.null2String(Integer.valueOf(syncLogDetailBean.getLanguage()));
/*  60 */         arrayList1.add(str1);
/*  61 */         arrayList1.add(str2);
/*  62 */         arrayList1.add(str3);
/*  63 */         arrayList1.add(str4);
/*  64 */         arrayList1.add(str5);
/*  65 */         arrayList1.add(str6);
/*  66 */         arrayList1.add(str7);
/*  67 */         arrayList1.add(str8);
/*  68 */         arrayList.add(arrayList1);
/*     */       } 
/*     */     }
/*  71 */     RecordSet recordSet = new RecordSet();
/*  72 */     String str = "insert into hrsyndetail (synid,syntype,outpk,pk,memo,synstate,error,language) values(?,?,?,?,?,?,?,?)";
/*  73 */     recordSet.executeBatchSql(str, arrayList);
/*  74 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void WriteLog(Map<String, Object> paramMap, int paramInt) {
/*  84 */     LogMan logMan = LogMan.getInstance();
/*     */     
/*  86 */     ArrayList<HashMap> arrayList = new ArrayList();
/*  87 */     if (paramInt == 1) {
/*     */       try {
/*  89 */         arrayList = (ArrayList)paramMap.get("1");
/*  90 */         if (arrayList.size() > 0) {
/*  91 */           newlog.info("OUTPK码|       公司编码       |       公司名称       |  同步状态");
/*     */         }
/*  93 */         for (byte b = 0; b < arrayList.size(); b++) {
/*  94 */           HashMap hashMap = arrayList.get(b);
/*  95 */           String str = (String)hashMap.get("Success");
/*  96 */           if (str.equals("1") || str.equals("2")) {
/*  97 */             str = "" + SystemEnv.getHtmlLabelName(25008, ThreadVarLanguage.getLang()) + "";
/*     */           } else {
/*  99 */             str = "" + SystemEnv.getHtmlLabelName(498, ThreadVarLanguage.getLang()) + "";
/* 100 */           }  newlog.info((new StringBuilder()).append(hashMap.get("OUTPK")).append(" | ").append(hashMap.get("PK")).append(" | ")
/* 101 */               .append(hashMap.get("Memo")).append(" | ").append(str).toString());
/*     */         } 
/* 103 */       } catch (Exception exception) {
/* 104 */         newlog.error(exception);
/*     */       } 
/* 106 */     } else if (paramInt == 2) {
/*     */       
/*     */       try {
/* 109 */         arrayList = (ArrayList<HashMap>)paramMap.get("2");
/* 110 */         if (arrayList.size() > 0) {
/* 111 */           newlog.info("       OUTPK码        |       部门编码       |       部门名称       |  同步状态");
/*     */         }
/* 113 */         for (byte b = 0; b < arrayList.size(); b++) {
/* 114 */           HashMap hashMap = arrayList.get(b);
/* 115 */           String str = (String)hashMap.get("Success");
/* 116 */           if (str.equals("1") || str.equals("2")) {
/* 117 */             str = "" + SystemEnv.getHtmlLabelName(25008, ThreadVarLanguage.getLang()) + "";
/*     */           } else {
/* 119 */             str = "" + SystemEnv.getHtmlLabelName(498, ThreadVarLanguage.getLang()) + "";
/* 120 */           }  newlog.info((new StringBuilder()).append(hashMap.get("OUTPK")).append(" | ").append(hashMap.get("PK")).append(" | ")
/* 121 */               .append(hashMap.get("Memo")).append(" | ").append(str).toString());
/*     */         } 
/* 123 */       } catch (Exception exception) {}
/*     */     
/*     */     }
/* 126 */     else if (paramInt == 3) {
/*     */       
/*     */       try {
/* 129 */         arrayList = (ArrayList<HashMap>)paramMap.get("3");
/* 130 */         if (arrayList.size() > 0) {
/* 131 */           newlog.info("       OUTPK码        |       岗位编码       |       岗位名称       |  同步状态");
/*     */         }
/* 133 */         for (byte b = 0; b < arrayList.size(); b++) {
/* 134 */           HashMap hashMap = arrayList.get(b);
/* 135 */           String str = (String)hashMap.get("Success");
/* 136 */           if (str.equals("1") || str.equals("2")) {
/* 137 */             str = "" + SystemEnv.getHtmlLabelName(25008, ThreadVarLanguage.getLang()) + "";
/*     */           } else {
/* 139 */             str = "" + SystemEnv.getHtmlLabelName(498, ThreadVarLanguage.getLang()) + "";
/* 140 */           }  newlog.info((new StringBuilder()).append(hashMap.get("OUTPK")).append(" | ").append(hashMap.get("PK")).append(" | ")
/* 141 */               .append(hashMap.get("Memo")).append(" | ").append(str).toString());
/*     */         } 
/* 143 */       } catch (Exception exception) {}
/*     */     
/*     */     }
/* 146 */     else if (paramInt == 4) {
/*     */       
/*     */       try {
/* 149 */         arrayList = (ArrayList<HashMap>)paramMap.get("4");
/* 150 */         if (arrayList.size() > 0) {
/* 151 */           newlog.info("       OUTPK码        |       人员编码       |       人员名称       |  同步状态");
/*     */         }
/* 153 */         for (byte b = 0; b < arrayList.size(); b++) {
/* 154 */           HashMap hashMap = arrayList.get(b);
/* 155 */           String str = (String)hashMap.get("Success");
/* 156 */           if (str.equals("1") || str.equals("2")) {
/* 157 */             str = "" + SystemEnv.getHtmlLabelName(25008, ThreadVarLanguage.getLang()) + "";
/*     */           } else {
/* 159 */             str = "" + SystemEnv.getHtmlLabelName(498, ThreadVarLanguage.getLang()) + "";
/* 160 */           }  newlog.info((new StringBuilder()).append(hashMap.get("OUTPK")).append(" | ").append(hashMap.get("PK")).append(" | ")
/* 161 */               .append(hashMap.get("Memo")).append(" | ").append(str).toString());
/*     */         } 
/* 163 */       } catch (Exception exception) {
/* 164 */         newlog.error(exception);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void WriteLog2File(Map<String, Object> paramMap, int paramInt) {
/* 175 */     SyncLogFileUtil syncLogFileUtil = SyncLogFileUtil.getInstance();
/* 176 */     ArrayList<HashMap> arrayList = new ArrayList();
/*     */     
/* 178 */     if (paramInt == 1) {
/*     */       try {
/* 180 */         arrayList = (ArrayList)paramMap.get("1");
/* 181 */         if (arrayList.size() > 0) {
/* 182 */           syncLogFileUtil.infoLog("OUTPK码|       公司编码       |       公司名称       |  同步状态  |   操作类型   |     错误信息      |  操作时间   |   接口传入数据    |");
/* 183 */           for (byte b = 0; b < arrayList.size(); b++) {
/* 184 */             HashMap hashMap = arrayList.get(b);
/* 185 */             String str1 = (String)hashMap.get("Success");
/* 186 */             if (str1.equals("1") || str1.equals("2")) {
/* 187 */               str1 = "" + SystemEnv.getHtmlLabelName(25008, ThreadVarLanguage.getLang()) + "";
/*     */             } else {
/* 189 */               str1 = "" + SystemEnv.getHtmlLabelName(498, ThreadVarLanguage.getLang()) + "";
/* 190 */             }  String str2 = Util.null2String(hashMap.get("operate"));
/* 191 */             if (str2.equals("1")) {
/* 192 */               str2 = "" + SystemEnv.getHtmlLabelName(33415, ThreadVarLanguage.getLang()) + "";
/*     */             }
/* 194 */             else if (str2.equals("2")) {
/* 195 */               str2 = "" + SystemEnv.getHtmlLabelName(130625, ThreadVarLanguage.getLang()) + "";
/* 196 */             } else if (str2.equals("3")) {
/* 197 */               str2 = "" + SystemEnv.getHtmlLabelName(22151, ThreadVarLanguage.getLang()) + "";
/*     */             } 
/* 199 */             syncLogFileUtil.infoLog((new StringBuilder()).append(hashMap.get("OUTPK")).append(" | ").append(hashMap.get("PK")).append(" | ")
/* 200 */                 .append(hashMap.get("Memo")).append(" | ").append(str1).append("|").append(str2).append("|").append(hashMap.get("errcode")).append("|").append(hashMap.get("operatetime")).append("|").append(hashMap.get("data")).append("|").toString());
/*     */           } 
/*     */         } 
/* 203 */       } catch (Exception exception) {
/* 204 */         syncLogFileUtil.errorLog(exception);
/*     */       } 
/*     */     }
/*     */     
/* 208 */     if (paramInt == 2) {
/*     */       try {
/* 210 */         arrayList = (ArrayList<HashMap>)paramMap.get("2");
/* 211 */         if (arrayList.size() > 0) {
/* 212 */           syncLogFileUtil.infoLog("OUTPK码|       部门编码       |       部门名称       |  同步状态  |   操作类型   |     错误信息      |  操作时间   |   接口传入数据    |");
/* 213 */           for (byte b = 0; b < arrayList.size(); b++) {
/* 214 */             HashMap hashMap = arrayList.get(b);
/* 215 */             String str1 = (String)hashMap.get("Success");
/* 216 */             if (str1.equals("1") || str1.equals("2")) {
/* 217 */               str1 = "" + SystemEnv.getHtmlLabelName(25008, ThreadVarLanguage.getLang()) + "";
/*     */             } else {
/* 219 */               str1 = "" + SystemEnv.getHtmlLabelName(498, ThreadVarLanguage.getLang()) + "";
/* 220 */             }  String str2 = Util.null2String(hashMap.get("operate"));
/* 221 */             if (str2.equals("1")) {
/* 222 */               str2 = "" + SystemEnv.getHtmlLabelName(33415, ThreadVarLanguage.getLang()) + "";
/*     */             }
/* 224 */             else if (str2.equals("2")) {
/* 225 */               str2 = "" + SystemEnv.getHtmlLabelName(130625, ThreadVarLanguage.getLang()) + "";
/* 226 */             } else if (str2.equals("3")) {
/* 227 */               str2 = "" + SystemEnv.getHtmlLabelName(22151, ThreadVarLanguage.getLang()) + "";
/*     */             } 
/* 229 */             syncLogFileUtil.infoLog((new StringBuilder()).append(hashMap.get("OUTPK")).append(" | ").append(hashMap.get("PK")).append(" | ")
/* 230 */                 .append(hashMap.get("Memo")).append(" | ").append(str1).append("|").append(str2).append("|").append(hashMap.get("errcode")).append("|").append(hashMap.get("operatetime")).append("|").append(hashMap.get("data")).append("|").toString());
/*     */           } 
/*     */         } 
/* 233 */       } catch (Exception exception) {
/* 234 */         syncLogFileUtil.errorLog(exception);
/*     */       } 
/*     */     }
/*     */     
/* 238 */     if (paramInt == 3) {
/*     */       try {
/* 240 */         arrayList = (ArrayList<HashMap>)paramMap.get("3");
/* 241 */         if (arrayList.size() > 0) {
/* 242 */           syncLogFileUtil.infoLog("OUTPK码|       岗位编码       |       岗位名称       |  同步状态  |   操作类型   |     错误信息      |  操作时间   |   接口传入数据    |");
/*     */         }
/* 244 */         for (byte b = 0; b < arrayList.size(); b++) {
/* 245 */           HashMap hashMap = arrayList.get(b);
/* 246 */           String str1 = (String)hashMap.get("Success");
/* 247 */           if (str1.equals("1") || str1.equals("2")) {
/* 248 */             str1 = "" + SystemEnv.getHtmlLabelName(25008, ThreadVarLanguage.getLang()) + "";
/*     */           } else {
/* 250 */             str1 = "" + SystemEnv.getHtmlLabelName(498, ThreadVarLanguage.getLang()) + "";
/* 251 */           }  String str2 = Util.null2String(hashMap.get("operate"));
/* 252 */           if (str2.equals("1")) {
/* 253 */             str2 = "" + SystemEnv.getHtmlLabelName(33415, ThreadVarLanguage.getLang()) + "";
/*     */           }
/* 255 */           else if (str2.equals("2")) {
/* 256 */             str2 = "" + SystemEnv.getHtmlLabelName(130625, ThreadVarLanguage.getLang()) + "";
/* 257 */           } else if (str2.equals("3")) {
/* 258 */             str2 = "" + SystemEnv.getHtmlLabelName(22151, ThreadVarLanguage.getLang()) + "";
/*     */           } 
/* 260 */           syncLogFileUtil.infoLog((new StringBuilder()).append(hashMap.get("OUTPK")).append(" | ").append(hashMap.get("PK")).append(" | ")
/* 261 */               .append(hashMap.get("Memo")).append(" | ").append(str1).append("|").append(str2).append("|").append(hashMap.get("errcode")).append("|").append(hashMap.get("operatetime")).append("|").append(hashMap.get("data")).append("|").toString());
/*     */         } 
/* 263 */       } catch (Exception exception) {
/* 264 */         syncLogFileUtil.errorLog(exception);
/*     */       } 
/*     */     }
/*     */     
/* 268 */     if (paramInt == 4)
/*     */       try {
/* 270 */         arrayList = (ArrayList<HashMap>)paramMap.get("4");
/* 271 */         if (arrayList.size() > 0) {
/* 272 */           syncLogFileUtil.infoLog("OUTPK码|       人员编码       |       人员名称       |  同步状态  |   操作类型   |     错误信息      |  操作时间   |   接口传入数据    |");
/*     */         }
/* 274 */         for (byte b = 0; b < arrayList.size(); b++) {
/* 275 */           HashMap hashMap = arrayList.get(b);
/* 276 */           String str1 = (String)hashMap.get("Success");
/* 277 */           if (str1.equals("1") || str1.equals("2")) {
/* 278 */             str1 = "" + SystemEnv.getHtmlLabelName(25008, ThreadVarLanguage.getLang()) + "";
/*     */           } else {
/* 280 */             str1 = "" + SystemEnv.getHtmlLabelName(498, ThreadVarLanguage.getLang()) + "";
/* 281 */           }  String str2 = Util.null2String(hashMap.get("operate"));
/* 282 */           if (str2.equals("1")) {
/* 283 */             str2 = "" + SystemEnv.getHtmlLabelName(33415, ThreadVarLanguage.getLang()) + "";
/*     */           }
/* 285 */           else if (str2.equals("2")) {
/* 286 */             str2 = "" + SystemEnv.getHtmlLabelName(130625, ThreadVarLanguage.getLang()) + "";
/* 287 */           } else if (str2.equals("3")) {
/* 288 */             str2 = "" + SystemEnv.getHtmlLabelName(22151, ThreadVarLanguage.getLang()) + "";
/*     */           } 
/* 290 */           syncLogFileUtil.infoLog((new StringBuilder()).append(hashMap.get("OUTPK")).append(" | ").append(hashMap.get("PK")).append(" | ")
/* 291 */               .append(hashMap.get("Memo")).append(" | ").append(str1).append("|").append(str2).append("|").append(hashMap.get("errcode")).append("|").append(hashMap.get("operatetime")).append("|").append(hashMap.get("data")).append("|").toString());
/*     */         } 
/* 293 */       } catch (Exception exception) {
/* 294 */         syncLogFileUtil.errorLog(exception);
/*     */       }  
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/log/SynLogUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */