/*    */ package weaver.integration.syncHrmData.log;
/*    */ 
/*    */ 
/*    */ public class SyncLogDetailBean
/*    */ {
/*    */   private int id;
/*    */   private int synid;
/*    */   private int syntype;
/*    */   private String outpk;
/*    */   private String pk;
/*    */   private String memo;
/*    */   private int synstate;
/*    */   private String error;
/*    */   private int language;
/*    */   
/*    */   public int getId() {
/* 17 */     return this.id;
/*    */   }
/*    */   
/*    */   public void setId(int paramInt) {
/* 21 */     this.id = paramInt;
/*    */   }
/*    */   
/*    */   public int getSynid() {
/* 25 */     return this.synid;
/*    */   }
/*    */   
/*    */   public void setSynid(int paramInt) {
/* 29 */     this.synid = paramInt;
/*    */   }
/*    */   
/*    */   public int getSyntype() {
/* 33 */     return this.syntype;
/*    */   }
/*    */   
/*    */   public void setSyntype(int paramInt) {
/* 37 */     this.syntype = paramInt;
/*    */   }
/*    */   
/*    */   public String getOutpk() {
/* 41 */     return this.outpk;
/*    */   }
/*    */   
/*    */   public void setOutpk(String paramString) {
/* 45 */     this.outpk = paramString;
/*    */   }
/*    */   
/*    */   public String getPk() {
/* 49 */     return this.pk;
/*    */   }
/*    */   
/*    */   public void setPk(String paramString) {
/* 53 */     this.pk = paramString;
/*    */   }
/*    */   
/*    */   public String getMemo() {
/* 57 */     return this.memo;
/*    */   }
/*    */   
/*    */   public void setMemo(String paramString) {
/* 61 */     this.memo = paramString;
/*    */   }
/*    */   
/*    */   public int getSynstate() {
/* 65 */     return this.synstate;
/*    */   }
/*    */   
/*    */   public void setSynstate(int paramInt) {
/* 69 */     this.synstate = paramInt;
/*    */   }
/*    */   
/*    */   public String getError() {
/* 73 */     return this.error;
/*    */   }
/*    */   
/*    */   public void setError(String paramString) {
/* 77 */     this.error = paramString;
/*    */   }
/*    */   
/*    */   public int getLanguage() {
/* 81 */     return this.language;
/*    */   }
/*    */   
/*    */   public void setLanguage(int paramInt) {
/* 85 */     this.language = paramInt;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/log/SyncLogDetailBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */