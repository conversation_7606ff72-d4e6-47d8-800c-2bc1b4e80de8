/*    */ package weaver.integration.syncHrmData.log;
/*    */ 
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SyncLogFileUtil
/*    */ {
/*    */   private BaseBean baseBean;
/* 14 */   private String moduleName = "hrsync";
/*    */   
/*    */   private static volatile SyncLogFileUtil syncLogFileUtil;
/*    */   
/*    */   private SyncLogFileUtil() {
/* 19 */     this.baseBean = new BaseBean();
/*    */   }
/*    */   
/*    */   public static SyncLogFileUtil getInstance() {
/* 23 */     if (syncLogFileUtil == null) {
/* 24 */       synchronized (SyncLogFileUtil.class) {
/* 25 */         if (syncLogFileUtil == null) {
/* 26 */           syncLogFileUtil = new SyncLogFileUtil();
/*    */         }
/*    */       } 
/*    */     }
/* 30 */     return syncLogFileUtil;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void infoLog(String paramString) {
/* 38 */     this.baseBean.infoLog(this.moduleName, paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void warningLog(String paramString) {
/* 46 */     this.baseBean.warningLog(this.moduleName, paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void debugLog(String paramString) {
/* 54 */     this.baseBean.debugLog(this.moduleName, paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void errorLog(Exception paramException) {
/* 62 */     this.baseBean.errorLog(this.moduleName, paramException);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/log/SyncLogFileUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */