/*     */ package weaver.integration.syncHrmData.util;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.api.hrm.util.ServiceUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.syncHrmData.config.MappingBean;
/*     */ import weaver.integration.syncHrmData.config.SettingConfigBean;
/*     */ import weaver.integration.syncHrmData.config.SettingConfigExecuter;
/*     */ import weaver.integration.syncHrmData.log.SynLogUtil;
/*     */ import weaver.integration.syncHrmData.log.SyncLogDetailBean;
/*     */ import weaver.integration.syncHrmData.output.bean.RecordData;
/*     */ import weaver.integration.syncHrmData.output.subject.impl.HrSyncDepartmentOutputServiceImpl;
/*     */ import weaver.integration.syncHrmData.output.subject.impl.HrSyncJobtitleOutputServiceImpl;
/*     */ import weaver.integration.syncHrmData.output.subject.impl.HrSyncSubcompanyOutputServiceImpl;
/*     */ import weaver.integration.syncHrmData.output.subject.impl.HrSyncUserOutputServiceImpl;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SyncUtil
/*     */ {
/*  34 */   private static Logger newLog = LoggerFactory.getLogger(SyncUtil.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean updateSyncData(String paramString1, String paramString2) {
/*  43 */     String str = "update Hrm_SynTS set Synts=? where id=?";
/*  44 */     RecordSet recordSet = new RecordSet();
/*  45 */     return recordSet.executeUpdate(str, new Object[] { paramString2, paramString1 });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getIncrementDate(String paramString) {
/*  54 */     String str = "select * from Hrm_SynTS where id=?";
/*  55 */     RecordSet recordSet = new RecordSet();
/*  56 */     recordSet.executeQuery(str, new Object[] { paramString });
/*  57 */     if (recordSet.next()) {
/*  58 */       return Util.null2String(recordSet.getString("Synts"));
/*     */     }
/*  60 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Map<String, String> getFeildName(int paramInt1, int paramInt2) {
/*  71 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  72 */     List list = null;
/*  73 */     ServiceUtil serviceUtil = new ServiceUtil();
/*  74 */     if (paramInt1 > 0 && paramInt1 < 5) {
/*  75 */       String str = String.valueOf(paramInt1);
/*  76 */       switch (str) {
/*     */         case "1":
/*  78 */           list = serviceUtil.getCusFieldsData("subcom", "", paramInt2);
/*     */           break;
/*     */         case "2":
/*  81 */           list = serviceUtil.getCusFieldsData("dept", "", paramInt2);
/*     */           break;
/*     */         case "3":
/*  84 */           list = serviceUtil.getCusFieldsData("jobtitle", "", paramInt2);
/*     */           break;
/*     */         
/*     */         case "4":
/*  88 */           list = serviceUtil.getCusFieldsData("hrm", "", paramInt2);
/*     */           break;
/*     */         default:
/*  91 */           list = new ArrayList();
/*     */           break;
/*     */       } 
/*  94 */       for (Map map : list) {
/*  95 */         String str1 = (String)map.get("fieldname");
/*  96 */         String str2 = (String)map.get("labelname");
/*  97 */         if ("2".equals(map.get("issystem"))) {
/*  98 */           str1 = "#cus#" + (String)map.get("fieldname");
/*     */         }
/* 100 */         hashMap.put(str1, str2);
/*     */       } 
/*     */     } 
/* 103 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static User getUser() {
/* 111 */     RecordSet recordSet = new RecordSet();
/* 112 */     recordSet.executeSql("select * from hrmresourcemanager where loginid='sysadmin'");
/* 113 */     recordSet.next();
/* 114 */     User user = new User();
/* 115 */     user.setLoginid("sysadmin");
/* 116 */     user.setLastname(recordSet.getString("lastname"));
/* 117 */     user.setLoginid(recordSet.getString("systemlanguage"));
/* 118 */     return user;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static SettingConfigBean getSetting() {
/* 126 */     String str1 = "select id from hrsyncset";
/* 127 */     RecordSet recordSet = new RecordSet();
/* 128 */     recordSet.executeQuery(str1, new Object[0]);
/* 129 */     String str2 = "1";
/* 130 */     if (recordSet.next()) {
/* 131 */       str2 = Util.null2String(recordSet.getString("id"), "1");
/*     */     }
/*     */     try {
/* 134 */       SettingConfigExecuter settingConfigExecuter = new SettingConfigExecuter();
/* 135 */       return settingConfigExecuter.getConfig(str2);
/*     */     }
/* 137 */     catch (Exception exception) {
/* 138 */       newLog.error("获取HR配置出现异常：", exception);
/* 139 */       return new SettingConfigBean();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Map<String, Object> handleResult(Map<String, Object> paramMap1, MappingBean paramMappingBean, List<Map<String, Object>> paramList, String paramString, int paramInt1, int paramInt2, Map<String, Object> paramMap2) {
/* 150 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 151 */     String str = "";
/* 152 */     switch (paramString) {
/*     */       
/*     */       case "1":
/* 155 */         str = "subcompanyname";
/*     */         break;
/*     */       
/*     */       case "2":
/* 159 */         str = "departmentname";
/*     */         break;
/*     */       
/*     */       case "3":
/* 163 */         str = "jobtitlename";
/*     */         break;
/*     */       
/*     */       case "4":
/* 167 */         str = "lastname";
/*     */         break;
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 173 */     ArrayList<SyncLogDetailBean> arrayList1 = new ArrayList();
/* 174 */     for (byte b = 0; b < paramList.size(); b++) {
/* 175 */       Map map = paramList.get(b);
/* 176 */       String str1 = Util.null2String(map.get(paramMappingBean.getKey_oa()));
/* 177 */       String str2 = "";
/* 178 */       String str3 = Util.null2String(map.get(str));
/*     */       
/* 180 */       String str4 = Util.null2String(paramMap2.get("Success"));
/* 181 */       String str5 = Util.null2String(paramMap2.get("ErrorMessage"));
/*     */       
/* 183 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 184 */       hashMap.put("OUTPK", str1);
/* 185 */       hashMap.put("PK", str2);
/* 186 */       hashMap.put("Memo", str3);
/* 187 */       hashMap.put("Success", str4);
/* 188 */       arrayList.add(hashMap);
/*     */       
/* 190 */       SyncLogDetailBean syncLogDetailBean = new SyncLogDetailBean();
/* 191 */       syncLogDetailBean.setSynid(paramInt1);
/* 192 */       syncLogDetailBean.setSyntype(Util.getIntValue(paramString));
/* 193 */       syncLogDetailBean.setOutpk(str1);
/* 194 */       syncLogDetailBean.setPk(str2);
/* 195 */       syncLogDetailBean.setMemo(str3);
/* 196 */       syncLogDetailBean.setSynstate(Util.getIntValue(str4));
/* 197 */       syncLogDetailBean.setError(str5);
/* 198 */       syncLogDetailBean.setLanguage(paramInt2);
/* 199 */       arrayList1.add(syncLogDetailBean);
/*     */     } 
/*     */     
/* 202 */     SynLogUtil.insertDetailBatch(arrayList1);
/* 203 */     paramMap1.put(paramString, arrayList);
/* 204 */     return paramMap1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Map<String, Object> handleResult2File(Map<String, Object> paramMap1, MappingBean paramMappingBean, List<Map<String, Object>> paramList, String paramString, int paramInt1, int paramInt2, Map<String, Object> paramMap2) {
/* 217 */     if ("1".equals(paramMap2.get("status"))) {
/* 218 */       ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 219 */       String str1 = "";
/* 220 */       String str2 = "";
/* 221 */       switch (paramString) {
/*     */         
/*     */         case "1":
/* 224 */           str1 = "subcompanyname";
/* 225 */           str2 = "HrmSubcompany";
/*     */           break;
/*     */         
/*     */         case "2":
/* 229 */           str1 = "departmentname";
/* 230 */           str2 = "HrmDepartment";
/*     */           break;
/*     */         
/*     */         case "3":
/* 234 */           str1 = "jobtitlename";
/* 235 */           str2 = "HrmJobtitles";
/*     */           break;
/*     */         
/*     */         case "4":
/* 239 */           str1 = "lastname";
/* 240 */           str2 = "HrmResource";
/*     */           break;
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 246 */       List<Map> list = (List)paramMap2.get("backdata");
/*     */       
/* 248 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 249 */       String str3 = "select " + paramMappingBean.getKey_oa() + ",id from " + str2;
/* 250 */       RecordSet recordSet = new RecordSet();
/* 251 */       recordSet.executeQuery(str3, new Object[0]);
/* 252 */       while (recordSet.next()) {
/* 253 */         hashMap.put(recordSet.getString(paramMappingBean.getKey_oa()), recordSet.getString("id"));
/*     */       }
/* 255 */       ArrayList<SyncLogDetailBean> arrayList1 = new ArrayList();
/* 256 */       byte b = 0;
/* 257 */       for (Map<String, Object> map : paramList) {
/*     */         
/* 259 */         String str4 = Util.null2String(map.get(paramMappingBean.getKey_oa()));
/* 260 */         String str5 = Util.null2String((String)hashMap.get(str4));
/* 261 */         String str6 = Util.null2String(map.get(str1));
/*     */         
/* 263 */         Map<Object, Object> map1 = list.get(b);
/* 264 */         if (map1 == null) {
/* 265 */           map1 = new HashMap<>();
/* 266 */           map1.put("errcode", "接口无对应返回值");
/*     */         } 
/* 268 */         String str7 = Util.null2String(map1.get("status"));
/* 269 */         if ("1".equals(str7))
/* 270 */         { str7 = Util.null2String(map1.get("operate")); }
/* 271 */         else { str7 = "0"; }
/* 272 */          String str8 = Util.null2String(map1.get("errcode"));
/* 273 */         String str9 = Util.null2String(map1.get("operatetime"));
/* 274 */         String str10 = JSON.toJSONString(map);
/* 275 */         String str11 = Util.null2String(map1.get("operate"));
/*     */         
/* 277 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 278 */         hashMap1.put("OUTPK", str4);
/* 279 */         hashMap1.put("PK", str5);
/* 280 */         hashMap1.put("Memo", str6);
/* 281 */         hashMap1.put("Success", str7);
/* 282 */         hashMap1.put("errcode", str8);
/* 283 */         hashMap1.put("operateTime", str9);
/* 284 */         hashMap1.put("data", str10);
/* 285 */         hashMap1.put("operate", str11);
/* 286 */         arrayList.add(hashMap1);
/*     */         
/* 288 */         SyncLogDetailBean syncLogDetailBean = new SyncLogDetailBean();
/* 289 */         syncLogDetailBean.setSynid(paramInt1);
/* 290 */         syncLogDetailBean.setSyntype(Util.getIntValue(paramString));
/* 291 */         syncLogDetailBean.setOutpk(str4);
/* 292 */         syncLogDetailBean.setPk(str5);
/* 293 */         syncLogDetailBean.setMemo(str6);
/* 294 */         syncLogDetailBean.setSynstate(Util.getIntValue(str7));
/* 295 */         syncLogDetailBean.setError(str8);
/* 296 */         syncLogDetailBean.setLanguage(paramInt2);
/* 297 */         arrayList1.add(syncLogDetailBean);
/* 298 */         b++;
/*     */ 
/*     */         
/* 301 */         if (str7.equals("1") || str7.equals("2")) {
/* 302 */           if (str11.equals("1")) {
/* 303 */             str11 = "insert";
/*     */           }
/* 305 */           else if (str11.equals("2")) {
/* 306 */             str11 = "update";
/* 307 */           } else if (str11.equals("3")) {
/*     */             
/* 309 */             str11 = "cancled";
/*     */           } 
/* 311 */           RecordData recordData = new RecordData(str11, paramMappingBean.getKey_oa(), str4);
/*     */           
/* 313 */           thirdsdkSync(paramString, recordData);
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 318 */       SynLogUtil.insertDetailBatch(arrayList1);
/* 319 */       if (paramMap1.containsKey(paramString)) {
/* 320 */         ArrayList<HashMap<Object, Object>> arrayList2 = (ArrayList)paramMap1.get(paramString);
/* 321 */         arrayList2.addAll(arrayList);
/*     */       } else {
/* 323 */         paramMap1.put(paramString, arrayList);
/* 324 */       }  return paramMap1;
/*     */     } 
/* 326 */     newLog.error("类型:" + paramString + " 同步失败：" + JSON.toJSONString(paramMap2));
/* 327 */     return paramMap1;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static void thirdsdkSync(String paramString, RecordData paramRecordData) {
/*     */     HrSyncSubcompanyOutputServiceImpl hrSyncSubcompanyOutputServiceImpl;
/*     */     HrSyncDepartmentOutputServiceImpl hrSyncDepartmentOutputServiceImpl;
/*     */     HrSyncJobtitleOutputServiceImpl hrSyncJobtitleOutputServiceImpl;
/*     */     HrSyncUserOutputServiceImpl hrSyncUserOutputServiceImpl;
/* 337 */     switch (paramString) {
/*     */       
/*     */       case "1":
/* 340 */         hrSyncSubcompanyOutputServiceImpl = new HrSyncSubcompanyOutputServiceImpl();
/* 341 */         hrSyncSubcompanyOutputServiceImpl.save(paramRecordData);
/*     */         break;
/*     */       
/*     */       case "2":
/* 345 */         hrSyncDepartmentOutputServiceImpl = new HrSyncDepartmentOutputServiceImpl();
/* 346 */         hrSyncDepartmentOutputServiceImpl.save(paramRecordData);
/*     */         break;
/*     */       
/*     */       case "3":
/* 350 */         hrSyncJobtitleOutputServiceImpl = new HrSyncJobtitleOutputServiceImpl();
/* 351 */         hrSyncJobtitleOutputServiceImpl.save(paramRecordData);
/*     */         break;
/*     */       
/*     */       case "4":
/* 355 */         hrSyncUserOutputServiceImpl = new HrSyncUserOutputServiceImpl();
/* 356 */         hrSyncUserOutputServiceImpl.save(paramRecordData);
/*     */         break;
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/util/SyncUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */