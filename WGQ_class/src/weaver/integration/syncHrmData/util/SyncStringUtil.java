/*    */ package weaver.integration.syncHrmData.util;
/*    */ 
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ 
/*    */ public class SyncStringUtil
/*    */ {
/*  8 */   private static Logger newlog = LoggerFactory.getLogger(SyncStringUtil.class);
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static String getTransqlMapKey(String paramString1, String paramString2) {
/* 21 */     return paramString1.toLowerCase() + "-" + paramString2;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/util/SyncStringUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */