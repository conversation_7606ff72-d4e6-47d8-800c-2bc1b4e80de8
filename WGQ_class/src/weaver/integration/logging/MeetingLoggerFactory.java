/*    */ package weaver.integration.logging;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class MeetingLoggerFactory
/*    */ {
/*    */   private static final String loggerName = "meeting";
/*    */   
/*    */   public static Logger getLogger(String paramString1, String paramString2) {
/* 13 */     if ("".equals(paramString1))
/* 14 */       paramString1 = "meeting"; 
/* 15 */     Log4JLogger log4JLogger = new Log4JLogger();
/* 16 */     log4JLogger.setClassname(paramString2);
/* 17 */     log4JLogger.init(paramString1);
/* 18 */     return log4JLogger;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static Logger getLogger(Class paramClass) {
/* 27 */     return getLogger("meeting", paramClass.getCanonicalName());
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static Logger getLogger(String paramString) {
/* 36 */     return getLogger("meeting", paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static Logger getLogger() {
/* 43 */     String str = Thread.currentThread().getStackTrace()[2].getClassName();
/* 44 */     return getLogger("meeting", str);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/logging/MeetingLoggerFactory.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */