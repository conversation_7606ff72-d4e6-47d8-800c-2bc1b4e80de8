/*    */ package weaver.integration.logging;
/*    */ 
/*    */ import org.apache.log4j.Logger;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Log4JLogger
/*    */   implements Logger
/*    */ {
/*    */   private Logger log;
/*    */   private String classname;
/*    */   
/*    */   public String getClassname() {
/* 20 */     return this.classname;
/*    */   }
/*    */   
/*    */   public void setClassname(String paramString) {
/* 24 */     this.classname = paramString;
/*    */   }
/*    */   
/*    */   public boolean isDebugEnabled() {
/* 28 */     return this.log.isDebugEnabled();
/*    */   }
/*    */   
/*    */   public boolean isInfoEnabled() {
/* 32 */     return this.log.isInfoEnabled();
/*    */   }
/*    */   
/*    */   public void debug(Object paramObject) {
/* 36 */     String str = Thread.currentThread().getStackTrace()[2].getMethodName();
/* 37 */     this.log.debug(this.classname + "." + str + "() - " + paramObject);
/*    */   }
/*    */   
/*    */   public void debug(Object paramObject, Throwable paramThrowable) {
/* 41 */     String str = Thread.currentThread().getStackTrace()[2].getMethodName();
/* 42 */     this.log.debug(this.classname + "." + str + "() - " + paramObject, paramThrowable);
/*    */   }
/*    */   
/*    */   public void info(Object paramObject) {
/* 46 */     String str = Thread.currentThread().getStackTrace()[2].getMethodName();
/* 47 */     this.log.info(this.classname + "." + str + "() - " + paramObject);
/*    */   }
/*    */   
/*    */   public void info(Object paramObject, Throwable paramThrowable) {
/* 51 */     String str = Thread.currentThread().getStackTrace()[2].getMethodName();
/* 52 */     this.log.info(this.classname + "." + str + "() - " + paramObject, paramThrowable);
/*    */   }
/*    */   
/*    */   public void warn(Object paramObject) {
/* 56 */     String str = Thread.currentThread().getStackTrace()[2].getMethodName();
/* 57 */     this.log.warn(this.classname + "." + str + "() - " + paramObject);
/*    */   }
/*    */   
/*    */   public void warn(Object paramObject, Throwable paramThrowable) {
/* 61 */     String str = Thread.currentThread().getStackTrace()[2].getMethodName();
/* 62 */     this.log.warn(this.classname + "." + str + "() - " + paramObject, paramThrowable);
/*    */   }
/*    */   
/*    */   public void error(Object paramObject) {
/* 66 */     String str = Thread.currentThread().getStackTrace()[2].getMethodName();
/* 67 */     this.log.error(this.classname + "." + str + "() - " + paramObject);
/*    */   }
/*    */   
/*    */   public void error(Object paramObject, Throwable paramThrowable) {
/* 71 */     String str = Thread.currentThread().getStackTrace()[2].getMethodName();
/* 72 */     this.log.error(this.classname + "." + str + "() - " + paramObject, paramThrowable);
/*    */   }
/*    */   
/*    */   public void init(String paramString) {
/* 76 */     if ("".equals(paramString))
/* 77 */       paramString = "integration"; 
/* 78 */     this.log = Logger.getLogger(paramString);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/logging/Log4JLogger.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */