/*    */ package weaver.integration.logging;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ScheduleLoggerFactory
/*    */ {
/*    */   private static final String typeName = "schedule";
/*    */   private static final String loggerName = "integration_schedule";
/*    */   
/*    */   public static Logger getLogger(String paramString1, String paramString2) {
/* 16 */     if ("".equals(paramString1))
/* 17 */       paramString1 = "integration_schedule"; 
/* 18 */     Log4JLogger log4JLogger = new Log4JLogger();
/* 19 */     log4JLogger.setClassname(paramString2);
/* 20 */     boolean bool = getIsOutPut();
/* 21 */     if (!bool) {
/* 22 */       paramString1 = paramString1 + "_off";
/*    */     }
/* 24 */     log4JLogger.init(paramString1);
/* 25 */     return log4JLogger;
/*    */   }
/*    */   
/*    */   private static boolean getIsOutPut() {
/* 29 */     RecordSet recordSet = new RecordSet();
/* 30 */     String str1 = "";
/* 31 */     String str2 = "select schedule from integrationlogsetting";
/* 32 */     recordSet.executeQuery(str2, new Object[0]);
/* 33 */     if (recordSet.next()) {
/* 34 */       str1 = Util.null2String(recordSet.getString(1));
/*    */     }
/* 36 */     return "1".equals(str1);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static Logger getLogger(Class paramClass) {
/* 45 */     return getLogger("integration_schedule", paramClass.getCanonicalName());
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static Logger getLogger(String paramString) {
/* 54 */     return getLogger("integration_schedule", paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static Logger getLogger() {
/* 62 */     String str = Thread.currentThread().getStackTrace()[2].getClassName();
/* 63 */     return getLogger("integration_schedule", str);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/logging/ScheduleLoggerFactory.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */