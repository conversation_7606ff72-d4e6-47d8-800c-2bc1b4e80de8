/*    */ package weaver.integration.logging;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class LoggerFactory
/*    */ {
/*    */   private static final String loggerName = "integration";
/*    */   
/*    */   public static Logger getLogger(String paramString1, String paramString2) {
/* 15 */     if ("".equals(paramString1))
/* 16 */       paramString1 = "integration"; 
/* 17 */     Log4JLogger log4JLogger = new Log4JLogger();
/* 18 */     log4JLogger.setClassname(paramString2);
/* 19 */     log4JLogger.init(paramString1);
/* 20 */     return log4JLogger;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static Logger getLogger(Class paramClass) {
/* 29 */     return getLogger("integration", paramClass.getCanonicalName());
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static Logger getLogger(String paramString) {
/* 38 */     return getLogger("integration", paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static Logger getLogger() {
/* 46 */     String str = Thread.currentThread().getStackTrace()[2].getClassName();
/* 47 */     return getLogger("integration", str);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/logging/LoggerFactory.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */