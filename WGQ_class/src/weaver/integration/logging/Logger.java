package weaver.integration.logging;

public interface Logger {
  boolean isDebugEnabled();
  
  void debug(Object paramObject);
  
  void debug(Object paramObject, Throwable paramThrowable);
  
  boolean isInfoEnabled();
  
  void info(Object paramObject);
  
  void info(Object paramObject, Throwable paramThrowable);
  
  void warn(Object paramObject);
  
  void warn(Object paramObject, Throwable paramThrowable);
  
  void error(Object paramObject);
  
  void error(Object paramObject, Throwable paramThrowable);
  
  String getClassname();
  
  void setClassname(String paramString);
  
  void init(String paramString);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/logging/Logger.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */