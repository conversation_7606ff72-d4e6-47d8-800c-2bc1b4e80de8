/*     */ package weaver.integration.mail.util;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.Calendar;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.interfaces.schedule.BaseCronJob;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MailScheduleLog
/*     */   extends BaseCronJob
/*     */ {
/*  21 */   private static final Logger log = LoggerFactory.getLogger(MailScheduleLog.class);
/*     */ 
/*     */ 
/*     */   
/*     */   public void execute() {
/*  26 */     log.info("执行邮箱日志清理计划任务");
/*     */     
/*  28 */     HashMap<String, String> hashMap = getSetting();
/*  29 */     log.info(String.format("日志清理设置: %s", new Object[] { JSON.toJSONString(hashMap) }));
/*  30 */     String str1 = hashMap.get("cleanStatus");
/*  31 */     String str2 = hashMap.get("cleanType");
/*  32 */     String str3 = hashMap.get("cleanCustomValue");
/*     */     
/*  34 */     if (StringUtils.isEmpty(str1) || StringUtils.isEmpty(str2)) {
/*  35 */       log.error("参数cleanStatus或cleanType为空");
/*     */       
/*     */       return;
/*     */     } 
/*  39 */     if ("0".equals(str1)) {
/*  40 */       log.error("日志清理已关闭");
/*     */       
/*     */       return;
/*     */     } 
/*  44 */     if ("4".equals(str2) && StringUtils.isEmpty(str3)) {
/*  45 */       log.error("cleanCustomValue参数为空");
/*     */       
/*     */       return;
/*     */     } 
/*  49 */     cleanLog(str2, str3);
/*     */     
/*  51 */     log.info("执行邮箱日志清理计划任务结束");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public HashMap<String, String> getSetting() {
/*  60 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  61 */     RecordSet recordSet = new RecordSet();
/*  62 */     String str = " select * from mail_log_setting ";
/*  63 */     boolean bool = recordSet.executeQuery(str, new Object[0]);
/*  64 */     if (bool && recordSet.next()) {
/*  65 */       String str1 = Util.null2String(recordSet.getString("cleanStatus"));
/*  66 */       String str2 = Util.null2String(recordSet.getString("cleanType"));
/*  67 */       String str3 = Util.null2String(recordSet.getString("cleanCustomValue"));
/*     */       
/*  69 */       hashMap.put("cleanStatus", str1);
/*  70 */       hashMap.put("cleanType", str2);
/*  71 */       hashMap.put("cleanCustomValue", str3);
/*     */     } 
/*  73 */     return (HashMap)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void cleanLog(String paramString1, String paramString2) {
/*  83 */     String str = getDate(paramString1, paramString2);
/*  84 */     if (StringUtils.isEmpty(str)) {
/*  85 */       log.error("计算日期为空");
/*     */       
/*     */       return;
/*     */     } 
/*  89 */     RecordSet recordSet = new RecordSet();
/*     */     try {
/*  91 */       String str1 = " delete from integrationmailog where logdate < ? ";
/*  92 */       recordSet.executeUpdate(str1, new Object[] { str });
/*  93 */     } catch (Exception exception) {
/*  94 */       log.error("清理日志出错", exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getDate(String paramString1, String paramString2) {
/* 106 */     int i = 0;
/* 107 */     if ("1".equals(paramString1)) {
/* 108 */       i = 30;
/* 109 */     } else if ("2".equals(paramString1)) {
/* 110 */       i = 90;
/* 111 */     } else if ("3".equals(paramString1)) {
/* 112 */       i = 180;
/* 113 */     } else if ("4".equals(paramString1)) {
/*     */       try {
/* 115 */         i = Integer.parseInt(paramString2);
/* 116 */       } catch (Exception exception) {
/* 117 */         return null;
/*     */       } 
/*     */       
/* 120 */       if (i <= 0) {
/* 121 */         return null;
/*     */       }
/*     */     } else {
/* 124 */       return null;
/*     */     } 
/*     */     
/* 127 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
/* 128 */     Calendar calendar = Calendar.getInstance();
/* 129 */     calendar.setTime(new Date());
/* 130 */     int j = calendar.get(5);
/*     */     
/* 132 */     calendar.set(5, j - i);
/*     */     
/* 134 */     return simpleDateFormat.format(calendar.getTime());
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/mail/util/MailScheduleLog.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */