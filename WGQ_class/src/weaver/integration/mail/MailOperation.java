/*    */ package weaver.integration.mail;
/*    */ 
/*    */ import com.engine.integration.dao.MailDao;
/*    */ import java.util.Map;
/*    */ import weaver.hrm.User;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ 
/*    */ 
/*    */ public interface MailOperation
/*    */ {
/* 12 */   public static final Logger newlog = LoggerFactory.getLogger(MailOperation.class);
/*    */   
/*    */   public static final String HASACCOUNT = "hasAccount";
/*    */   
/*    */   public static final String ISOPENMAIL = "isOpenMail";
/*    */   
/*    */   public static final String ERRMSGTIP = "errmsgTip";
/*    */   
/*    */   public static final String SUCCESS = "success";
/*    */   
/*    */   public static final String ISERROR = "isError";
/*    */   
/*    */   public static final String MESSAGECODE = "messageCode";
/*    */   public static final String ERRCODE = "errcode";
/*    */   public static final String CODE1 = "001";
/*    */   public static final String CODE2 = "002";
/*    */   public static final String CODE3 = "003";
/* 29 */   public static final MailDao maildao = new MailDao();
/*    */   public static final String COUNT = "count";
/*    */   public static final String UNREADLIST = "unReadList";
/*    */   public static final String DATE = "date";
/*    */   public static final String FROM = "from";
/*    */   public static final String SUBJECT = "subject";
/*    */   public static final String URL = "url";
/*    */   public static final String LOGINURL = "loginUrl";
/*    */   public static final String NAME = "name";
/*    */   
/*    */   Map<String, Object> checkMailAccountExists(User paramUser, Map<String, Object> paramMap1, Map<String, Object> paramMap2);
/*    */   
/*    */   Map<String, Object> testMail(int paramInt, Map<String, Object> paramMap);
/*    */   
/*    */   Map<String, Object> initOrgAndUser(User paramUser, Map<String, Object> paramMap1, Map<String, Object> paramMap2);
/*    */   
/*    */   Map<String, Object> getMailUnReadCount(User paramUser, Map<String, Object> paramMap1, Map<String, Object> paramMap2);
/*    */   
/*    */   Map<String, Object> getMailUnReadList(User paramUser, Map<String, Object> paramMap1, Map<String, Object> paramMap2);
/*    */   
/*    */   Map<String, Object> getMailLoginUrlAndName(Map<String, Object> paramMap1, Map<String, Object> paramMap2);
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/mail/MailOperation.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */