/*     */ package weaver.integration.mail.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.integration.mail.MailOperation;
/*     */ import weaver.integration.thirdsdk.qqmail.constant.MessageCode;
/*     */ import weaver.integration.thirdsdk.wymail.api.WYMailApi;
/*     */ import weaver.integration.thirdsdk.wymail.api.WYMailSynApi;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WYMailOperationImpl
/*     */   implements MailOperation
/*     */ {
/*     */   public Map<String, Object> checkMailAccountExists(User paramUser, Map<String, Object> paramMap1, Map<String, Object> paramMap2) {
/*  26 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  27 */     String str1 = Util.null2String(paramMap1.get("bindfield"));
/*  28 */     String str2 = maildao.getBindFieldValue(str1, paramUser.getUID() + "");
/*  29 */     boolean bool1 = false;
/*  30 */     boolean bool2 = false;
/*  31 */     boolean bool3 = false;
/*  32 */     String str3 = "";
/*  33 */     if ("1".equals(paramMap1.get("isuse"))) {
/*  34 */       bool1 = true;
/*     */     }
/*     */     
/*  37 */     String str4 = "";
/*  38 */     String str5 = "";
/*  39 */     String str6 = "";
/*  40 */     String str7 = "";
/*  41 */     for (String str : paramMap2.keySet()) {
/*  42 */       if ("email163_domain".equals(str)) {
/*  43 */         str4 = paramMap2.get(str).toString(); continue;
/*  44 */       }  if ("email163_product".equals(str)) {
/*  45 */         str5 = paramMap2.get(str).toString(); continue;
/*  46 */       }  if ("email163_key".equals(str)) {
/*  47 */         str6 = paramMap2.get(str).toString(); continue;
/*  48 */       }  if ("email163_url".equals(str)) {
/*  49 */         str7 = paramMap2.get(str).toString();
/*     */       }
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  57 */     JSONObject jSONObject = WYMailApi.testWY(str4, str5, str6, str7);
/*  58 */     if (!jSONObject.getBoolean("suc").booleanValue()) {
/*     */       
/*  60 */       String str = jSONObject.getString("error_code");
/*  61 */       str3 = str;
/*  62 */       bool3 = true;
/*     */     } 
/*  64 */     if (paramUser != null && str2 != null && !"".equals(str2)) {
/*     */       
/*  66 */       Map map = WYMailApi.checkUser(str2, str4, str5, str6, str7);
/*  67 */       if (((Boolean)map.get("checkUser")).booleanValue()) {
/*  68 */         bool2 = true;
/*     */       } else {
/*  70 */         str3 = SystemEnv.getHtmlLabelNames("125018", paramUser.getLanguage());
/*  71 */         bool3 = true;
/*     */       } 
/*     */     } else {
/*     */       
/*  75 */       str3 = SystemEnv.getHtmlLabelNames("125018", paramUser.getLanguage());
/*  76 */       bool3 = true;
/*     */     } 
/*     */     
/*  79 */     hashMap.put("hasAccount", Boolean.valueOf(bool2));
/*  80 */     hashMap.put("isOpenMail", Boolean.valueOf(bool1));
/*  81 */     hashMap.put("errmsgTip", str3);
/*  82 */     hashMap.put("isError", Boolean.valueOf(bool3));
/*  83 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> testMail(int paramInt, Map<String, Object> paramMap) {
/*  88 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  89 */     String str1 = "";
/*  90 */     String str2 = "";
/*  91 */     String str3 = "";
/*  92 */     String str4 = "";
/*  93 */     for (String str : paramMap.keySet()) {
/*  94 */       if ("email163_domain".equals(str)) {
/*  95 */         str1 = paramMap.get(str).toString(); continue;
/*  96 */       }  if ("email163_product".equals(str)) {
/*  97 */         str2 = paramMap.get(str).toString(); continue;
/*  98 */       }  if ("email163_key".equals(str)) {
/*  99 */         str3 = paramMap.get(str).toString(); continue;
/* 100 */       }  if ("email163_url".equals(str)) {
/* 101 */         str4 = paramMap.get(str).toString();
/*     */       }
/*     */     } 
/* 104 */     String str5 = "";
/*     */     try {
/* 106 */       JSONObject jSONObject = WYMailApi.testWY(str1, str2, str3, str4);
/* 107 */       if (jSONObject.getBoolean("suc").booleanValue()) {
/*     */         
/* 109 */         str5 = "success";
/*     */       } else {
/* 111 */         str5 = jSONObject.getString("error_code");
/*     */       } 
/* 113 */     } catch (Exception exception) {
/* 114 */       str5 = SystemEnv.getHtmlLabelName(385008, paramInt);
/*     */     } 
/*     */     
/* 117 */     if (!"".equalsIgnoreCase(str5)) {
/* 118 */       if (!"success".equals(str5)) {
/* 119 */         hashMap.put("errmsgTip", str5);
/* 120 */         hashMap.put("isError", Boolean.valueOf(true));
/*     */       } else {
/* 122 */         hashMap.put("errmsgTip", SystemEnv.getHtmlLabelName(32297, paramInt));
/*     */       } 
/*     */     }
/* 125 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> initOrgAndUser(User paramUser, Map<String, Object> paramMap1, Map<String, Object> paramMap2) {
/* 130 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 131 */     if ("0".equals(paramMap1.get("isuse"))) {
/* 132 */       hashMap.put("messageCode", "001");
/* 133 */       return (Map)hashMap;
/* 134 */     }  if ("0".equals(paramMap1.get("issync"))) {
/* 135 */       hashMap.put("messageCode", "002");
/* 136 */       return (Map)hashMap;
/*     */     } 
/*     */     
/* 139 */     String str1 = "";
/* 140 */     String str2 = "";
/* 141 */     String str3 = "";
/* 142 */     String str4 = "";
/* 143 */     for (String str : paramMap2.keySet()) {
/* 144 */       if ("email163_domain".equals(str)) {
/* 145 */         str1 = paramMap2.get(str).toString(); continue;
/* 146 */       }  if ("email163_product".equals(str)) {
/* 147 */         str2 = paramMap2.get(str).toString(); continue;
/* 148 */       }  if ("email163_key".equals(str)) {
/* 149 */         str3 = paramMap2.get(str).toString(); continue;
/* 150 */       }  if ("email163_url".equals(str)) {
/* 151 */         str4 = paramMap2.get(str).toString();
/*     */       }
/*     */     } 
/*     */     
/*     */     try {
/* 156 */       JSONObject jSONObject = WYMailApi.testWY(str1, str2, str3, str4);
/* 157 */       if (!jSONObject.getBoolean("suc").booleanValue()) {
/* 158 */         hashMap.put("messageCode", "003");
/* 159 */         return (Map)hashMap;
/*     */       } 
/* 161 */     } catch (Exception exception) {
/* 162 */       hashMap.put("messageCode", "003");
/* 163 */       return (Map)hashMap;
/*     */     } 
/*     */     try {
/* 166 */       WYMailSynApi wYMailSynApi = WYMailSynApi.getInstance(paramUser, paramMap1, paramMap2);
/* 167 */       wYMailSynApi.initOrgAndUser(paramUser);
/* 168 */     } catch (Exception exception) {
/* 169 */       newlog.error("OA组织架构和人员初始化到阿里邮箱，出现异常：", exception);
/*     */     } 
/* 171 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMailUnReadCount(User paramUser, Map<String, Object> paramMap1, Map<String, Object> paramMap2) {
/* 177 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 178 */     String str1 = "0";
/* 179 */     String str2 = "";
/* 180 */     String str3 = Util.null2String(paramMap1.get("bindfield"));
/* 181 */     String str4 = maildao.getBindFieldValue(str3, paramUser.getUID() + "");
/* 182 */     boolean bool = false;
/*     */     
/* 184 */     if (!"1".equals(Util.null2String(paramMap1.get("isuse")))) {
/* 185 */       str2 = SystemEnv.getHtmlLabelNames("32386", paramUser.getLanguage());
/* 186 */       bool = true;
/* 187 */       hashMap.put("errmsgTip", str2);
/* 188 */       hashMap.put("isError", Boolean.valueOf(bool));
/* 189 */       return (Map)hashMap;
/*     */     } 
/* 191 */     String str5 = "";
/* 192 */     String str6 = "";
/* 193 */     String str7 = "";
/* 194 */     String str8 = "";
/* 195 */     for (String str : paramMap2.keySet()) {
/* 196 */       if ("email163_domain".equals(str)) {
/* 197 */         str5 = paramMap2.get(str).toString(); continue;
/* 198 */       }  if ("email163_product".equals(str)) {
/* 199 */         str6 = paramMap2.get(str).toString(); continue;
/* 200 */       }  if ("email163_key".equals(str)) {
/* 201 */         str7 = paramMap2.get(str).toString(); continue;
/* 202 */       }  if ("email163_url".equals(str)) {
/* 203 */         str8 = paramMap2.get(str).toString();
/*     */       }
/*     */     } 
/* 206 */     if (paramUser != null && str4 != null && !"".equals(str4)) {
/*     */       
/* 208 */       JSONObject jSONObject = WYMailApi.getMailUnreadCount(str4, str7, str5, str6, str8);
/* 209 */       if (jSONObject.getBoolean("suc").booleanValue()) {
/* 210 */         str1 = jSONObject.getJSONObject("con").getString("count");
/*     */       } else {
/* 212 */         str2 = (String)MessageCode.RETURN_CODE.get(jSONObject.getJSONObject("status").getString("errcode"));
/* 213 */         bool = true;
/*     */       } 
/*     */     } else {
/* 216 */       str2 = SystemEnv.getHtmlLabelNames("125018", paramUser.getLanguage());
/* 217 */       bool = true;
/*     */     } 
/* 219 */     hashMap.put("count", str1);
/* 220 */     hashMap.put("errmsgTip", str2);
/* 221 */     hashMap.put("isError", Boolean.valueOf(bool));
/* 222 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMailUnReadList(User paramUser, Map<String, Object> paramMap1, Map<String, Object> paramMap2) {
/* 227 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 228 */     JSONArray jSONArray = new JSONArray();
/* 229 */     String str1 = Util.null2String(paramMap1.get("bindfield"));
/* 230 */     String str2 = maildao.getBindFieldValue(str1, paramUser.getUID() + "");
/* 231 */     String str3 = "";
/* 232 */     boolean bool = false;
/*     */     
/* 234 */     if (!"1".equals(Util.null2String(paramMap1.get("isuse")))) {
/* 235 */       str3 = SystemEnv.getHtmlLabelNames("32386", paramUser.getLanguage());
/* 236 */       bool = true;
/* 237 */       hashMap.put("errmsgTip", str3);
/* 238 */       hashMap.put("isError", Boolean.valueOf(bool));
/* 239 */       return (Map)hashMap;
/*     */     } 
/* 241 */     String str4 = "";
/* 242 */     String str5 = "";
/* 243 */     String str6 = "";
/* 244 */     String str7 = "";
/* 245 */     for (String str : paramMap2.keySet()) {
/* 246 */       if ("email163_domain".equals(str)) {
/* 247 */         str4 = paramMap2.get(str).toString(); continue;
/* 248 */       }  if ("email163_product".equals(str)) {
/* 249 */         str5 = paramMap2.get(str).toString(); continue;
/* 250 */       }  if ("email163_key".equals(str)) {
/* 251 */         str6 = paramMap2.get(str).toString(); continue;
/* 252 */       }  if ("email163_url".equals(str)) {
/* 253 */         str7 = paramMap2.get(str).toString();
/*     */       }
/*     */     } 
/* 256 */     if (paramUser != null && str2 != null && !"".equals(str2)) {
/*     */       
/* 258 */       JSONObject jSONObject = WYMailApi.getUnReadList(str2, str4, str5, str6, str7);
/* 259 */       if (jSONObject.getBoolean("suc").booleanValue()) {
/* 260 */         JSONObject jSONObject1 = jSONObject.getJSONObject("con");
/* 261 */         JSONArray jSONArray1 = jSONObject1.getJSONArray("var");
/* 262 */         for (byte b = 0; b < jSONArray1.size(); b++) {
/* 263 */           JSONObject jSONObject2 = (JSONObject)jSONArray1.get(b);
/*     */           
/* 265 */           Boolean bool1 = jSONObject2.getJSONObject("flags").getBoolean("read");
/* 266 */           if (bool1 == null) {
/* 267 */             bool1 = Boolean.valueOf(false);
/*     */           }
/*     */           
/* 270 */           String str8 = Util.null2String(jSONObject2.getString("subject"));
/*     */           
/* 272 */           String str9 = Util.null2String(jSONObject2.getString("from"));
/*     */           
/* 274 */           String str10 = Util.null2String(jSONObject2.getString("receivedDate"));
/* 275 */           if (!"".equals(str10)) {
/* 276 */             Date date = new Date();
/* 277 */             date.setTime(Long.parseLong(str10));
/* 278 */             SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 279 */             str10 = simpleDateFormat.format(date);
/*     */           } 
/* 281 */           String str11 = jSONObject2.getString("id");
/* 282 */           String str12 = WYMailApi.getSingleMailUrl(str11, str2, str6, str4, str7);
/* 283 */           if (!bool1.booleanValue()) {
/* 284 */             HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 285 */             hashMap1.put("date", str10);
/* 286 */             hashMap1.put("from", str9);
/* 287 */             hashMap1.put("subject", str8);
/* 288 */             hashMap1.put("url", str12);
/* 289 */             jSONArray.add(hashMap1);
/*     */           } 
/*     */         } 
/*     */       } else {
/* 293 */         str3 = jSONObject.getString("error_code");
/* 294 */         bool = true;
/*     */       } 
/*     */     } else {
/* 297 */       str3 = SystemEnv.getHtmlLabelNames("125018", paramUser.getLanguage());
/* 298 */       bool = true;
/*     */     } 
/* 300 */     hashMap.put("unReadList", jSONArray);
/* 301 */     hashMap.put("errmsgTip", str3);
/* 302 */     hashMap.put("isError", Boolean.valueOf(bool));
/* 303 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMailLoginUrlAndName(Map<String, Object> paramMap1, Map<String, Object> paramMap2) {
/* 308 */     RecordSet recordSet = new RecordSet();
/* 309 */     String str1 = Util.null2String(paramMap1.get("id"));
/* 310 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 312 */     String str2 = " select sysid from outter_sys where mailsyscode = ?";
/* 313 */     recordSet.executeQuery(str2, new Object[] { str1 });
/* 314 */     if (recordSet.next()) {
/* 315 */       String str3 = "/interface/Entrance.jsp?id=" + recordSet.getString("sysid");
/* 316 */       String str4 = recordSet.getString("name");
/* 317 */       hashMap.put("loginUrl", str3);
/* 318 */       hashMap.put("name", str4);
/*     */     } 
/* 320 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/mail/impl/WYMailOperationImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */