/*     */ package weaver.integration.mail.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.integration.mail.MailOperation;
/*     */ import weaver.integration.thirdsdk.alymail.api.ALYMailApi;
/*     */ import weaver.integration.thirdsdk.alymail.api.ALYMailSynApi;
/*     */ import weaver.integration.thirdsdk.qqmail.constant.MessageCode;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ALYMailOperationImpl
/*     */   implements MailOperation
/*     */ {
/*     */   public Map<String, Object> checkMailAccountExists(User paramUser, Map<String, Object> paramMap1, Map<String, Object> paramMap2) {
/*  22 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  23 */     String str1 = Util.null2String(paramMap1.get("bindfield"));
/*  24 */     String str2 = maildao.getBindFieldValue(str1, paramUser.getUID() + "");
/*  25 */     boolean bool1 = false;
/*  26 */     boolean bool2 = false;
/*  27 */     boolean bool3 = false;
/*  28 */     String str3 = "";
/*  29 */     if ("1".equals(paramMap1.get("isuse"))) {
/*  30 */       bool1 = true;
/*     */     }
/*     */     
/*  33 */     String str4 = "";
/*  34 */     String str5 = "";
/*  35 */     String str6 = "";
/*  36 */     for (String str : paramMap2.keySet()) {
/*  37 */       if ("alcode".equals(str)) {
/*  38 */         str4 = paramMap2.get(str).toString(); continue;
/*  39 */       }  if ("alpassword".equals(str)) {
/*  40 */         str5 = paramMap2.get(str).toString(); continue;
/*  41 */       }  if ("aldomain".equals(str)) {
/*  42 */         str6 = paramMap2.get(str).toString();
/*     */       }
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  50 */     JSONObject jSONObject1 = ALYMailApi.getToken(str4, str5);
/*  51 */     String str7 = "";
/*  52 */     JSONObject jSONObject2 = jSONObject1.getJSONObject("status");
/*  53 */     JSONObject jSONObject3 = jSONObject1.getJSONObject("data");
/*     */     
/*  55 */     if (jSONObject3.containsKey("accessToken") && "100".equals(jSONObject2.getString("statusCode"))) {
/*  56 */       str7 = jSONObject3.getString("accessToken");
/*     */     } else {
/*     */       
/*  59 */       String str = jSONObject2.getString("statusCode");
/*  60 */       str3 = (String)MessageCode.RETURN_CODE.get(str);
/*  61 */       bool3 = true;
/*     */     } 
/*  63 */     if (paramUser != null && str2 != null && !"".equals(str2)) {
/*     */       
/*  65 */       if (ALYMailApi.checkUser(paramUser.getEmail(), str7, str6)) {
/*  66 */         bool2 = true;
/*     */       } else {
/*  68 */         str3 = SystemEnv.getHtmlLabelNames("125018", paramUser.getLanguage());
/*  69 */         bool3 = true;
/*     */       } 
/*     */     } else {
/*  72 */       str3 = SystemEnv.getHtmlLabelNames("125018", paramUser.getLanguage());
/*  73 */       bool3 = true;
/*     */     } 
/*  75 */     hashMap.put("hasAccount", Boolean.valueOf(bool2));
/*  76 */     hashMap.put("isOpenMail", Boolean.valueOf(bool1));
/*  77 */     hashMap.put("errmsgTip", str3);
/*  78 */     hashMap.put("isError", Boolean.valueOf(bool3));
/*  79 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> testMail(int paramInt, Map<String, Object> paramMap) {
/*  84 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  85 */     String str1 = "";
/*  86 */     String str2 = "";
/*  87 */     String str3 = "";
/*  88 */     for (String str : paramMap.keySet()) {
/*  89 */       if ("alcode".equals(str)) {
/*  90 */         str1 = paramMap.get(str).toString(); continue;
/*  91 */       }  if ("alpassword".equals(str)) {
/*  92 */         str2 = paramMap.get(str).toString(); continue;
/*  93 */       }  if ("aldomain".equals(str)) {
/*  94 */         str3 = paramMap.get(str).toString();
/*     */       }
/*     */     } 
/*     */     
/*  98 */     String str4 = "";
/*     */     try {
/* 100 */       JSONObject jSONObject1 = ALYMailApi.getToken(str1, str2);
/* 101 */       JSONObject jSONObject2 = jSONObject1.getJSONObject("status");
/* 102 */       JSONObject jSONObject3 = jSONObject1.getJSONObject("data");
/*     */       
/* 104 */       if (jSONObject3.containsKey("accessToken") && "100".equals(jSONObject2.getString("statusCode"))) {
/* 105 */         String str5 = jSONObject1.getJSONObject("data").getString("accessToken");
/* 106 */         JSONObject jSONObject = ALYMailApi.getDomain(str5, str3);
/* 107 */         String str6 = jSONObject.getJSONObject("status").getString("statusCode");
/* 108 */         if ("100".equals(str6)) {
/*     */           
/* 110 */           str4 = "success";
/*     */         } else {
/* 112 */           newlog.error("222");
/* 113 */           str4 = (String)MessageCode.RETURN_CODE.get(str6);
/*     */         } 
/*     */       } else {
/*     */         
/* 117 */         String str = jSONObject2.getString("statusCode");
/* 118 */         str4 = (String)MessageCode.RETURN_CODE.get(str);
/*     */       } 
/* 120 */     } catch (Exception exception) {
/* 121 */       str4 = SystemEnv.getHtmlLabelName(385008, paramInt);
/*     */     } 
/*     */     
/* 124 */     if (!"".equalsIgnoreCase(str4)) {
/* 125 */       if (!"success".equals(str4)) {
/* 126 */         hashMap.put("errmsgTip", str4);
/* 127 */         hashMap.put("isError", Boolean.valueOf(true));
/*     */       } else {
/* 129 */         hashMap.put("errmsgTip", SystemEnv.getHtmlLabelName(32297, paramInt));
/*     */       } 
/*     */     }
/* 132 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> initOrgAndUser(User paramUser, Map<String, Object> paramMap1, Map<String, Object> paramMap2) {
/* 137 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 138 */     if ("0".equals(paramMap1.get("isuse"))) {
/* 139 */       hashMap.put("messageCode", "001");
/* 140 */       return (Map)hashMap;
/* 141 */     }  if ("0".equals(paramMap1.get("issync"))) {
/* 142 */       hashMap.put("messageCode", "002");
/* 143 */       return (Map)hashMap;
/*     */     } 
/*     */     
/* 146 */     String str1 = "";
/* 147 */     String str2 = "";
/* 148 */     for (String str : paramMap2.keySet()) {
/* 149 */       if ("alcode".equals(str)) {
/* 150 */         str1 = paramMap2.get(str).toString(); continue;
/* 151 */       }  if ("alpassword".equals(str)) {
/* 152 */         str2 = paramMap2.get(str).toString();
/*     */       }
/*     */     } 
/*     */     
/* 156 */     JSONObject jSONObject1 = ALYMailApi.getToken(str1, str2);
/* 157 */     JSONObject jSONObject2 = jSONObject1.getJSONObject("status");
/* 158 */     String str3 = "";
/*     */     try {
/* 160 */       if (jSONObject1 != null) {
/* 161 */         JSONObject jSONObject = jSONObject1.getJSONObject("data");
/* 162 */         str3 = jSONObject.getString("accessToken");
/*     */       } else {
/* 164 */         String str = jSONObject2.getString("statusCode");
/* 165 */         hashMap.put("messageCode", "003");
/* 166 */         hashMap.put("errcode", str);
/* 167 */         return (Map)hashMap;
/*     */       } 
/* 169 */     } catch (Exception exception) {
/* 170 */       hashMap.put("messageCode", "003");
/* 171 */       return (Map)hashMap;
/*     */     } 
/*     */     try {
/* 174 */       ALYMailSynApi aLYMailSynApi = ALYMailSynApi.getInstance(paramUser, paramMap1, paramMap2);
/* 175 */       aLYMailSynApi.initOrgAndUser(paramUser, str3);
/* 176 */     } catch (Exception exception) {
/* 177 */       newlog.error("OA组织架构和人员初始化到阿里邮箱，出现异常：", exception);
/*     */     } 
/* 179 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMailUnReadCount(User paramUser, Map<String, Object> paramMap1, Map<String, Object> paramMap2) {
/* 185 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 186 */     String str1 = "0";
/* 187 */     String str2 = "";
/* 188 */     String str3 = Util.null2String(paramMap1.get("bindfield"));
/* 189 */     String str4 = maildao.getBindFieldValue(str3, paramUser.getUID() + "");
/* 190 */     boolean bool = false;
/*     */     
/* 192 */     if (!"1".equals(Util.null2String(paramMap1.get("isuse")))) {
/* 193 */       str2 = SystemEnv.getHtmlLabelNames("32386", paramUser.getLanguage());
/* 194 */       bool = true;
/* 195 */       hashMap.put("errmsgTip", str2);
/* 196 */       hashMap.put("isError", Boolean.valueOf(bool));
/* 197 */       return (Map)hashMap;
/*     */     } 
/* 199 */     String str5 = "";
/* 200 */     String str6 = "";
/* 201 */     String str7 = "";
/* 202 */     for (String str : paramMap2.keySet()) {
/* 203 */       if ("alcode".equals(str)) {
/* 204 */         str5 = paramMap2.get(str).toString(); continue;
/* 205 */       }  if ("alpassword".equals(str)) {
/* 206 */         str6 = paramMap2.get(str).toString(); continue;
/* 207 */       }  if ("aldomain".equals(str)) {
/* 208 */         str7 = paramMap2.get(str).toString();
/*     */       }
/*     */     } 
/* 211 */     JSONObject jSONObject1 = ALYMailApi.getToken(str5, str6);
/* 212 */     JSONObject jSONObject2 = jSONObject1.getJSONObject("status");
/* 213 */     JSONObject jSONObject3 = jSONObject1.getJSONObject("data");
/* 214 */     String str8 = "";
/* 215 */     if (jSONObject3.containsKey("accessToken") && "100".equals(jSONObject2.getString("statusCode"))) {
/* 216 */       str8 = jSONObject3.getString("accessToken");
/*     */     } else {
/*     */       
/* 219 */       String str = jSONObject2.getString("statusCode");
/* 220 */       str2 = (String)MessageCode.RETURN_CODE.get(str);
/* 221 */       bool = true;
/*     */     } 
/* 223 */     if (paramUser != null && str4 != null && !"".equals(str4)) {
/*     */       
/* 225 */       JSONObject jSONObject = ALYMailApi.getMailUnread(paramUser.getEmail(), str8, str7);
/* 226 */       newlog.info("阿里邮箱入参------------------user.getEmail()：" + paramUser.getEmail());
/* 227 */       newlog.info("阿里邮箱入参------------------token：" + str8);
/* 228 */       newlog.info("阿里邮箱入参------------------aldomain：" + str7);
/* 229 */       newlog.info("阿里邮箱返回数据------------------：" + jSONObject.toString());
/* 230 */       if ("100".equals(jSONObject.getJSONObject("status").getString("statusCode"))) {
/*     */         
/* 232 */         str1 = getMailUnReadCnt(jSONObject.getJSONObject("data")) + "";
/*     */       } else {
/* 234 */         str2 = (String)MessageCode.RETURN_CODE.get(jSONObject.getJSONObject("status").getString("errcode"));
/* 235 */         bool = true;
/*     */       } 
/*     */     } else {
/*     */       
/* 239 */       str2 = SystemEnv.getHtmlLabelNames("125018", paramUser.getLanguage());
/* 240 */       bool = true;
/*     */     } 
/* 242 */     hashMap.put("count", str1);
/* 243 */     hashMap.put("errmsgTip", str2);
/* 244 */     hashMap.put("isError", Boolean.valueOf(bool));
/* 245 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private int getMailUnReadCnt(JSONObject paramJSONObject) {
/* 254 */     byte b = 0;
/* 255 */     JSONArray jSONArray = paramJSONObject.getJSONArray("dataList");
/* 256 */     if (jSONArray != null && jSONArray.size() > 0) {
/* 257 */       for (byte b1 = 0; b1 < jSONArray.size(); b1++) {
/* 258 */         JSONObject jSONObject = jSONArray.getJSONObject(b1);
/* 259 */         String str = Util.null2String(jSONObject.getString("read"));
/* 260 */         if ("0".equals(str)) {
/* 261 */           b++;
/*     */         }
/*     */       } 
/*     */     }
/* 265 */     return b;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMailUnReadList(User paramUser, Map<String, Object> paramMap1, Map<String, Object> paramMap2) {
/* 270 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 271 */     JSONArray jSONArray = new JSONArray();
/* 272 */     String str1 = Util.null2String(paramMap1.get("bindfield"));
/* 273 */     String str2 = maildao.getBindFieldValue(str1, paramUser.getUID() + "");
/* 274 */     String str3 = "";
/* 275 */     boolean bool = false;
/*     */     
/* 277 */     if (!"1".equals(Util.null2String(paramMap1.get("isuse")))) {
/* 278 */       str3 = SystemEnv.getHtmlLabelNames("32386", paramUser.getLanguage());
/* 279 */       bool = true;
/* 280 */       hashMap.put("errmsgTip", str3);
/* 281 */       hashMap.put("isError", Boolean.valueOf(bool));
/* 282 */       return (Map)hashMap;
/*     */     } 
/* 284 */     String str4 = "";
/* 285 */     String str5 = "";
/* 286 */     String str6 = "";
/* 287 */     for (String str : paramMap2.keySet()) {
/* 288 */       if ("alcode".equals(str)) {
/* 289 */         str4 = paramMap2.get(str).toString(); continue;
/* 290 */       }  if ("alpassword".equals(str)) {
/* 291 */         str5 = paramMap2.get(str).toString(); continue;
/* 292 */       }  if ("aldomain".equals(str)) {
/* 293 */         str6 = paramMap2.get(str).toString();
/*     */       }
/*     */     } 
/* 296 */     JSONObject jSONObject1 = ALYMailApi.getToken(str4, str5);
/* 297 */     JSONObject jSONObject2 = jSONObject1.getJSONObject("status");
/* 298 */     JSONObject jSONObject3 = jSONObject1.getJSONObject("data");
/* 299 */     String str7 = "";
/* 300 */     if (jSONObject3.containsKey("accessToken") && "100".equals(jSONObject2.getString("statusCode"))) {
/* 301 */       str7 = jSONObject3.getString("accessToken");
/*     */     } else {
/*     */       
/* 304 */       String str = jSONObject2.getString("statusCode");
/* 305 */       str3 = (String)MessageCode.RETURN_CODE.get(str);
/* 306 */       bool = true;
/*     */     } 
/* 308 */     if (paramUser != null && str2 != null && !"".equals(str2)) {
/*     */       
/* 310 */       JSONObject jSONObject = ALYMailApi.getMailUnread(paramUser.getEmail(), str7, str6);
/* 311 */       if ("100".equals(jSONObject.getJSONObject("status").getString("statusCode"))) {
/* 312 */         JSONArray jSONArray1 = new JSONArray();
/* 313 */         jSONArray1 = jSONObject.getJSONObject("data").getJSONArray("dataList");
/* 314 */         if (jSONArray1 != null && jSONArray1.size() > 0) {
/* 315 */           for (byte b = 0; b < jSONArray1.size(); b++) {
/* 316 */             JSONObject jSONObject4 = jSONArray1.getJSONObject(b);
/* 317 */             String str8 = jSONObject4.getString("from");
/* 318 */             String str9 = jSONObject4.getString("date");
/* 319 */             String str10 = jSONObject4.getString("subject");
/* 320 */             String str11 = jSONObject4.getString("read");
/* 321 */             if ("0".equals(str11)) {
/* 322 */               jSONObject4.put("from", str8);
/* 323 */               jSONObject4.put("date", str9);
/* 324 */               jSONObject4.put("subject", str10);
/* 325 */               jSONArray.add(jSONObject4);
/*     */             } 
/*     */           } 
/*     */         }
/*     */       } else {
/* 330 */         str3 = (String)MessageCode.RETURN_CODE.get(jSONObject.getJSONObject("status").getString("errcode"));
/* 331 */         bool = true;
/*     */       } 
/*     */     } else {
/*     */       
/* 335 */       str3 = SystemEnv.getHtmlLabelNames("125018", paramUser.getLanguage());
/* 336 */       bool = true;
/*     */     } 
/* 338 */     hashMap.put("unReadList", jSONArray);
/* 339 */     hashMap.put("errmsgTip", str3);
/* 340 */     hashMap.put("isError", Boolean.valueOf(bool));
/* 341 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> getMailLoginUrlAndName(Map<String, Object> paramMap1, Map<String, Object> paramMap2) {
/* 346 */     RecordSet recordSet = new RecordSet();
/* 347 */     String str1 = Util.null2String(paramMap1.get("id"));
/* 348 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 350 */     String str2 = " select sysid from outter_sys where mailsyscode = ?";
/* 351 */     recordSet.executeQuery(str2, new Object[] { str1 });
/* 352 */     if (recordSet.next()) {
/* 353 */       String str3 = "/interface/Entrance.jsp?id=" + recordSet.getString("sysid");
/* 354 */       String str4 = recordSet.getString("name");
/* 355 */       hashMap.put("loginUrl", str3);
/* 356 */       hashMap.put("name", str4);
/*     */     } 
/* 358 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/mail/impl/ALYMailOperationImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */