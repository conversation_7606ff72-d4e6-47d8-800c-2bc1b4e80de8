/*     */ package weaver.integration.mail.WYUtil.rsa;
/*     */ 
/*     */ import java.security.KeyFactory;
/*     */ import java.security.KeyPair;
/*     */ import java.security.KeyPairGenerator;
/*     */ import java.security.PrivateKey;
/*     */ import java.security.PublicKey;
/*     */ import java.security.SecureRandom;
/*     */ import java.security.Signature;
/*     */ import java.security.interfaces.RSAPrivateKey;
/*     */ import java.security.interfaces.RSAPublicKey;
/*     */ import java.security.spec.PKCS8EncodedKeySpec;
/*     */ import java.security.spec.X509EncodedKeySpec;
/*     */ import javax.crypto.Cipher;
/*     */ import org.apache.commons.lang.ArrayUtils;
/*     */ import sun.misc.BASE64Encoder;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RSATool
/*     */ {
/*  24 */   private static final char[] bcdLookup = new char[] { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f' };
/*     */ 
/*     */   
/*  27 */   private static final BASE64Encoder base64 = new BASE64Encoder();
/*     */   
/*  29 */   private String pri = null;
/*  30 */   private String pub = null;
/*     */   
/*     */   public String bytesToHexStr(byte[] paramArrayOfbyte) {
/*  33 */     StringBuffer stringBuffer = new StringBuffer(paramArrayOfbyte.length * 2);
/*     */     
/*  35 */     for (byte b = 0; b < paramArrayOfbyte.length; b++) {
/*  36 */       stringBuffer.append(bcdLookup[paramArrayOfbyte[b] >>> 4 & 0xF]);
/*  37 */       stringBuffer.append(bcdLookup[paramArrayOfbyte[b] & 0xF]);
/*     */     } 
/*     */     
/*  40 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public byte[] hexStrToBytes(String paramString) {
/*  46 */     byte[] arrayOfByte = new byte[paramString.length() / 2];
/*     */     
/*  48 */     for (byte b = 0; b < arrayOfByte.length; b++) {
/*  49 */       arrayOfByte[b] = (byte)Integer.parseInt(paramString.substring(2 * b, 2 * b + 2), 16);
/*     */     }
/*     */ 
/*     */     
/*  53 */     return arrayOfByte;
/*     */   }
/*     */   
/*     */   public void genRSAKeyPair() {
/*  57 */     KeyPairGenerator keyPairGenerator = null;
/*  58 */     KeyPair keyPair = null;
/*     */     try {
/*  60 */       System.out.println("Generating a pair of RSA key ... ");
/*  61 */       keyPairGenerator = KeyPairGenerator.getInstance("RSA");
/*  62 */       SecureRandom secureRandom = new SecureRandom();
/*  63 */       secureRandom.setSeed(System.currentTimeMillis());
/*     */       
/*  65 */       keyPairGenerator.initialize(1024, secureRandom);
/*     */       
/*  67 */       keyPair = keyPairGenerator.genKeyPair();
/*  68 */       PublicKey publicKey = keyPair.getPublic();
/*  69 */       PrivateKey privateKey = keyPair.getPrivate();
/*     */       
/*  71 */       this.pub = bytesToHexStr(publicKey.getEncoded());
/*  72 */       this.pri = bytesToHexStr(privateKey.getEncoded());
/*  73 */       System.out.println("pubKey:" + this.pub);
/*  74 */       System.out.println("priKey:" + this.pri);
/*  75 */       System.out.println("1024-bit RSA key GENERATED.");
/*  76 */     } catch (Exception exception) {
/*  77 */       System.out.println("Exception genRSAKeyPair:" + exception);
/*     */     } 
/*     */   }
/*     */   
/*     */   public String generateSHA1withRSASigature(String paramString1, String paramString2) {
/*     */     try {
/*  83 */       byte[] arrayOfByte1 = hexStrToBytes(paramString2.trim());
/*  84 */       PKCS8EncodedKeySpec pKCS8EncodedKeySpec = new PKCS8EncodedKeySpec(arrayOfByte1);
/*  85 */       KeyFactory keyFactory = KeyFactory.getInstance("RSA");
/*  86 */       RSAPrivateKey rSAPrivateKey = (RSAPrivateKey)keyFactory.generatePrivate(pKCS8EncodedKeySpec);
/*     */       
/*  88 */       Signature signature = Signature.getInstance("SHA1withRSA");
/*  89 */       signature.initSign(rSAPrivateKey);
/*  90 */       signature.update(paramString1.getBytes());
/*  91 */       byte[] arrayOfByte2 = signature.sign();
/*  92 */       return bytesToHexStr(arrayOfByte2);
/*  93 */     } catch (Exception exception) {
/*  94 */       exception.printStackTrace();
/*  95 */       return null;
/*     */     } 
/*     */   }
/*     */   
/*     */   public String encryptWithPriKey(String paramString1, String paramString2) {
/*     */     try {
/* 101 */       byte[] arrayOfByte1 = hexStrToBytes(paramString2.trim());
/* 102 */       PKCS8EncodedKeySpec pKCS8EncodedKeySpec = new PKCS8EncodedKeySpec(arrayOfByte1);
/* 103 */       KeyFactory keyFactory = KeyFactory.getInstance("RSA");
/* 104 */       PrivateKey privateKey = keyFactory.generatePrivate(pKCS8EncodedKeySpec);
/*     */       
/* 106 */       Cipher cipher = Cipher.getInstance("RSA");
/* 107 */       cipher.init(1, privateKey);
/*     */       
/* 109 */       byte[] arrayOfByte2 = paramString1.getBytes();
/* 110 */       byte[] arrayOfByte3 = new byte[0];
/* 111 */       for (byte b = 0; b < arrayOfByte2.length; b += 102) {
/* 112 */         byte[] arrayOfByte4 = ArrayUtils.subarray(arrayOfByte2, b, b + 102);
/* 113 */         byte[] arrayOfByte5 = cipher.doFinal(arrayOfByte4);
/* 114 */         arrayOfByte3 = ArrayUtils.addAll(arrayOfByte3, arrayOfByte5);
/*     */       } 
/* 116 */       return bytesToHexStr(arrayOfByte3);
/* 117 */     } catch (Exception exception) {
/* 118 */       exception.printStackTrace();
/* 119 */       return null;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean verifySHA1withRSASigature(String paramString1, String paramString2, String paramString3) {
/*     */     try {
/* 127 */       Signature signature = Signature.getInstance("SHA1withRSA");
/*     */       
/* 129 */       byte[] arrayOfByte1 = hexStrToBytes(paramString3.trim());
/*     */       
/* 131 */       X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(arrayOfByte1);
/* 132 */       KeyFactory keyFactory = KeyFactory.getInstance("RSA");
/* 133 */       RSAPublicKey rSAPublicKey = (RSAPublicKey)keyFactory.generatePublic(x509EncodedKeySpec);
/*     */       
/* 135 */       signature.initVerify(rSAPublicKey);
/* 136 */       signature.update(paramString2.getBytes());
/*     */       
/* 138 */       byte[] arrayOfByte2 = hexStrToBytes(paramString1);
/* 139 */       return signature.verify(arrayOfByte2);
/*     */     }
/* 141 */     catch (Exception exception) {
/* 142 */       exception.printStackTrace();
/* 143 */       return false;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String encryptLongTextWithPriKey(String paramString1, String paramString2) {
/* 150 */     if (paramString1.length() <= 117) {
/* 151 */       return encryptWithPriKey(paramString1, paramString2);
/*     */     }
/*     */     
/* 154 */     StringBuffer stringBuffer = new StringBuffer();
/* 155 */     byte b = 0;
/* 156 */     while (b < paramString1.length()) {
/* 157 */       int i = (b + 117 > paramString1.length()) ? paramString1.length() : (b + 117);
/* 158 */       String str1 = paramString1.substring(b, i);
/* 159 */       String str2 = encryptWithPriKey(str1, paramString2);
/* 160 */       stringBuffer.append(str2);
/* 161 */       b += 117;
/*     */     } 
/*     */     
/* 164 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   public String encryptWithPriKeyWithBase64(String paramString1, String paramString2) {
/*     */     try {
/* 169 */       byte[] arrayOfByte1 = hexStrToBytes(paramString2.trim());
/* 170 */       PKCS8EncodedKeySpec pKCS8EncodedKeySpec = new PKCS8EncodedKeySpec(arrayOfByte1);
/* 171 */       KeyFactory keyFactory = KeyFactory.getInstance("RSA");
/* 172 */       PrivateKey privateKey = keyFactory.generatePrivate(pKCS8EncodedKeySpec);
/* 173 */       Cipher cipher = Cipher.getInstance("RSA");
/* 174 */       cipher.init(1, privateKey);
/* 175 */       byte[] arrayOfByte2 = cipher.doFinal(paramString1.getBytes());
/*     */       
/* 177 */       return base64.encode(arrayOfByte2).replaceAll("[^a-zA-Z0-9+/=]", "");
/* 178 */     } catch (Exception exception) {
/* 179 */       exception.printStackTrace();
/* 180 */       return null;
/*     */     } 
/*     */   }
/*     */   
/*     */   public String encryptWithPubKey(String paramString1, String paramString2) {
/*     */     try {
/* 186 */       byte[] arrayOfByte1 = hexStrToBytes(paramString2.trim());
/*     */       
/* 188 */       X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(arrayOfByte1);
/*     */       
/* 190 */       KeyFactory keyFactory = KeyFactory.getInstance("RSA");
/* 191 */       PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec);
/*     */       
/* 193 */       Cipher cipher = Cipher.getInstance("RSA");
/* 194 */       cipher.init(1, publicKey);
/* 195 */       byte[] arrayOfByte2 = cipher.doFinal(paramString1.getBytes());
/*     */       
/* 197 */       return bytesToHexStr(arrayOfByte2);
/* 198 */     } catch (Exception exception) {
/* 199 */       exception.printStackTrace();
/* 200 */       return null;
/*     */     } 
/*     */   }
/*     */   
/*     */   public String decryptWithPriKey(String paramString1, String paramString2) {
/*     */     try {
/* 206 */       byte[] arrayOfByte1 = hexStrToBytes(paramString2.trim());
/*     */       
/* 208 */       PKCS8EncodedKeySpec pKCS8EncodedKeySpec = new PKCS8EncodedKeySpec(arrayOfByte1);
/* 209 */       KeyFactory keyFactory = KeyFactory.getInstance("RSA");
/*     */       
/* 211 */       RSAPrivateKey rSAPrivateKey = (RSAPrivateKey)keyFactory.generatePrivate(pKCS8EncodedKeySpec);
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 216 */       Cipher cipher = Cipher.getInstance("RSA");
/* 217 */       cipher.init(2, rSAPrivateKey);
/*     */       
/* 219 */       byte[] arrayOfByte2 = hexStrToBytes(paramString1);
/*     */       
/* 221 */       byte[] arrayOfByte3 = cipher.doFinal(arrayOfByte2);
/*     */       
/* 223 */       return bytesToHexStr(arrayOfByte3);
/*     */     }
/* 225 */     catch (Exception exception) {
/* 226 */       exception.printStackTrace();
/* 227 */       return null;
/*     */     } 
/*     */   }
/*     */   
/*     */   public String decryptWithPubKey(String paramString1, String paramString2) {
/*     */     try {
/* 233 */       byte[] arrayOfByte1 = hexStrToBytes(paramString2.trim());
/*     */       
/* 235 */       X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(arrayOfByte1);
/* 236 */       KeyFactory keyFactory = KeyFactory.getInstance("RSA");
/* 237 */       PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec);
/*     */       
/* 239 */       Cipher cipher = Cipher.getInstance("RSA");
/* 240 */       cipher.init(2, publicKey);
/*     */       
/* 242 */       byte[] arrayOfByte2 = hexStrToBytes(paramString1);
/*     */       
/* 244 */       byte[] arrayOfByte3 = cipher.doFinal(arrayOfByte2);
/*     */       
/* 246 */       return new String(arrayOfByte3);
/*     */     }
/* 248 */     catch (Exception exception) {
/* 249 */       System.err.println(exception);
/* 250 */       exception.printStackTrace(System.err);
/* 251 */       return null;
/*     */     } 
/*     */   }
/*     */   
/*     */   public RSAPrivateKey getPriKey(String paramString) {
/*     */     try {
/* 257 */       byte[] arrayOfByte = hexStrToBytes(paramString.trim());
/*     */       
/* 259 */       PKCS8EncodedKeySpec pKCS8EncodedKeySpec = new PKCS8EncodedKeySpec(arrayOfByte);
/* 260 */       KeyFactory keyFactory = KeyFactory.getInstance("RSA");
/* 261 */       return (RSAPrivateKey)keyFactory.generatePrivate(pKCS8EncodedKeySpec);
/*     */     
/*     */     }
/* 264 */     catch (Exception exception) {
/* 265 */       exception.printStackTrace();
/* 266 */       return null;
/*     */     } 
/*     */   }
/*     */   
/*     */   public RSAPublicKey getPubKey(String paramString) {
/*     */     try {
/* 272 */       byte[] arrayOfByte = hexStrToBytes(paramString.trim());
/*     */       
/* 274 */       X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(arrayOfByte);
/* 275 */       KeyFactory keyFactory = KeyFactory.getInstance("RSA");
/* 276 */       return (RSAPublicKey)keyFactory.generatePublic(x509EncodedKeySpec);
/*     */     }
/* 278 */     catch (Exception exception) {
/* 279 */       exception.printStackTrace();
/* 280 */       return null;
/*     */     } 
/*     */   }
/*     */   
/*     */   public String getPri() {
/* 285 */     return this.pri;
/*     */   }
/*     */   
/*     */   public void setPri(String paramString) {
/* 289 */     this.pri = paramString;
/*     */   }
/*     */   
/*     */   public String getPub() {
/* 293 */     return this.pub;
/*     */   }
/*     */   
/*     */   public void setPub(String paramString) {
/* 297 */     this.pub = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/mail/WYUtil/rsa/RSATool.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */