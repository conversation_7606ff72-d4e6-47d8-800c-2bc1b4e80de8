/*    */ package weaver.integration.mail.WYUtil.rsa;
/*    */ 
/*    */ import java.io.BufferedReader;
/*    */ import java.io.FileInputStream;
/*    */ import java.io.IOException;
/*    */ import java.io.InputStreamReader;
/*    */ import java.io.UnsupportedEncodingException;
/*    */ import java.net.URLDecoder;
/*    */ import java.net.URLEncoder;
/*    */ 
/*    */ public class Utils
/*    */ {
/*    */   private static final int ENCRYPT_LENGTH = 117;
/*    */   private static final int DECRYPT_LENGTH = 256;
/*    */   
/*    */   public static void createKeyPair() {
/* 17 */     RSATool rSATool = new RSATool();
/* 18 */     rSATool.genRSAKeyPair();
/*    */   }
/*    */ 
/*    */   
/*    */   public static String encrypt(String paramString1, String paramString2) throws UnsupportedEncodingException {
/* 23 */     String str = URLEncoder.encode(paramString2, "utf-8");
/* 24 */     RSATool rSATool = new RSATool();
/* 25 */     StringBuffer stringBuffer = new StringBuffer();
/* 26 */     byte b = 0;
/* 27 */     int i = str.length();
/* 28 */     while (b < i) {
/* 29 */       String str1 = null;
/* 30 */       if (b + 117 < i) {
/* 31 */         str1 = str.substring(b, b + 117);
/*    */       } else {
/* 33 */         str1 = str.substring(b);
/*    */       } 
/*    */       
/* 36 */       stringBuffer.append(rSATool.encryptWithPriKey(str1, paramString1));
/* 37 */       b += 117;
/*    */     } 
/*    */     
/* 40 */     return stringBuffer.toString();
/*    */   }
/*    */ 
/*    */   
/*    */   public static String decrypt(String paramString1, String paramString2) throws UnsupportedEncodingException {
/* 45 */     RSATool rSATool = new RSATool();
/* 46 */     int i = paramString2.length() / 256;
/* 47 */     StringBuffer stringBuffer = new StringBuffer();
/* 48 */     for (byte b = 0; b < i; b++) {
/* 49 */       String str = paramString2.substring(b * 256, (b + 1) * 256);
/*    */       
/* 51 */       stringBuffer.append(rSATool.decryptWithPubKey(str, paramString1));
/*    */     } 
/*    */     
/* 54 */     return URLDecoder.decode(stringBuffer.toString(), "utf-8");
/*    */   }
/*    */   
/*    */   public static String getKey(String paramString) throws IOException {
/* 58 */     BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(new FileInputStream(paramString)));
/*    */     
/* 60 */     StringBuffer stringBuffer = new StringBuffer();
/* 61 */     String str = null;
/* 62 */     while ((str = bufferedReader.readLine()) != null) {
/* 63 */       stringBuffer.append(str);
/*    */     }
/*    */     
/* 66 */     return stringBuffer.toString();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/mail/WYUtil/rsa/Utils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */