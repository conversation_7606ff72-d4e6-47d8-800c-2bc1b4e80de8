/*     */ package weaver.integration.mail.WYUtil.rsa;
/*     */ 
/*     */ import java.security.KeyFactory;
/*     */ import java.security.KeyPair;
/*     */ import java.security.KeyPairGenerator;
/*     */ import java.security.PrivateKey;
/*     */ import java.security.PublicKey;
/*     */ import java.security.SecureRandom;
/*     */ import java.security.Signature;
/*     */ import java.security.interfaces.RSAPrivateKey;
/*     */ import java.security.spec.PKCS8EncodedKeySpec;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RSASignatureToQiye
/*     */ {
/*  20 */   private static final char[] bcdLookup = new char[] { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f' };
/*     */ 
/*     */   
/*     */   private static final String ENCODING_UTF_8 = "UTF-8";
/*     */   
/*  25 */   private static String pri = null;
/*  26 */   private static String pub = null;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void createKeyPair() {
/*  32 */     KeyPairGenerator keyPairGenerator = null;
/*  33 */     KeyPair keyPair = null;
/*     */     try {
/*  35 */       System.out.println("Generating a pair of RSA key ... ");
/*  36 */       keyPairGenerator = KeyPairGenerator.getInstance("RSA");
/*  37 */       SecureRandom secureRandom = new SecureRandom();
/*  38 */       secureRandom.setSeed(System.currentTimeMillis());
/*     */       
/*  40 */       keyPairGenerator.initialize(1024, secureRandom);
/*     */       
/*  42 */       keyPair = keyPairGenerator.genKeyPair();
/*  43 */       PublicKey publicKey = keyPair.getPublic();
/*  44 */       PrivateKey privateKey = keyPair.getPrivate();
/*     */       
/*  46 */       pub = bytesToHexStr(publicKey.getEncoded());
/*  47 */       pri = bytesToHexStr(privateKey.getEncoded());
/*  48 */       System.out.println("pubKey:" + pub);
/*  49 */       System.out.println("priKey:" + pri);
/*  50 */       System.out.println("1024-bit RSA key GENERATED.");
/*  51 */     } catch (Exception exception) {
/*  52 */       System.out.println("Exception genRSAKeyPair:" + exception);
/*     */     } 
/*     */   }
/*     */   
/*     */   private static String bytesToHexStr(byte[] paramArrayOfbyte) {
/*  57 */     StringBuffer stringBuffer = new StringBuffer(paramArrayOfbyte.length * 2);
/*     */     
/*  59 */     for (byte b = 0; b < paramArrayOfbyte.length; b++) {
/*  60 */       stringBuffer.append(bcdLookup[paramArrayOfbyte[b] >>> 4 & 0xF]);
/*  61 */       stringBuffer.append(bcdLookup[paramArrayOfbyte[b] & 0xF]);
/*     */     } 
/*     */     
/*  64 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static byte[] hexStrToBytes(String paramString) {
/*  70 */     byte[] arrayOfByte = new byte[paramString.length() / 2];
/*     */     
/*  72 */     for (byte b = 0; b < arrayOfByte.length; b++) {
/*  73 */       arrayOfByte[b] = (byte)Integer.parseInt(paramString.substring(2 * b, 2 * b + 2), 16);
/*     */     }
/*     */ 
/*     */     
/*  77 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String generateSigature(String paramString1, String paramString2) {
/*     */     try {
/*  89 */       Signature signature = Signature.getInstance("SHA1withRSA");
/*  90 */       byte[] arrayOfByte1 = hexStrToBytes(paramString1.trim());
/*  91 */       PKCS8EncodedKeySpec pKCS8EncodedKeySpec = new PKCS8EncodedKeySpec(arrayOfByte1);
/*     */       
/*  93 */       KeyFactory keyFactory = KeyFactory.getInstance("RSA");
/*     */ 
/*     */       
/*  96 */       RSAPrivateKey rSAPrivateKey = (RSAPrivateKey)keyFactory.generatePrivate(pKCS8EncodedKeySpec);
/*  97 */       signature.initSign(rSAPrivateKey);
/*  98 */       signature.update(paramString2.getBytes("UTF-8"));
/*     */       
/* 100 */       byte[] arrayOfByte2 = signature.sign();
/* 101 */       return bytesToHexStr(arrayOfByte2);
/* 102 */     } catch (Exception exception) {
/* 103 */       exception.printStackTrace();
/* 104 */       return null;
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/mail/WYUtil/rsa/RSASignatureToQiye.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */