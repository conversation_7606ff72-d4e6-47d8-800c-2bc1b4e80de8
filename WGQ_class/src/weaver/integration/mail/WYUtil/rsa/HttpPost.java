/*    */ package weaver.integration.mail.WYUtil.rsa;
/*    */ 
/*    */ import java.io.BufferedReader;
/*    */ import java.io.IOException;
/*    */ import java.io.InputStreamReader;
/*    */ import java.io.OutputStreamWriter;
/*    */ import java.net.HttpURLConnection;
/*    */ import java.net.MalformedURLException;
/*    */ import java.net.ProtocolException;
/*    */ import java.net.URL;
/*    */ 
/*    */ public class HttpPost
/*    */ {
/*    */   public String post(String paramString) {
/* 15 */     StringBuffer stringBuffer1 = new StringBuffer();
/* 16 */     HttpURLConnection httpURLConnection = null;
/*    */     
/*    */     try {
/* 19 */       URL uRL = new URL(paramString);
/* 20 */       httpURLConnection = (HttpURLConnection)uRL.openConnection();
/* 21 */       httpURLConnection.setRequestMethod("POST");
/* 22 */       httpURLConnection.setDoOutput(true);
/* 23 */       httpURLConnection.setDoInput(true);
/* 24 */       httpURLConnection.setUseCaches(false);
/* 25 */       httpURLConnection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
/*    */ 
/*    */       
/* 28 */       OutputStreamWriter outputStreamWriter = new OutputStreamWriter(httpURLConnection.getOutputStream(), "UTF-8");
/* 29 */       outputStreamWriter.write(stringBuffer1.toString());
/* 30 */       outputStreamWriter.flush();
/* 31 */       outputStreamWriter.close();
/* 32 */     } catch (MalformedURLException malformedURLException) {
/*    */       
/* 34 */       malformedURLException.printStackTrace();
/* 35 */     } catch (ProtocolException protocolException) {
/*    */       
/* 37 */       protocolException.printStackTrace();
/* 38 */     } catch (IOException iOException) {
/*    */       
/* 40 */       iOException.printStackTrace();
/*    */     } finally {
/* 42 */       if (httpURLConnection != null) {
/* 43 */         httpURLConnection.disconnect();
/*    */       }
/*    */     } 
/*    */     
/* 47 */     StringBuffer stringBuffer2 = new StringBuffer();
/*    */     
/*    */     try {
/* 50 */       BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(httpURLConnection.getInputStream(), "UTF-8"));
/*    */       String str;
/* 52 */       while ((str = bufferedReader.readLine()) != null) {
/* 53 */         stringBuffer2.append(str);
/* 54 */         stringBuffer2.append("\n");
/*    */       } 
/* 56 */     } catch (Exception exception) {
/* 57 */       exception.printStackTrace();
/*    */     } 
/*    */     
/* 60 */     return stringBuffer2.toString();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/mail/WYUtil/rsa/HttpPost.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */