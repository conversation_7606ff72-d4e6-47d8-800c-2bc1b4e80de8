/*    */ package weaver.integration.page.impl;
/*    */ 
/*    */ import com.alibaba.druid.sql.PagerUtils;
/*    */ import weaver.integration.page.PageInterface;
/*    */ 
/*    */ public class MysqlPageInterfaceimpl
/*    */   implements PageInterface
/*    */ {
/*    */   public String getPagingSql(String paramString, int paramInt1, int paramInt2) {
/* 10 */     return PagerUtils.limit(paramString, "mysql", paramInt2, paramInt1);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/page/impl/MysqlPageInterfaceimpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */