/*    */ package weaver.integration.page.impl;
/*    */ 
/*    */ import com.alibaba.druid.sql.PagerUtils;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ import weaver.integration.page.PageInterface;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class JcPageInterfaceimpl
/*    */   implements PageInterface
/*    */ {
/* 14 */   private static Logger newlog = LoggerFactory.getLogger(JcPageInterfaceimpl.class);
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getPagingSql(String paramString, int paramInt1, int paramInt2) {
/* 21 */     return PagerUtils.limit(paramString, "oracle", paramInt2, paramInt1);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/page/impl/JcPageInterfaceimpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */