/*    */ package weaver.integration.page.impl;
/*    */ 
/*    */ import weaver.integration.page.PageInterface;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class InformixPageInterfaceimpl
/*    */   implements PageInterface
/*    */ {
/*    */   public String getPagingSql(String paramString, int paramInt1, int paramInt2) {
/* 11 */     paramString = paramString + "  skip  " + paramInt2 + " first " + paramInt1;
/* 12 */     return paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/page/impl/InformixPageInterfaceimpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */