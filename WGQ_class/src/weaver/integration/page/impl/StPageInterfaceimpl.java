/*    */ package weaver.integration.page.impl;
/*    */ 
/*    */ import com.alibaba.druid.sql.PagerUtils;
/*    */ import weaver.integration.page.PageInterface;
/*    */ 
/*    */ public class StPageInterfaceimpl
/*    */   implements PageInterface
/*    */ {
/*    */   public String getPagingSql(String paramString, int paramInt1, int paramInt2) {
/* 10 */     return PagerUtils.limit(paramString, "oracle", paramInt2, paramInt1);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/page/impl/StPageInterfaceimpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */