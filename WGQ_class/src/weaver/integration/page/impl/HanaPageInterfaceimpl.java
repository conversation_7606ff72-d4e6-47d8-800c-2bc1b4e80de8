/*    */ package weaver.integration.page.impl;
/*    */ 
/*    */ import weaver.integration.page.PageInterface;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HanaPageInterfaceimpl
/*    */   implements PageInterface
/*    */ {
/*    */   public String getPagingSql(String paramString, int paramInt1, int paramInt2) {
/* 12 */     paramString = paramString + "  LIMIT  " + paramInt1 + " OFFSET " + paramInt2;
/* 13 */     return paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/page/impl/HanaPageInterfaceimpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */