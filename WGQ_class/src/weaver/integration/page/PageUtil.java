/*    */ package weaver.integration.page;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.integration.framework.converter.rule.RuleBase;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class PageUtil
/*    */ {
/*    */   public static String limit(String paramString1, String paramString2, int paramInt1, int paramInt2) {
/* 21 */     RecordSet recordSet = new RecordSet();
/* 22 */     String str = "";
/* 23 */     recordSet.executeQuery(RuleBase.PAGE_SQL, new Object[] { paramString2.toLowerCase() });
/* 24 */     if (recordSet.next()) {
/* 25 */       str = recordSet.getString(1);
/*    */     }
/* 27 */     if (str.equals("")) {
/* 28 */       return paramString1;
/*    */     }
/*    */     try {
/* 31 */       PageInterface pageInterface = (PageInterface)Class.forName(str).newInstance();
/* 32 */       paramString1 = pageInterface.getPagingSql(paramString1, paramInt1, paramInt2);
/* 33 */     } catch (InstantiationException instantiationException) {
/* 34 */       instantiationException.printStackTrace();
/* 35 */     } catch (IllegalAccessException illegalAccessException) {
/* 36 */       illegalAccessException.printStackTrace();
/* 37 */     } catch (ClassNotFoundException classNotFoundException) {
/* 38 */       classNotFoundException.printStackTrace();
/*    */     } 
/* 40 */     return paramString1;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static boolean isSupportPage(String paramString) {
/* 49 */     boolean bool = false;
/* 50 */     RecordSet recordSet = new RecordSet();
/* 51 */     recordSet.executeQuery(RuleBase.PAGE_SQL, new Object[] { paramString.toLowerCase() });
/* 52 */     if (recordSet.next()) {
/* 53 */       bool = true;
/*    */     }
/* 55 */     return bool;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/page/PageUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */