/*     */ package weaver.integration.cache;
/*     */ 
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class OfsSettingCacheNew
/*     */   extends CommonCache
/*     */ {
/*  15 */   protected static String TABLE_NAME = "ofs_setting";
/*     */ 
/*     */ 
/*     */   
/*  19 */   protected static String TABLE_WHERE = null;
/*     */ 
/*     */ 
/*     */   
/*  23 */   protected static String TABLE_ORDER = "isuse";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.STRING)
/*  26 */   protected static String PK_NAME = "isuse";
/*     */   
/*     */   @CacheColumn
/*     */   protected static int oashortname;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int oafullname;
/*     */   
/*     */   @CacheColumn
/*     */   protected static int showsysname;
/*     */   @CacheColumn
/*     */   protected static int showdone;
/*     */   @CacheColumn
/*     */   protected static int remindoa;
/*     */   @CacheColumn
/*     */   protected static int remindim;
/*     */   @CacheColumn
/*     */   protected static int remindapp;
/*     */   @CacheColumn
/*     */   protected static int remindemessage;
/*     */   @CacheColumn
/*     */   protected static int messagetypeid;
/*     */   @CacheColumn
/*     */   protected static int remindebridge;
/*     */   @CacheColumn
/*     */   protected static int remindebridgetemplate;
/*     */   @CacheColumn
/*     */   protected static int modifier;
/*     */   @CacheColumn
/*     */   protected static int modifydate;
/*     */   @CacheColumn
/*     */   protected static int modifytime;
/*     */   @CacheColumn
/*     */   protected static int invokewsLog;
/*     */   @CacheColumn
/*     */   protected static int invokerestLog;
/*     */   @CacheColumn
/*     */   protected static int clearinvokelog;
/*     */   @CacheColumn
/*     */   protected static int remaininvokelogdays;
/*     */   @CacheColumn
/*     */   protected static int messagecenter;
/*     */   @CacheColumn
/*     */   protected static int remindshortmessage;
/*     */   @CacheColumn
/*     */   protected static int customreminders;
/*     */   @CacheColumn
/*     */   protected static int customreminderclass;
/*     */   
/*     */   public String getOashortname() {
/*  76 */     return (String)getRowValue(oashortname);
/*     */   }
/*     */   
/*     */   public String getOafullname() {
/*  80 */     return (String)getRowValue(oafullname);
/*     */   }
/*     */   
/*     */   public String getShowsysname() {
/*  84 */     return (String)getRowValue(showsysname);
/*     */   }
/*     */   
/*     */   public String getShowdone() {
/*  88 */     return (String)getRowValue(showdone);
/*     */   }
/*     */   
/*     */   public String getRemindoa() {
/*  92 */     return (String)getRowValue(remindoa);
/*     */   }
/*     */   
/*     */   public String getRemindim() {
/*  96 */     return (String)getRowValue(remindim);
/*     */   }
/*     */   
/*     */   public String getRemindapp() {
/* 100 */     return (String)getRowValue(remindapp);
/*     */   }
/*     */   
/*     */   public String getRemindemessage() {
/* 104 */     return (String)getRowValue(remindemessage);
/*     */   }
/*     */   
/*     */   public String getMessagetypeid() {
/* 108 */     return (String)getRowValue(messagetypeid);
/*     */   }
/*     */   
/*     */   public String getRemindebridge() {
/* 112 */     return (String)getRowValue(remindebridge);
/*     */   }
/*     */   
/*     */   public String getRemindebridgetemplate() {
/* 116 */     return (String)getRowValue(remindebridgetemplate);
/*     */   }
/*     */   
/*     */   public String getModifier() {
/* 120 */     return (String)getRowValue(modifier);
/*     */   }
/*     */   
/*     */   public String getModifydate() {
/* 124 */     return (String)getRowValue(modifydate);
/*     */   }
/*     */   
/*     */   public String getModifytime() {
/* 128 */     return (String)getRowValue(modifytime);
/*     */   }
/*     */   
/*     */   public String getInvokewsLog() {
/* 132 */     return (String)getRowValue(invokewsLog);
/*     */   }
/*     */   public String getInvokerestLog() {
/* 135 */     return (String)getRowValue(invokerestLog);
/*     */   }
/*     */   public String getClearinvokelog() {
/* 138 */     return (String)getRowValue(clearinvokelog);
/*     */   }
/*     */   public String getRemaininvokelogdays() {
/* 141 */     return (String)getRowValue(remaininvokelogdays);
/*     */   }
/*     */   
/*     */   public String getMessagecenter() {
/* 145 */     return (String)getRowValue(messagecenter);
/*     */   }
/*     */   public String getRemindshortmessage() {
/* 148 */     return (String)getRowValue(remindshortmessage);
/*     */   }
/*     */   public String getCustomreminders() {
/* 151 */     return (String)getRowValue(customreminders);
/*     */   }
/*     */   public String getCustomreminderclass() {
/* 154 */     return (String)getRowValue(customreminderclass);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/cache/OfsSettingCacheNew.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */