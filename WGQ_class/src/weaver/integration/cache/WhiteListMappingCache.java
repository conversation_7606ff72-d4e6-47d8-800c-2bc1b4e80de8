/*    */ package weaver.integration.cache;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import weaver.cache.CacheColumn;
/*    */ import weaver.cache.CacheColumnType;
/*    */ import weaver.cache.PKColumn;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WhiteListMappingCache
/*    */   extends CommonCache
/*    */ {
/* 21 */   protected static String TABLE_NAME = "(select t1.id,t1.whitelist_id,t1.filter_id,t2.name_,t2.clazz_,t3.exclude_,t3.type_ from Int_WhiteList_Mapping t1 LEFT OUTER JOIN Int_RegisterFilter t2 ON t2.id=t1.filter_id LEFT OUTER JOIN Int_WhiteList t3 ON t3.id=t1.whitelist_id) tt1";
/*    */ 
/*    */ 
/*    */   
/* 25 */   protected static String TABLE_WHERE = null;
/*    */ 
/*    */ 
/*    */   
/* 29 */   protected static String TABLE_ORDER = "id";
/*    */   
/*    */   @PKColumn(type = CacheColumnType.NUMBER)
/* 32 */   protected static String PK_NAME = "id";
/*    */   
/*    */   @CacheColumn
/*    */   protected static int whitelist_id;
/*    */   @CacheColumn
/*    */   protected static int filter_id;
/*    */   @CacheColumn
/*    */   protected static int name_;
/*    */   @CacheColumn
/*    */   protected static int clazz_;
/*    */   @CacheColumn
/*    */   protected static int exclude_;
/*    */   
/*    */   public String getWhitelist_id() {
/* 46 */     return (String)getRowValue(whitelist_id);
/*    */   }
/*    */   
/*    */   public String getFilter_id() {
/* 50 */     return (String)getRowValue(filter_id);
/*    */   }
/*    */   
/*    */   public String getName_() {
/* 54 */     return (String)getRowValue(name_);
/*    */   }
/*    */   
/*    */   public String getClazz_() {
/* 58 */     return (String)getRowValue(clazz_);
/*    */   }
/*    */   public String getExclude_() {
/* 61 */     return (String)getRowValue(exclude_);
/*    */   }
/*    */   
/*    */   public static List<String> getExclusionsByFilter(String paramString) {
/* 65 */     ArrayList<String> arrayList = new ArrayList();
/* 66 */     WhiteListMappingCache whiteListMappingCache = new WhiteListMappingCache();
/* 67 */     while (whiteListMappingCache.next()) {
/* 68 */       String str = whiteListMappingCache.getClazz_();
/* 69 */       if (paramString.equals(str)) {
/* 70 */         arrayList.add(whiteListMappingCache.getExclude_());
/*    */       }
/*    */     } 
/* 73 */     return arrayList;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/cache/WhiteListMappingCache.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */