/*     */ package weaver.integration.cache;
/*     */ 
/*     */ import dk.itst.oiosaml.error.Layer;
/*     */ import dk.itst.oiosaml.error.WrappedException;
/*     */ import org.opensaml.DefaultBootstrap;
/*     */ import org.opensaml.xml.ConfigurationException;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ 
/*     */ 
/*     */ public class SAMLConfigCache
/*     */   extends CommonCache
/*     */ {
/*  15 */   protected static String TABLE_NAME = "Int_SAML_Cfg";
/*     */ 
/*     */ 
/*     */   
/*  19 */   protected static String TABLE_WHERE = null;
/*     */ 
/*     */ 
/*     */   
/*  23 */   protected static String TABLE_ORDER = "id";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  26 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn
/*     */   protected static int isuse;
/*     */   @CacheColumn
/*     */   protected static int pcauth;
/*     */   @CacheColumn
/*     */   protected static int appauth;
/*     */   @CacheColumn
/*     */   protected static int cfg_dir;
/*     */   @CacheColumn
/*     */   protected static int dt_cfg;
/*     */   @CacheColumn
/*     */   protected static int account_type;
/*     */   @CacheColumn
/*     */   protected static int account_key;
/*     */   @CacheColumn
/*     */   protected static int custom_sql;
/*     */   @CacheColumn
/*     */   protected static int creater;
/*     */   @CacheColumn
/*     */   protected static int createdate;
/*     */   @CacheColumn
/*     */   protected static int createtime;
/*     */   @CacheColumn
/*     */   protected static int modifier;
/*     */   @CacheColumn
/*     */   protected static int modifydate;
/*     */   @CacheColumn
/*     */   protected static int modifytime;
/*     */   
/*     */   public String getIsuse() {
/*  58 */     return (String)getRowValue(isuse);
/*     */   }
/*     */   
/*     */   public String getPcauth() {
/*  62 */     return (String)getRowValue(pcauth);
/*     */   }
/*     */   
/*     */   public String getAppauth() {
/*  66 */     return (String)getRowValue(appauth);
/*     */   }
/*     */   
/*     */   public String getCfg_dir() {
/*  70 */     return (String)getRowValue(cfg_dir);
/*     */   }
/*     */   
/*     */   public String getDt_cfg() {
/*  74 */     return (String)getRowValue(dt_cfg);
/*     */   }
/*     */   
/*     */   public String getAccount_type() {
/*  78 */     return (String)getRowValue(account_type);
/*     */   }
/*     */   
/*     */   public String getAccount_key() {
/*  82 */     return (String)getRowValue(account_key);
/*     */   }
/*     */   
/*     */   public String getCustom_sql() {
/*  86 */     return (String)getRowValue(custom_sql);
/*     */   }
/*     */   
/*     */   public String getCreater() {
/*  90 */     return (String)getRowValue(creater);
/*     */   }
/*     */   
/*     */   public String getCreatedate() {
/*  94 */     return (String)getRowValue(createdate);
/*     */   }
/*     */   
/*     */   public String getCreatetime() {
/*  98 */     return (String)getRowValue(createtime);
/*     */   }
/*     */   
/*     */   public String getModifier() {
/* 102 */     return (String)getRowValue(modifier);
/*     */   }
/*     */   
/*     */   public String getModifydate() {
/* 106 */     return (String)getRowValue(modifydate);
/*     */   }
/*     */   
/*     */   public String getModifytime() {
/* 110 */     return (String)getRowValue(modifytime);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isUsePC() {
/* 119 */     SAMLConfigCache sAMLConfigCache = new SAMLConfigCache();
/* 120 */     if (sAMLConfigCache.next()) {
/* 121 */       String str1 = sAMLConfigCache.getIsuse();
/* 122 */       String str2 = sAMLConfigCache.getPcauth();
/* 123 */       return ("1".equals(str1) && "1".equals(str2));
/*     */     } 
/* 125 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   static {
/*     */     try {
/* 133 */       DefaultBootstrap.bootstrap();
/* 134 */     } catch (ConfigurationException configurationException) {
/* 135 */       throw new WrappedException(Layer.DATAACCESS, configurationException);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/cache/SAMLConfigCache.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */