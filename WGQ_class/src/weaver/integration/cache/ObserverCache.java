/*    */ package weaver.integration.cache;
/*    */ 
/*    */ import weaver.cache.CacheColumn;
/*    */ import weaver.cache.CacheColumnType;
/*    */ import weaver.cache.PKColumn;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ObserverCache
/*    */   extends CommonCache
/*    */ {
/* 18 */   protected static String TABLE_NAME = "Int_Observers";
/*    */ 
/*    */ 
/*    */   
/* 22 */   protected static String TABLE_WHERE = null;
/*    */ 
/*    */ 
/*    */   
/* 26 */   protected static String TABLE_ORDER = "id";
/*    */   
/*    */   @PKColumn(type = CacheColumnType.NUMBER)
/* 29 */   protected static String PK_NAME = "id";
/*    */   
/*    */   @CacheColumn
/*    */   protected static int scope;
/*    */   @CacheColumn
/*    */   protected static int type;
/*    */   @CacheColumn
/*    */   protected static int observer_clazz;
/*    */   @CacheColumn
/*    */   protected static int mapping_clazz;
/*    */   @CacheColumn
/*    */   protected static int isopen;
/*    */   
/*    */   public String getScope() {
/* 43 */     return (String)getRowValue(scope);
/*    */   }
/*    */   
/*    */   public String getType() {
/* 47 */     return (String)getRowValue(type);
/*    */   }
/*    */   
/*    */   public String getObserver_clazz() {
/* 51 */     return (String)getRowValue(observer_clazz);
/*    */   }
/*    */   
/*    */   public String getMapping_clazz() {
/* 55 */     return (String)getRowValue(mapping_clazz);
/*    */   }
/*    */   
/*    */   public String getIsopen() {
/* 59 */     return (String)getRowValue(isopen);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/cache/ObserverCache.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */