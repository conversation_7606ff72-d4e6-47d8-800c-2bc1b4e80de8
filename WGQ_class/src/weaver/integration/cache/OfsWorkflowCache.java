/*    */ package weaver.integration.cache;
/*    */ 
/*    */ import java.util.Map;
/*    */ import java.util.concurrent.ConcurrentHashMap;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.StaticObj;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Deprecated
/*    */ public class OfsWorkflowCache
/*    */ {
/*    */   private static final String fieldStr = " workflowid, sysid, workflowname, receivewfdata ,cancel, creator, createdate, createtime, modifier, modifydate, modifytime ";
/*    */   public static final String Ofs_Workflow = "ofs_workflow";
/* 17 */   private StaticObj staticobj = null;
/* 18 */   private static Object lock = new Object();
/*    */   public OfsWorkflowCache() {
/* 20 */     this.staticobj = StaticObj.getInstance();
/* 21 */     getCache();
/*    */   }
/*    */   private Map<String, Map<String, String>> ofsWorkflowMap;
/*    */   
/*    */   private void getCache() {
/* 26 */     synchronized (lock) {
/* 27 */       if (this.staticobj.getObject("OfsWorkflowInfo") == null) {
/* 28 */         setCache();
/*    */       }
/* 30 */       this.ofsWorkflowMap = (Map<String, Map<String, String>>)this.staticobj.getRecordFromObj("OfsWorkflowInfo", "OfsWorkflowInfoMaps");
/*    */     } 
/*    */   }
/*    */   
/*    */   private void setCache() {
/* 35 */     ConcurrentHashMap<Object, Object> concurrentHashMap = new ConcurrentHashMap<>();
/* 36 */     RecordSet recordSet = new RecordSet();
/* 37 */     String str = " select  workflowid, sysid, workflowname, receivewfdata ,cancel, creator, createdate, createtime, modifier, modifydate, modifytime  from ofs_workflow order by sysid";
/* 38 */     recordSet.executeQuery(str, new Object[0]);
/* 39 */     while (recordSet.next()) {
/* 40 */       ConcurrentHashMap<Object, Object> concurrentHashMap1 = new ConcurrentHashMap<>();
/* 41 */       String str1 = Util.getIntValue(recordSet.getString("sysid"), 0) + "";
/* 42 */       String str2 = Util.null2String(recordSet.getString("workflowname"));
/*    */       
/* 44 */       concurrentHashMap1.put("workflowid", Util.getIntValue(recordSet.getString("workflowid"), 0) + "");
/* 45 */       concurrentHashMap1.put("sysid", Util.getIntValue(recordSet.getString("sysid"), 0) + "");
/* 46 */       concurrentHashMap1.put("workflowname", Util.null2String(recordSet.getString("workflowname")));
/* 47 */       concurrentHashMap1.put("receivewfdata", Util.null2String(recordSet.getString("receivewfdata")));
/* 48 */       concurrentHashMap1.put("cancel", Util.getIntValue(recordSet.getString("cancel"), 0) + "");
/*    */       
/* 50 */       concurrentHashMap1.put("creator", Util.null2String(recordSet.getString("creator")));
/* 51 */       concurrentHashMap1.put("createdate", Util.null2String(recordSet.getString("createdate")));
/* 52 */       concurrentHashMap1.put("createtime", Util.null2String(recordSet.getString("createtime")));
/* 53 */       concurrentHashMap1.put("modifier", Util.null2String(recordSet.getString("modifier")));
/* 54 */       concurrentHashMap1.put("modifydate", Util.null2String(recordSet.getString("modifydate")));
/* 55 */       concurrentHashMap1.put("modifytime", Util.null2String(recordSet.getString("modifytime")));
/*    */       
/* 57 */       concurrentHashMap.put(str1 + "^_^" + str2, concurrentHashMap1);
/*    */     } 
/*    */     
/* 60 */     this.staticobj.putRecordToObj("OfsWorkflowInfo", "OfsWorkflowInfoMaps", concurrentHashMap);
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Map<String, String>> getOfsWorkflowMap() {
/* 65 */     return this.ofsWorkflowMap;
/*    */   }
/*    */   
/*    */   public Map<String, String> getOneMap(String paramString1, String paramString2) {
/* 69 */     return this.ofsWorkflowMap.get(paramString1 + "^_^" + paramString2);
/*    */   }
/*    */   
/*    */   public void setMap(Map<String, String> paramMap) {
/* 73 */     String str = (String)paramMap.get("sysid") + "^_^" + (String)paramMap.get("workflowname");
/* 74 */     this.ofsWorkflowMap.put(str, paramMap);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/cache/OfsWorkflowCache.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */