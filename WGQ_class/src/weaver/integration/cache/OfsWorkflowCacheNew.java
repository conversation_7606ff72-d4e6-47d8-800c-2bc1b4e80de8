/*    */ package weaver.integration.cache;
/*    */ 
/*    */ import weaver.cache.CacheColumn;
/*    */ import weaver.cache.CacheColumnType;
/*    */ import weaver.cache.PKColumn;
/*    */ 
/*    */ 
/*    */ public class OfsWorkflowCacheNew
/*    */   extends CommonCache
/*    */ {
/* 11 */   protected static String TABLE_NAME = "ofs_workflow";
/*    */ 
/*    */ 
/*    */   
/* 15 */   protected static String TABLE_WHERE = null;
/*    */ 
/*    */ 
/*    */   
/* 19 */   protected static String TABLE_ORDER = "workflowid";
/*    */   
/*    */   @PKColumn(type = CacheColumnType.NUMBER)
/* 22 */   protected static String PK_NAME = "workflowid";
/*    */   
/*    */   @CacheColumn
/*    */   protected static int sysid;
/*    */   @CacheColumn
/*    */   protected static int workflowname;
/*    */   @CacheColumn
/*    */   protected static int receivewfdata;
/*    */   @CacheColumn
/*    */   protected static int cancel;
/*    */   @CacheColumn
/*    */   protected static int creator;
/*    */   @CacheColumn
/*    */   protected static int createdate;
/*    */   @CacheColumn
/*    */   protected static int createtime;
/*    */   @CacheColumn
/*    */   protected static int modifier;
/*    */   @CacheColumn
/*    */   protected static int modifydate;
/*    */   @CacheColumn
/*    */   protected static int modifytime;
/*    */   
/*    */   public String getSysid() {
/* 46 */     return (String)getRowValue(sysid);
/*    */   }
/*    */   
/*    */   public String getWorkflowname() {
/* 50 */     return (String)getRowValue(workflowname);
/*    */   }
/*    */   
/*    */   public String getReceivewfdata() {
/* 54 */     return (String)getRowValue(receivewfdata);
/*    */   }
/*    */   
/*    */   public String getCancel() {
/* 58 */     return (String)getRowValue(cancel);
/*    */   }
/*    */   
/*    */   public String getCreator() {
/* 62 */     return (String)getRowValue(creator);
/*    */   }
/*    */   
/*    */   public String getCreatedate() {
/* 66 */     return (String)getRowValue(createdate);
/*    */   }
/*    */   
/*    */   public String getCreatetime() {
/* 70 */     return (String)getRowValue(createtime);
/*    */   }
/*    */   
/*    */   public String getModifier() {
/* 74 */     return (String)getRowValue(modifier);
/*    */   }
/*    */   
/*    */   public String getModifydate() {
/* 78 */     return (String)getRowValue(modifydate);
/*    */   }
/*    */   
/*    */   public String getModifytime() {
/* 82 */     return (String)getRowValue(modifytime);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/cache/OfsWorkflowCacheNew.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */