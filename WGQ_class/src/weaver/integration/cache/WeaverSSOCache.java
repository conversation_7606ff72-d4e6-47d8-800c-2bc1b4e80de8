/*     */ package weaver.integration.cache;
/*     */ 
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ 
/*     */ 
/*     */ public class WeaverSSOCache
/*     */   extends CommonCache
/*     */ {
/*  11 */   protected static String TABLE_NAME = "weaver_sso";
/*     */ 
/*     */ 
/*     */   
/*  15 */   protected static String TABLE_WHERE = null;
/*     */ 
/*     */ 
/*     */   
/*  19 */   protected static String TABLE_ORDER = "isuse";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.STRING)
/*  22 */   protected static String PK_NAME = "isuse";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int token_isuse;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int oauth2_isuse;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int spnego_isuse;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int spnego_ds;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int dlgt_isuse;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int dlgt_ec_isuse;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @CacheColumn
/*     */   protected static int dlgt_em_isuse;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getToken_isuse() {
/*  71 */     return (String)getRowValue(token_isuse);
/*     */   }
/*     */   
/*     */   public String getOauth2_isuse() {
/*  75 */     return (String)getRowValue(oauth2_isuse);
/*     */   }
/*     */   public String getSpnego_isuse() {
/*  78 */     return (String)getRowValue(spnego_isuse);
/*     */   }
/*     */   public String getSpnego_ds() {
/*  81 */     return (String)getRowValue(spnego_ds);
/*     */   }
/*     */   public String getDlgt_isuse() {
/*  84 */     return (String)getRowValue(dlgt_isuse);
/*     */   }
/*     */   public String getDlgt_ec_isuse() {
/*  87 */     return (String)getRowValue(dlgt_ec_isuse);
/*     */   }
/*     */   public String getDlgt_em_isuse() {
/*  90 */     return (String)getRowValue(dlgt_em_isuse);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isOpenCas() {
/*  98 */     WeaverSSOCache weaverSSOCache = new WeaverSSOCache();
/*  99 */     if (weaverSSOCache.next()) {
/* 100 */       return "1".equals(weaverSSOCache.getId());
/*     */     }
/* 102 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isOpenOAuth2() {
/* 110 */     WeaverSSOCache weaverSSOCache = new WeaverSSOCache();
/* 111 */     if (weaverSSOCache.next()) {
/* 112 */       return "1".equals(weaverSSOCache.getOauth2_isuse());
/*     */     }
/* 114 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isOpenToken() {
/* 122 */     WeaverSSOCache weaverSSOCache = new WeaverSSOCache();
/* 123 */     if (weaverSSOCache.next()) {
/* 124 */       return "1".equals(weaverSSOCache.getToken_isuse());
/*     */     }
/* 126 */     return false;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/cache/WeaverSSOCache.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */