/*     */ package weaver.integration.cache;
/*     */ 
/*     */ import java.util.Map;
/*     */ import java.util.concurrent.ConcurrentHashMap;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Deprecated
/*     */ public class OfsSettingCache
/*     */ {
/*     */   private static final String fieldStr = " isuse, oashortname, oafullname, showsysname, showdone, remindoa, remindim, remindapp, modifier, modifydate, modifytime, messagetypeid, remindemessage, remindebridge, remindebridgetemplate ";
/*     */   public static final String Ofs_Setting = "ofs_setting";
/*     */   private String isuse;
/*     */   private String oashortname;
/*     */   private String oafullname;
/*     */   private String showsysname;
/*     */   private String showdone;
/*     */   private String remindoa;
/*     */   private String remindim;
/*     */   private String remindapp;
/*     */   private String remindemessage;
/*     */   private String messagetypeid;
/*     */   private String remindebridge;
/*     */   private String remindebridgetemplate;
/*     */   private String modifier;
/*     */   private String modifydate;
/*     */   private String modifytime;
/*  34 */   private StaticObj staticobj = null;
/*  35 */   private static Object lock = new Object();
/*     */   public OfsSettingCache() {
/*  37 */     this.staticobj = StaticObj.getInstance();
/*  38 */     getCache();
/*     */   }
/*     */   
/*     */   private void getCache() {
/*  42 */     synchronized (lock) {
/*  43 */       if (this.staticobj.getObject("OfsSetting") == null) {
/*  44 */         setCache();
/*     */       }
/*  46 */       this.isuse = (String)this.staticobj.getRecordFromObj("OfsSetting", "isuse");
/*  47 */       this.oashortname = (String)this.staticobj.getRecordFromObj("OfsSetting", "oashortname");
/*  48 */       this.oafullname = (String)this.staticobj.getRecordFromObj("OfsSetting", "oafullname");
/*  49 */       this.showsysname = (String)this.staticobj.getRecordFromObj("OfsSetting", "showsysname");
/*  50 */       this.showdone = (String)this.staticobj.getRecordFromObj("OfsSetting", "showdone");
/*  51 */       this.remindoa = (String)this.staticobj.getRecordFromObj("OfsSetting", "remindoa");
/*  52 */       this.remindim = (String)this.staticobj.getRecordFromObj("OfsSetting", "remindim");
/*  53 */       this.remindapp = (String)this.staticobj.getRecordFromObj("OfsSetting", "remindapp");
/*  54 */       this.remindemessage = (String)this.staticobj.getRecordFromObj("OfsSetting", "remindemessage");
/*  55 */       this.messagetypeid = (String)this.staticobj.getRecordFromObj("OfsSetting", "messagetypeid");
/*  56 */       this.remindebridge = (String)this.staticobj.getRecordFromObj("OfsSetting", "remindebridge");
/*  57 */       this.remindebridgetemplate = (String)this.staticobj.getRecordFromObj("OfsSetting", "remindebridgetemplate");
/*  58 */       this.modifier = (String)this.staticobj.getRecordFromObj("OfsSetting", "modifier");
/*  59 */       this.modifydate = (String)this.staticobj.getRecordFromObj("OfsSetting", "modifydate");
/*  60 */       this.modifytime = (String)this.staticobj.getRecordFromObj("OfsSetting", "modifytime");
/*     */     } 
/*     */   }
/*     */   
/*     */   private void setCache() {
/*  65 */     RecordSet recordSet = new RecordSet();
/*  66 */     String str = " select  isuse, oashortname, oafullname, showsysname, showdone, remindoa, remindim, remindapp, modifier, modifydate, modifytime, messagetypeid, remindemessage, remindebridge, remindebridgetemplate  from ofs_setting";
/*  67 */     recordSet.executeQuery(str, new Object[0]);
/*  68 */     if (recordSet.next()) {
/*  69 */       this.isuse = "" + Util.getIntValue(recordSet.getString("isuse"));
/*  70 */       this.oashortname = Util.null2String(recordSet.getString("oashortname"));
/*  71 */       this.oafullname = Util.null2String(recordSet.getString("oafullname"));
/*  72 */       this.showsysname = Util.null2String(recordSet.getString("showsysname"));
/*  73 */       this.showdone = Util.null2String(recordSet.getString("showdone"));
/*  74 */       this.remindoa = Util.null2String(recordSet.getString("remindoa"));
/*  75 */       this.remindim = Util.null2String(recordSet.getString("remindim"));
/*  76 */       this.remindapp = Util.null2String(recordSet.getString("remindapp"));
/*  77 */       this.remindemessage = Util.null2String(recordSet.getString("remindemessage"));
/*  78 */       this.messagetypeid = Util.null2String(recordSet.getString("messagetypeid"));
/*  79 */       this.remindebridge = Util.null2String(recordSet.getString("remindebridge"));
/*  80 */       this.remindebridgetemplate = Util.null2String(recordSet.getString("remindebridgetemplate"));
/*  81 */       this.modifier = Util.null2String(recordSet.getString("modifier"));
/*  82 */       this.modifydate = Util.null2String(recordSet.getString("modifydate"));
/*  83 */       this.modifytime = Util.null2String(recordSet.getString("modifytime"));
/*     */       
/*  85 */       this.staticobj.putRecordToObj("OfsSetting", "isuse", this.isuse);
/*  86 */       this.staticobj.putRecordToObj("OfsSetting", "oashortname", this.oashortname);
/*  87 */       this.staticobj.putRecordToObj("OfsSetting", "oafullname", this.oafullname);
/*  88 */       this.staticobj.putRecordToObj("OfsSetting", "showsysname", this.showsysname);
/*  89 */       this.staticobj.putRecordToObj("OfsSetting", "showdone", this.showdone);
/*  90 */       this.staticobj.putRecordToObj("OfsSetting", "remindoa", this.remindoa);
/*  91 */       this.staticobj.putRecordToObj("OfsSetting", "remindim", this.remindim);
/*  92 */       this.staticobj.putRecordToObj("OfsSetting", "remindapp", this.remindapp);
/*  93 */       this.staticobj.putRecordToObj("OfsSetting", "remindemessage", this.remindemessage);
/*  94 */       this.staticobj.putRecordToObj("OfsSetting", "messagetypeid", this.messagetypeid);
/*  95 */       this.staticobj.putRecordToObj("OfsSetting", "remindebridge", this.remindebridge);
/*  96 */       this.staticobj.putRecordToObj("OfsSetting", "remindebridgetemplate", this.remindebridgetemplate);
/*  97 */       this.staticobj.putRecordToObj("OfsSetting", "modifier", this.modifier);
/*  98 */       this.staticobj.putRecordToObj("OfsSetting", "modifydate", this.modifydate);
/*  99 */       this.staticobj.putRecordToObj("OfsSetting", "modifytime", this.modifytime);
/*     */     } 
/*     */   }
/*     */   
/*     */   public Map<String, String> getOneMap() {
/* 104 */     ConcurrentHashMap<Object, Object> concurrentHashMap = new ConcurrentHashMap<>();
/* 105 */     concurrentHashMap.put("isuse", Util.getIntValue(this.isuse, 0) + "");
/* 106 */     concurrentHashMap.put("oashortname", this.oashortname);
/* 107 */     concurrentHashMap.put("oafullname", this.oafullname);
/* 108 */     concurrentHashMap.put("showsysname", Util.getIntValue(this.showsysname, 0) + "");
/* 109 */     concurrentHashMap.put("showdone", Util.getIntValue(this.showdone, 0) + "");
/* 110 */     concurrentHashMap.put("remindoa", Util.getIntValue(this.remindoa, 0) + "");
/* 111 */     concurrentHashMap.put("remindim", Util.getIntValue(this.remindim, 0) + "");
/* 112 */     concurrentHashMap.put("remindapp", Util.getIntValue(this.remindapp, 0) + "");
/* 113 */     concurrentHashMap.put("remindemessage", Util.getIntValue(this.remindemessage, 0) + "");
/* 114 */     concurrentHashMap.put("messagetypeid", Util.null2String(this.messagetypeid));
/* 115 */     concurrentHashMap.put("remindebridge", Util.getIntValue(this.remindebridge, 0) + "");
/* 116 */     concurrentHashMap.put("remindebridgetemplate", Util.null2String(this.remindebridgetemplate));
/*     */     
/* 118 */     concurrentHashMap.put("modifier", Util.null2String(this.modifier));
/* 119 */     concurrentHashMap.put("modifydate", Util.null2String(this.modifydate));
/* 120 */     concurrentHashMap.put("modifytime", Util.null2String(this.modifytime));
/* 121 */     return (Map)concurrentHashMap;
/*     */   }
/*     */   
/*     */   public String getIsuse() {
/* 125 */     return this.isuse;
/*     */   }
/*     */   
/*     */   public String getOashortname() {
/* 129 */     return this.oashortname;
/*     */   }
/*     */   
/*     */   public String getOafullname() {
/* 133 */     return this.oafullname;
/*     */   }
/*     */   
/*     */   public String getShowsysname() {
/* 137 */     return this.showsysname;
/*     */   }
/*     */   
/*     */   public String getShowdone() {
/* 141 */     return this.showdone;
/*     */   }
/*     */   
/*     */   public String getRemindoa() {
/* 145 */     return this.remindoa;
/*     */   }
/*     */   
/*     */   public String getRemindim() {
/* 149 */     return this.remindim;
/*     */   }
/*     */   
/*     */   public String getRemindapp() {
/* 153 */     return this.remindapp;
/*     */   }
/*     */   
/*     */   public String getRemindemessage() {
/* 157 */     return this.remindemessage;
/*     */   }
/*     */   
/*     */   public String getMessagetypeid() {
/* 161 */     return this.messagetypeid;
/*     */   }
/*     */   
/*     */   public String getRemindebridge() {
/* 165 */     return this.remindebridge;
/*     */   }
/*     */   
/*     */   public String getRemindebridgetemplate() {
/* 169 */     return this.remindebridgetemplate;
/*     */   }
/*     */   
/*     */   public String getModifier() {
/* 173 */     return this.modifier;
/*     */   }
/*     */   
/*     */   public String getModifydate() {
/* 177 */     return this.modifydate;
/*     */   }
/*     */   
/*     */   public String getModifytime() {
/* 181 */     return this.modifytime;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/cache/OfsSettingCache.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */