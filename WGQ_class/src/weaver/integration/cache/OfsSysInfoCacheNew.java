/*     */ package weaver.integration.cache;
/*     */ 
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ 
/*     */ 
/*     */ public class OfsSysInfoCacheNew
/*     */   extends CommonCache
/*     */ {
/*  11 */   protected static String TABLE_NAME = "ofs_sysinfo";
/*     */ 
/*     */ 
/*     */   
/*  15 */   protected static String TABLE_WHERE = null;
/*     */ 
/*     */ 
/*     */   
/*  19 */   protected static String TABLE_ORDER = "sysid";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  22 */   protected static String PK_NAME = "sysid";
/*     */   
/*     */   @CacheColumn
/*     */   protected static int syscode;
/*     */   @CacheColumn
/*     */   protected static int sysshortname;
/*     */   @CacheColumn
/*     */   protected static int sysfullname;
/*     */   @CacheColumn
/*     */   protected static int securityip;
/*     */   @CacheColumn
/*     */   protected static int pcprefixurl;
/*     */   @CacheColumn
/*     */   protected static int appprefixurl;
/*     */   @CacheColumn
/*     */   protected static int autocreatewftype;
/*     */   @CacheColumn
/*     */   protected static int editwftype;
/*     */   @CacheColumn
/*     */   protected static int receivewfdata;
/*     */   @CacheColumn
/*     */   protected static int hrmtransrule;
/*     */   @CacheColumn
/*     */   protected static int cancel;
/*     */   @CacheColumn
/*     */   protected static int creator;
/*     */   @CacheColumn
/*     */   protected static int createdate;
/*     */   @CacheColumn
/*     */   protected static int createtime;
/*     */   @CacheColumn
/*     */   protected static int modifier;
/*     */   @CacheColumn
/*     */   protected static int modifydate;
/*     */   @CacheColumn
/*     */   protected static int modifytime;
/*     */   @CacheColumn
/*     */   protected static int pcouterfixurl;
/*     */   @CacheColumn
/*     */   protected static int pcentranceurl;
/*     */   @CacheColumn
/*     */   protected static int appentranceurl;
/*     */   @CacheColumn
/*     */   protected static int showpc;
/*     */   @CacheColumn
/*     */   protected static int showapp;
/*     */   
/*     */   public String getSyscode() {
/*  70 */     return (String)getRowValue(syscode);
/*     */   }
/*     */   
/*     */   public String getSysshortname() {
/*  74 */     return (String)getRowValue(sysshortname);
/*     */   }
/*     */   
/*     */   public String getSysfullname() {
/*  78 */     return (String)getRowValue(sysfullname);
/*     */   }
/*     */   
/*     */   public String getSecurityip() {
/*  82 */     return (String)getRowValue(securityip);
/*     */   }
/*     */   
/*     */   public String getPcprefixurl() {
/*  86 */     return (String)getRowValue(pcprefixurl);
/*     */   }
/*     */   
/*     */   public String getAppprefixurl() {
/*  90 */     return (String)getRowValue(appprefixurl);
/*     */   }
/*     */   
/*     */   public String getAutocreatewftype() {
/*  94 */     return (String)getRowValue(autocreatewftype);
/*     */   }
/*     */   
/*     */   public String getEditwftype() {
/*  98 */     return (String)getRowValue(editwftype);
/*     */   }
/*     */   
/*     */   public String getReceivewfdata() {
/* 102 */     return (String)getRowValue(receivewfdata);
/*     */   }
/*     */   
/*     */   public String getHrmtransrule() {
/* 106 */     return (String)getRowValue(hrmtransrule);
/*     */   }
/*     */   
/*     */   public String getCancel() {
/* 110 */     return (String)getRowValue(cancel);
/*     */   }
/*     */   
/*     */   public String getCreator() {
/* 114 */     return (String)getRowValue(creator);
/*     */   }
/*     */   
/*     */   public String getCreatedate() {
/* 118 */     return (String)getRowValue(createdate);
/*     */   }
/*     */   
/*     */   public String getCreatetime() {
/* 122 */     return (String)getRowValue(createtime);
/*     */   }
/*     */   
/*     */   public String getModifier() {
/* 126 */     return (String)getRowValue(modifier);
/*     */   }
/*     */   
/*     */   public String getModifydate() {
/* 130 */     return (String)getRowValue(modifydate);
/*     */   }
/*     */   
/*     */   public String getModifytime() {
/* 134 */     return (String)getRowValue(modifytime);
/*     */   }
/*     */   
/*     */   public String getPcouterfixurl() {
/* 138 */     return (String)getRowValue(pcouterfixurl);
/*     */   }
/*     */   
/*     */   public String getPcentranceurl() {
/* 142 */     return (String)getRowValue(pcentranceurl);
/*     */   }
/*     */   
/*     */   public String getAppentranceurl() {
/* 146 */     return (String)getRowValue(appentranceurl);
/*     */   }
/*     */   
/*     */   public String getShowpc() {
/* 150 */     return (String)getRowValue(showpc);
/*     */   }
/*     */   
/*     */   public String getShowapp() {
/* 154 */     return (String)getRowValue(showapp);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/cache/OfsSysInfoCacheNew.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */