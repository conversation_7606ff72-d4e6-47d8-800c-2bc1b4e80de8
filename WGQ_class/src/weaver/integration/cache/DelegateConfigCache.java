/*     */ package weaver.integration.cache;
/*     */ 
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ 
/*     */ 
/*     */ public class DelegateConfigCache
/*     */   extends CommonCache
/*     */ {
/*  11 */   protected static String TABLE_NAME = "Int_Delegate_Cfg";
/*     */ 
/*     */ 
/*     */   
/*  15 */   protected static String TABLE_WHERE = null;
/*     */ 
/*     */ 
/*     */   
/*  19 */   protected static String TABLE_ORDER = "id";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  22 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn
/*     */   protected static int isuse;
/*     */   @CacheColumn
/*     */   protected static int ec_isuse;
/*     */   @CacheColumn
/*     */   protected static int em_isuse;
/*     */   @CacheColumn
/*     */   protected static int auto_login;
/*     */   @CacheColumn
/*     */   protected static int auto_login_range;
/*     */   @CacheColumn
/*     */   protected static int appid;
/*     */   @CacheColumn
/*     */   protected static int pub_key;
/*     */   @CacheColumn
/*     */   protected static int server_url;
/*     */   @CacheColumn
/*     */   protected static int service_url;
/*     */   @CacheColumn
/*     */   protected static int referer_url;
/*     */   @CacheColumn
/*     */   protected static int accounttype;
/*     */   @CacheColumn
/*     */   protected static int customsql;
/*     */   @CacheColumn
/*     */   protected static int creater;
/*     */   @CacheColumn
/*     */   protected static int createdate;
/*     */   @CacheColumn
/*     */   protected static int createtime;
/*     */   @CacheColumn
/*     */   protected static int modifier;
/*     */   @CacheColumn
/*     */   protected static int modifydate;
/*     */   @CacheColumn
/*     */   protected static int modifytime;
/*     */   
/*     */   public String getIsuse() {
/*  62 */     return (String)getRowValue(isuse);
/*     */   }
/*     */   
/*     */   public String getEc_isuse() {
/*  66 */     return (String)getRowValue(ec_isuse);
/*     */   }
/*     */   
/*     */   public String getEm_isuse() {
/*  70 */     return (String)getRowValue(em_isuse);
/*     */   }
/*     */   
/*     */   public String getAuto_login() {
/*  74 */     return (String)getRowValue(auto_login);
/*     */   }
/*     */   
/*     */   public String getAuto_login_range() {
/*  78 */     return (String)getRowValue(auto_login_range);
/*     */   }
/*     */   
/*     */   public String getAppid() {
/*  82 */     return (String)getRowValue(appid);
/*     */   }
/*     */   
/*     */   public String getPub_key() {
/*  86 */     return (String)getRowValue(pub_key);
/*     */   }
/*     */   
/*     */   public String getServer_url() {
/*  90 */     return (String)getRowValue(server_url);
/*     */   }
/*     */   
/*     */   public String getService_url() {
/*  94 */     return (String)getRowValue(service_url);
/*     */   }
/*     */   
/*     */   public String getReferer_url() {
/*  98 */     return (String)getRowValue(referer_url);
/*     */   }
/*     */   
/*     */   public String getAccounttype() {
/* 102 */     return (String)getRowValue(accounttype);
/*     */   }
/*     */   
/*     */   public String getCustomsql() {
/* 106 */     return (String)getRowValue(customsql);
/*     */   }
/*     */   
/*     */   public String getCreater() {
/* 110 */     return (String)getRowValue(creater);
/*     */   }
/*     */   
/*     */   public String getCreatedate() {
/* 114 */     return (String)getRowValue(createdate);
/*     */   }
/*     */   
/*     */   public String getCreatetime() {
/* 118 */     return (String)getRowValue(createtime);
/*     */   }
/*     */   
/*     */   public String getModifier() {
/* 122 */     return (String)getRowValue(modifier);
/*     */   }
/*     */   
/*     */   public String getModifydate() {
/* 126 */     return (String)getRowValue(modifydate);
/*     */   }
/*     */   
/*     */   public String getModifytime() {
/* 130 */     return (String)getRowValue(modifytime);
/*     */   }
/*     */   
/*     */   public static boolean isUse() {
/* 134 */     DelegateConfigCache delegateConfigCache = new DelegateConfigCache();
/* 135 */     if (delegateConfigCache.next()) {
/* 136 */       String str = delegateConfigCache.getIsuse();
/* 137 */       return "1".equals(str);
/*     */     } 
/* 139 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isUsePC() {
/* 147 */     DelegateConfigCache delegateConfigCache = new DelegateConfigCache();
/* 148 */     if (delegateConfigCache.next()) {
/* 149 */       String str1 = delegateConfigCache.getIsuse();
/* 150 */       String str2 = delegateConfigCache.getEc_isuse();
/* 151 */       return ("1".equals(str1) && "1".equals(str2));
/*     */     } 
/* 153 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isUseMobile() {
/* 161 */     DelegateConfigCache delegateConfigCache = new DelegateConfigCache();
/* 162 */     if (delegateConfigCache.next()) {
/* 163 */       String str1 = delegateConfigCache.getIsuse();
/* 164 */       String str2 = delegateConfigCache.getEm_isuse();
/* 165 */       return ("1".equals(str1) && "1".equals(str2));
/*     */     } 
/* 167 */     return false;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/cache/DelegateConfigCache.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */