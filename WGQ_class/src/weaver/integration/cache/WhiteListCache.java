/*    */ package weaver.integration.cache;
/*    */ 
/*    */ import weaver.cache.CacheColumn;
/*    */ import weaver.cache.CacheColumnType;
/*    */ import weaver.cache.PKColumn;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WhiteListCache
/*    */   extends CommonCache
/*    */ {
/* 18 */   protected static String TABLE_NAME = "Int_WhiteList";
/*    */ 
/*    */ 
/*    */   
/* 22 */   protected static String TABLE_WHERE = null;
/*    */ 
/*    */ 
/*    */   
/* 26 */   protected static String TABLE_ORDER = "orderNum";
/*    */   
/*    */   @PKColumn(type = CacheColumnType.NUMBER)
/* 29 */   protected static String PK_NAME = "id";
/*    */   
/*    */   @CacheColumn
/*    */   protected static int exclude_;
/*    */   @CacheColumn
/*    */   protected static int type_;
/*    */   @CacheColumn
/*    */   protected static int orderNum;
/*    */   @CacheColumn
/*    */   protected static int is_sys;
/*    */   
/*    */   public String getExclude_() {
/* 41 */     return (String)getRowValue(exclude_);
/*    */   }
/*    */   
/*    */   public String getType_() {
/* 45 */     return (String)getRowValue(type_);
/*    */   }
/*    */   
/*    */   public String getOrderNum() {
/* 49 */     return (String)getRowValue(orderNum);
/*    */   }
/*    */   
/*    */   public String getIs_sys() {
/* 53 */     return (String)getRowValue(is_sys);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/cache/WhiteListCache.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */