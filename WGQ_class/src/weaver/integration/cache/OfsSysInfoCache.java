/*    */ package weaver.integration.cache;
/*    */ 
/*    */ import java.util.Map;
/*    */ import java.util.concurrent.ConcurrentHashMap;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.StaticObj;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Deprecated
/*    */ public class OfsSysInfoCache
/*    */ {
/*    */   private static final String fieldStr = " sysid, syscode, sysshortname, sysfullname ,securityip ,pcprefixurl,appprefixurl, autocreatewftype, editwftype, receivewfdata, hrmtransrule, cancel, creator, createdate, createtime, modifier, modifydate, modifytime, pcouterfixurl, pcentranceurl, appentranceurl ";
/*    */   public static final String Ofs_SysInfo = "ofs_sysinfo";
/* 19 */   private StaticObj staticobj = null;
/* 20 */   private static Object lock = new Object();
/*    */   public OfsSysInfoCache() {
/* 22 */     this.staticobj = StaticObj.getInstance();
/* 23 */     getCache();
/*    */   }
/*    */   
/*    */   private Map<String, Map<String, String>> ofsSysInfoMap;
/*    */   
/*    */   private void getCache() {
/* 29 */     synchronized (lock) {
/* 30 */       if (this.staticobj.getObject("OfsSysInfo") == null) {
/* 31 */         setCache();
/*    */       }
/* 33 */       this.ofsSysInfoMap = (Map<String, Map<String, String>>)this.staticobj.getRecordFromObj("OfsSysInfo", "ofsSysInfoMap");
/*    */     } 
/*    */   }
/*    */   
/*    */   private void setCache() {
/* 38 */     ConcurrentHashMap<Object, Object> concurrentHashMap = new ConcurrentHashMap<>();
/* 39 */     RecordSet recordSet = new RecordSet();
/* 40 */     String str = " select  sysid, syscode, sysshortname, sysfullname ,securityip ,pcprefixurl,appprefixurl, autocreatewftype, editwftype, receivewfdata, hrmtransrule, cancel, creator, createdate, createtime, modifier, modifydate, modifytime, pcouterfixurl, pcentranceurl, appentranceurl  from ofs_sysinfo";
/* 41 */     recordSet.executeQuery(str, new Object[0]);
/* 42 */     while (recordSet.next()) {
/* 43 */       ConcurrentHashMap<Object, Object> concurrentHashMap1 = new ConcurrentHashMap<>();
/* 44 */       String str1 = Util.null2String(recordSet.getString("syscode"));
/*    */       
/* 46 */       concurrentHashMap1.put("sysid", Util.getIntValue(recordSet.getString("sysid"), 0) + "");
/* 47 */       concurrentHashMap1.put("syscode", Util.null2String(recordSet.getString("syscode")));
/* 48 */       concurrentHashMap1.put("sysshortname", Util.null2String(recordSet.getString("sysshortname")));
/* 49 */       concurrentHashMap1.put("sysfullname", Util.null2String(recordSet.getString("sysfullname")));
/* 50 */       concurrentHashMap1.put("securityip", Util.null2String(recordSet.getString("securityip")));
/* 51 */       concurrentHashMap1.put("pcprefixurl", Util.null2String(recordSet.getString("pcprefixurl")));
/* 52 */       concurrentHashMap1.put("appprefixurl", Util.null2String(recordSet.getString("appprefixurl")));
/* 53 */       concurrentHashMap1.put("autocreatewftype", Util.null2String(recordSet.getString("autocreatewftype")));
/* 54 */       concurrentHashMap1.put("editwftype", Util.null2String(recordSet.getString("editwftype")));
/* 55 */       concurrentHashMap1.put("receivewfdata", Util.null2String(recordSet.getString("receivewfdata")));
/* 56 */       concurrentHashMap1.put("hrmtransrule", Util.null2String(recordSet.getString("hrmtransrule")));
/* 57 */       concurrentHashMap1.put("cancel", Util.getIntValue(recordSet.getString("cancel"), 0) + "");
/*    */       
/* 59 */       concurrentHashMap1.put("creator", Util.null2String(recordSet.getString("creator")));
/* 60 */       concurrentHashMap1.put("createdate", Util.null2String(recordSet.getString("createdate")));
/* 61 */       concurrentHashMap1.put("createtime", Util.null2String(recordSet.getString("createtime")));
/* 62 */       concurrentHashMap1.put("modifier", Util.null2String(recordSet.getString("modifier")));
/* 63 */       concurrentHashMap1.put("modifydate", Util.null2String(recordSet.getString("modifydate")));
/* 64 */       concurrentHashMap1.put("modifytime", Util.null2String(recordSet.getString("modifytime")));
/* 65 */       concurrentHashMap1.put("pcouterfixurl", Util.null2String(recordSet.getString("pcouterfixurl")));
/* 66 */       concurrentHashMap1.put("pcentranceurl", Util.null2String(recordSet.getString("pcentranceurl")));
/* 67 */       concurrentHashMap1.put("appentranceurl", Util.null2String(recordSet.getString("appentranceurl")));
/*    */       
/* 69 */       concurrentHashMap.put(str1, concurrentHashMap1);
/*    */     } 
/*    */     
/* 72 */     this.staticobj.putRecordToObj("OfsSysInfo", "ofsSysInfoMap", concurrentHashMap);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public Map<String, String> getOneMap(String paramString) {
/* 78 */     return this.ofsSysInfoMap.get(paramString);
/*    */   }
/*    */   
/*    */   public Map<String, Map<String, String>> getOfsSysInfoMap() {
/* 82 */     return this.ofsSysInfoMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/cache/OfsSysInfoCache.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */