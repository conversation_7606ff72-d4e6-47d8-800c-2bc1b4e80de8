/*    */ package weaver.integration.cache;
/*    */ 
/*    */ import weaver.cache.CacheColumn;
/*    */ import weaver.cache.CacheColumnType;
/*    */ import weaver.cache.PKColumn;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RegisterFilterCache
/*    */   extends CommonCache
/*    */ {
/* 18 */   protected static String TABLE_NAME = "Int_RegisterFilter";
/*    */ 
/*    */ 
/*    */   
/* 22 */   protected static String TABLE_WHERE = null;
/*    */ 
/*    */ 
/*    */   
/* 26 */   protected static String TABLE_ORDER = "orderNum";
/*    */   
/*    */   @PKColumn(type = CacheColumnType.NUMBER)
/* 29 */   protected static String PK_NAME = "id";
/*    */   
/*    */   @CacheColumn
/*    */   protected static int name_;
/*    */   @CacheColumn
/*    */   protected static int clazz_;
/*    */   @CacheColumn
/*    */   protected static int orderNum;
/*    */   
/*    */   public String getName_() {
/* 39 */     return (String)getRowValue(name_);
/*    */   }
/*    */   
/*    */   public String getClazz_() {
/* 43 */     return (String)getRowValue(clazz_);
/*    */   }
/*    */   
/*    */   public String getOrderNum() {
/* 47 */     return (String)getRowValue(orderNum);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/cache/RegisterFilterCache.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */