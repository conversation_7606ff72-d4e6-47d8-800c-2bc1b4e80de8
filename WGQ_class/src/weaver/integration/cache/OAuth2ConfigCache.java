/*     */ package weaver.integration.cache;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.engine.integration.util.HttpsUtil;
/*     */ import java.util.Map;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ import weaver.general.Util;
/*     */ import weaver.interfaces.sso.oauth2.SchemeUtils;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class OAuth2ConfigCache
/*     */   extends CommonCache
/*     */ {
/*  19 */   protected static String TABLE_NAME = "Int_OAuth2_Cfg";
/*     */ 
/*     */ 
/*     */   
/*  23 */   protected static String TABLE_WHERE = null;
/*     */ 
/*     */ 
/*     */   
/*  27 */   protected static String TABLE_ORDER = "id";
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  30 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn
/*     */   protected static int isuse;
/*     */   @CacheColumn
/*     */   protected static int isuse_ac;
/*     */   @CacheColumn
/*     */   protected static int pcauth;
/*     */   @CacheColumn
/*     */   protected static int appauth;
/*     */   @CacheColumn
/*     */   protected static int client_id_key;
/*     */   @CacheColumn
/*     */   protected static int client_id;
/*     */   @CacheColumn
/*     */   protected static int client_secret_key;
/*     */   @CacheColumn
/*     */   protected static int client_secret;
/*     */   @CacheColumn
/*     */   protected static int code_key;
/*     */   @CacheColumn
/*     */   protected static int account_type;
/*     */   @CacheColumn
/*     */   protected static int account_key;
/*     */   @CacheColumn
/*     */   protected static int access_token_key;
/*     */   @CacheColumn
/*     */   protected static int redirect_uri_key;
/*     */   @CacheColumn
/*     */   protected static int authorize_cfg;
/*     */   @CacheColumn
/*     */   protected static int access_token_cfg;
/*     */   @CacheColumn
/*     */   protected static int profile_cfg;
/*     */   @CacheColumn
/*     */   protected static int logout_cfg;
/*     */   @CacheColumn
/*     */   protected static int refresh_token_cfg;
/*     */   @CacheColumn
/*     */   protected static int heart_beat_cfg;
/*     */   @CacheColumn
/*     */   protected static int other_params;
/*     */   @CacheColumn
/*     */   protected static int creater;
/*     */   @CacheColumn
/*     */   protected static int createdate;
/*     */   @CacheColumn
/*     */   protected static int createtime;
/*     */   @CacheColumn
/*     */   protected static int modifier;
/*     */   @CacheColumn
/*     */   protected static int modifydate;
/*     */   @CacheColumn
/*     */   protected static int modifytime;
/*     */   
/*     */   public String getIsuse() {
/*  86 */     return (String)getRowValue(isuse);
/*     */   }
/*     */   
/*     */   public String getIsuse_ac() {
/*  90 */     return (String)getRowValue(isuse_ac);
/*     */   }
/*     */   
/*     */   public String getPcauth() {
/*  94 */     return (String)getRowValue(pcauth);
/*     */   }
/*     */   
/*     */   public String getAppauth() {
/*  98 */     return (String)getRowValue(appauth);
/*     */   }
/*     */   
/*     */   public String getClient_id_key() {
/* 102 */     return (String)getRowValue(client_id_key);
/*     */   }
/*     */   
/*     */   public String getClient_id() {
/* 106 */     return (String)getRowValue(client_id);
/*     */   }
/*     */   
/*     */   public String getClient_secret_key() {
/* 110 */     return (String)getRowValue(client_secret_key);
/*     */   }
/*     */   
/*     */   public String getClient_secret() {
/* 114 */     return (String)getRowValue(client_secret);
/*     */   }
/*     */   
/*     */   public String getCode_key() {
/* 118 */     return (String)getRowValue(code_key);
/*     */   }
/*     */   
/*     */   public String getAccount_type() {
/* 122 */     return (String)getRowValue(account_type);
/*     */   }
/*     */   
/*     */   public String getAccount_key() {
/* 126 */     return (String)getRowValue(account_key);
/*     */   }
/*     */   
/*     */   public String getAccess_token_key() {
/* 130 */     return (String)getRowValue(access_token_key);
/*     */   }
/*     */   public String getRedirect_uri_key() {
/* 133 */     return (String)getRowValue(redirect_uri_key);
/*     */   }
/*     */   public String getAuthorize_cfg() {
/* 136 */     return (String)getRowValue(authorize_cfg);
/*     */   }
/*     */   
/*     */   public String getAccess_token_cfg() {
/* 140 */     return (String)getRowValue(access_token_cfg);
/*     */   }
/*     */   
/*     */   public String getProfile_cfg() {
/* 144 */     return (String)getRowValue(profile_cfg);
/*     */   }
/*     */   
/*     */   public String getLogout_cfg() {
/* 148 */     return (String)getRowValue(logout_cfg);
/*     */   }
/*     */   
/*     */   public String getRefresh_token_cfg() {
/* 152 */     return (String)getRowValue(refresh_token_cfg);
/*     */   }
/*     */   
/*     */   public String getHeart_beat_cfg() {
/* 156 */     return (String)getRowValue(heart_beat_cfg);
/*     */   }
/*     */   public String getOther_params() {
/* 159 */     return (String)getRowValue(other_params);
/*     */   }
/*     */   
/*     */   public String getCreater() {
/* 163 */     return (String)getRowValue(creater);
/*     */   }
/*     */   
/*     */   public String getCreatedate() {
/* 167 */     return (String)getRowValue(createdate);
/*     */   }
/*     */   
/*     */   public String getCreatetime() {
/* 171 */     return (String)getRowValue(createtime);
/*     */   }
/*     */   
/*     */   public String getModifier() {
/* 175 */     return (String)getRowValue(modifier);
/*     */   }
/*     */   
/*     */   public String getModifydate() {
/* 179 */     return (String)getRowValue(modifydate);
/*     */   }
/*     */   
/*     */   public String getModifytime() {
/* 183 */     return (String)getRowValue(modifytime);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isUsePC() {
/* 192 */     OAuth2ConfigCache oAuth2ConfigCache = new OAuth2ConfigCache();
/* 193 */     if (oAuth2ConfigCache.next()) {
/* 194 */       String str1 = oAuth2ConfigCache.getIsuse();
/* 195 */       String str2 = oAuth2ConfigCache.getPcauth();
/* 196 */       return ("1".equals(str1) && "1".equals(str2));
/*     */     } 
/* 198 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getAuthorizeURL(Map<String, Object> paramMap) {
/* 207 */     String str = "";
/* 208 */     OAuth2ConfigCache oAuth2ConfigCache = new OAuth2ConfigCache();
/* 209 */     if (oAuth2ConfigCache.next()) {
/* 210 */       String str1 = oAuth2ConfigCache.getAuthorize_cfg();
/* 211 */       JSONObject jSONObject = JSON.parseObject(str1);
/* 212 */       if (jSONObject != null) {
/* 213 */         str = Util.null2String(jSONObject.getString("authorize_url"));
/* 214 */         JSONArray jSONArray = jSONObject.getJSONArray("authorize_params");
/* 215 */         String str2 = HttpsUtil.getRequestParams(jSONArray, paramMap);
/* 216 */         str = HttpsUtil.getRequestUrl(str, paramMap);
/* 217 */         str = str + ((str.indexOf("?") > 0) ? "&" : "?") + str2;
/*     */       } 
/*     */     } 
/*     */     
/* 221 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getLogoutURL(Map<String, Object> paramMap) {
/* 231 */     String str = "";
/* 232 */     OAuth2ConfigCache oAuth2ConfigCache = new OAuth2ConfigCache();
/* 233 */     if (oAuth2ConfigCache.next() && "1".equals(oAuth2ConfigCache.getIsuse())) {
/* 234 */       String str1 = oAuth2ConfigCache.getLogout_cfg();
/* 235 */       JSONObject jSONObject = JSON.parseObject(str1);
/* 236 */       if (jSONObject != null) {
/* 237 */         str = Util.null2String(jSONObject.getString("logout_url"));
/* 238 */         JSONArray jSONArray = jSONObject.getJSONArray("logout_params");
/* 239 */         String str2 = HttpsUtil.getRequestParams(jSONArray, paramMap);
/* 240 */         str = HttpsUtil.getRequestUrl(str, paramMap);
/* 241 */         str = str + ((str.indexOf("?") > 0) ? "&" : "?") + str2;
/*     */       } 
/*     */     } 
/*     */     
/* 245 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean getHeartBeatResult(Map<String, Object> paramMap) {
/* 255 */     boolean bool = true;
/* 256 */     OAuth2ConfigCache oAuth2ConfigCache = new OAuth2ConfigCache();
/* 257 */     if (oAuth2ConfigCache.next() && "1".equals(oAuth2ConfigCache.getIsuse())) {
/* 258 */       String str = oAuth2ConfigCache.getHeart_beat_cfg();
/* 259 */       JSONObject jSONObject = JSON.parseObject(str);
/* 260 */       if (jSONObject != null && "1".equals(jSONObject.getString("heart_beat_use"))) {
/* 261 */         String str1 = Util.null2String(jSONObject.getString("heart_beat_url"));
/* 262 */         String str2 = Util.null2String(jSONObject.getString("heart_beat_ok_flag"));
/* 263 */         String str3 = Util.null2String(jSONObject.getString("heart_beat_method")).equals("1") ? "GET" : "POST";
/* 264 */         JSONArray jSONArray1 = jSONObject.getJSONArray("heart_beat_header");
/* 265 */         JSONArray jSONArray2 = jSONObject.getJSONArray("heart_beat_params");
/*     */         
/* 267 */         boolean bool1 = SchemeUtils.isParaScheme(jSONArray2);
/* 268 */         if (bool1) {
/* 269 */           String str5 = oAuth2ConfigCache.getClient_id();
/* 270 */           String str6 = oAuth2ConfigCache.getClient_secret();
/* 271 */           SchemeUtils.generateParaHeartbeatParams(paramMap, str5, str6);
/*     */         } 
/*     */ 
/*     */         
/* 275 */         String str4 = HttpsUtil.getResult(jSONArray1, jSONArray2, str1, str3, paramMap);
/* 276 */         if (!"".equals(str2) && str4.indexOf(str2) == -1) {
/* 277 */           bool = false;
/*     */         }
/*     */       } 
/*     */     } 
/*     */     
/* 282 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/cache/OAuth2ConfigCache.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */