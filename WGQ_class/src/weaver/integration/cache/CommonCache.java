/*    */ package weaver.integration.cache;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import weaver.cache.CacheBase;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CommonCache
/*    */   extends CacheBase
/*    */ {
/*    */   public boolean next() {
/* 14 */     return super.next();
/*    */   }
/*    */   
/*    */   public void setTofirstRow() {
/* 18 */     super.setTofirstRow();
/*    */   }
/*    */   
/*    */   public String getId() {
/* 22 */     return (String)getRowValue(0);
/*    */   }
/*    */   
/*    */   public void removeCache() {
/* 26 */     super.removeCache();
/*    */   }
/*    */   
/*    */   public void addCacheByList(ArrayList<String> paramArrayList) {
/* 30 */     if (paramArrayList == null || paramArrayList.isEmpty()) {
/*    */       return;
/*    */     }
/* 33 */     String str = "";
/* 34 */     for (byte b = 0; b < paramArrayList.size(); b++) {
/* 35 */       str = paramArrayList.get(b);
/* 36 */       addCacheByKey(str);
/*    */     } 
/*    */   }
/*    */   
/*    */   public void addCacheByKey(String paramString) {
/* 41 */     addCache(paramString);
/*    */   }
/*    */   
/*    */   public void updateCacheByKey(String paramString) {
/* 45 */     updateCache(paramString);
/*    */   }
/*    */   
/*    */   public void deleteCacheByKey(String paramString) {
/* 49 */     deleteCache(paramString);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/cache/CommonCache.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */