/*      */ package weaver.integration.conftest;
/*      */ 
/*      */ import java.io.PrintWriter;
/*      */ import java.io.StringReader;
/*      */ import java.io.StringWriter;
/*      */ import java.lang.reflect.Field;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.Hashtable;
/*      */ import java.util.Iterator;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.Set;
/*      */ import java.util.TreeMap;
/*      */ import java.util.regex.Matcher;
/*      */ import java.util.regex.Pattern;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import javax.servlet.http.HttpServletResponse;
/*      */ import net.sf.json.JSONArray;
/*      */ import net.sf.json.JSONObject;
/*      */ import net.sf.jsqlparser.parser.CCJSqlParserManager;
/*      */ import net.sf.jsqlparser.statement.select.Select;
/*      */ import org.json.JSONArray;
/*      */ import org.json.JSONObject;
/*      */ import org.quartz.CronExpression;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.conn.RecordSetDataSource;
/*      */ import weaver.general.SecurityHelper;
/*      */ import weaver.general.StaticObj;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.integration.logging.Logger;
/*      */ import weaver.integration.logging.LoggerFactory;
/*      */ import weaver.interfaces.datasource.BaseDataSource;
/*      */ import weaver.interfaces.datasource.DataSource;
/*      */ import weaver.interfaces.workflow.action.jc.dynamic.DynamicEngine;
/*      */ import weaver.interfaces.workflow.browser.BaseBrowser;
/*      */ import weaver.interfaces.workflow.browser.Browser;
/*      */ import weaver.interfaces.workflow.browser.BrowserBean;
/*      */ import weaver.servicefiles.ScheduleXML;
/*      */ import weaver.workflow.automatic.automaticcols;
/*      */ import weaver.wsclient.util.WSDLFacade;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class ConfigureTestUtil
/*      */ {
/*   68 */   private Logger newlog = LoggerFactory.getLogger(ConfigureTestUtil.class);
/*      */ 
/*      */ 
/*      */   
/*   72 */   private HttpServletRequest request = null;
/*      */ 
/*      */ 
/*      */   
/*   76 */   private String messages = "success";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static boolean ParseSQL(String paramString) {
/*   83 */     boolean bool = true;
/*   84 */     if ("".equals(paramString)) return bool; 
/*      */     try {
/*   86 */       Pattern pattern = Pattern.compile("(\\$(.+?)\\$)|(\\{\\?(.+?)\\})");
/*   87 */       Matcher matcher = pattern.matcher(paramString);
/*   88 */       while (matcher.find()) {
/*   89 */         String str = matcher.group();
/*   90 */         paramString = paramString.replace(str, "''");
/*      */       } 
/*   92 */       paramString = paramString.replaceAll("\\\\'", "'");
/*   93 */       CCJSqlParserManager cCJSqlParserManager = new CCJSqlParserManager();
/*   94 */       Select select = (Select)cCJSqlParserManager.parse(new StringReader(paramString));
/*   95 */     } catch (Exception exception) {
/*   96 */       bool = false;
/*      */     } 
/*   98 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void dmlTest() {
/*  104 */     String str = Util.null2String(this.request.getParameter("dataid"));
/*  105 */     RecordSet recordSet = new RecordSet();
/*  106 */     recordSet.execute("select * from formactionset t1,formactionsqlset t2 where t1.id=t2.actionid and t1.id=" + str);
/*  107 */     if (recordSet.next()) {
/*  108 */       String str1 = Util.null2String(recordSet.getString("datasourceid"));
/*  109 */       String str2 = Util.null2String(recordSet.getString("dmltablename"));
/*  110 */       String str3 = "";
/*  111 */       if (!str1.equals("")) {
/*  112 */         BaseDataSource baseDataSource = new BaseDataSource();
/*  113 */         String str4 = Util.null2String(baseDataSource.testDataSource(str1, 10));
/*  114 */         if (str4.equals("0")) {
/*  115 */           RecordSetDataSource recordSetDataSource = new RecordSetDataSource(str1);
/*  116 */           if (baseDataSource.getType().toLowerCase().equals("oracle")) {
/*  117 */             str3 = "select COUNT(1) from user_tables where table_name = '" + str2.toUpperCase() + "'";
/*      */           }
/*  119 */           else if (baseDataSource.getType().toLowerCase().equals("postgresql")) {
/*  120 */             str3 = "select count(1) from information_schema.tables where  upper(table_name) =upper('" + str2 + "')";
/*      */           } else {
/*  122 */             str3 = "select count(1) from sys.objects where name ='" + str2 + "'";
/*      */           } 
/*  124 */           recordSetDataSource.execute(str3);
/*  125 */           recordSetDataSource.next();
/*  126 */           int i = Util.getIntValue(recordSetDataSource.getString(1));
/*  127 */           if (i == 0) {
/*  128 */             this.messages = "notable";
/*      */           }
/*      */         } else {
/*  131 */           this.messages = "connectError";
/*      */         } 
/*      */       } else {
/*  134 */         if (recordSet.getDBType().toLowerCase().equals("oracle")) {
/*  135 */           str3 = "select COUNT(1) from user_tables where table_name = '" + str2.toUpperCase() + "'";
/*      */         }
/*  137 */         else if (recordSet.getDBType().toLowerCase().equals("postgresql")) {
/*      */           
/*  139 */           str3 = "select count(1) from information_schema.tables where  upper(table_name) =upper('" + str2 + "')";
/*      */         } else {
/*      */           
/*  142 */           str3 = "select count(1) from sys.objects where name ='" + str2 + "'";
/*      */         } 
/*  144 */         recordSet.execute(str3);
/*  145 */         recordSet.next();
/*  146 */         int i = Util.getIntValue(recordSet.getString(1));
/*  147 */         if (i == 0) {
/*  148 */           this.messages = "notable";
/*      */         }
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void wfwsconfigTest() {
/*  159 */     String str1 = Util.null2String(this.request.getParameter("dataid"));
/*  160 */     String str2 = "select 1 from wsregistemethod t1 left join wsformactionset t2 on t1.id=t2.wsoperation where t2.id=" + str1;
/*  161 */     RecordSet recordSet = new RecordSet();
/*  162 */     recordSet.execute(str2);
/*  163 */     if (recordSet.next()) {
/*  164 */       webserviceTest();
/*      */     } else {
/*  166 */       this.messages = "methoderror";
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void webserviceTest() {
/*  175 */     RecordSet recordSet = new RecordSet();
/*  176 */     String str = Util.null2String(this.request.getParameter("id"));
/*  177 */     if (str.equals("")) {
/*  178 */       String str1 = Util.null2String(this.request.getParameter("dataid"));
/*  179 */       if (!str1.equals("")) {
/*  180 */         recordSet.execute("select wsurl from wsformactionset where id=" + str1);
/*  181 */         recordSet.next();
/*  182 */         str = Util.null2String(recordSet.getString(1));
/*      */       } 
/*      */     } 
/*  185 */     if (!"".equals(str)) {
/*  186 */       WSDLFacade wSDLFacade = new WSDLFacade();
/*  187 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*      */       try {
/*  189 */         recordSet.execute("select webserviceurl from wsregiste where id=" + str);
/*  190 */         recordSet.next();
/*  191 */         String str1 = recordSet.getString(1);
/*  192 */         this.messages = wSDLFacade.getAllMethod(str1);
/*  193 */         JSONArray jSONArray = JSONArray.fromObject(this.messages);
/*  194 */         for (byte b = 0; b < jSONArray.size(); b++) {
/*  195 */           JSONObject jSONObject = JSONObject.fromObject(jSONArray.get(b));
/*  196 */           String str2 = Util.null2String(jSONObject.getString("name"));
/*  197 */           String str3 = Util.null2String(jSONObject.getString("outparamtype"));
/*  198 */           hashMap.put(str2, str3);
/*      */         } 
/*  200 */         this.messages = "success";
/*  201 */       } catch (Exception exception) {
/*  202 */         this.newlog.error(exception);
/*  203 */         this.messages = "exception";
/*      */       } 
/*  205 */       if (this.messages.equals("success")) {
/*      */         try {
/*  207 */           recordSet.execute("select methodname,methodreturntype from wsregistemethod where mainid=" + str);
/*  208 */           while (recordSet.next()) {
/*  209 */             String str1 = Util.null2String(recordSet.getString("methodname"));
/*  210 */             String str2 = Util.null2String(recordSet.getString("methodreturntype"));
/*  211 */             String str3 = (String)hashMap.get(str1);
/*  212 */             if (str3 == null) {
/*  213 */               this.messages = "methoderror"; break;
/*      */             } 
/*  215 */             if ("unknowtype".equals(str3) || !str2.equals(str3)) {
/*  216 */               this.messages = "revalerror";
/*      */               break;
/*      */             } 
/*      */           } 
/*  220 */         } catch (Exception exception) {
/*  221 */           this.newlog.error(exception);
/*  222 */           this.messages = "methoderror";
/*      */         } 
/*      */       }
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void hrSynchronize() {
/*  234 */     String str = Util.null2String(this.request.getParameter("intetype"));
/*  235 */     if (str.equals("1")) {
/*  236 */       hrSynForDB();
/*  237 */     } else if (str.equals("2")) {
/*  238 */       hrSynForWS();
/*  239 */     } else if (str.equals("3")) {
/*  240 */       hrSynForCust();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void hrSynForCust() {
/*  247 */     String str = Util.null2String(this.request.getParameter("custominterface"));
/*      */     try {
/*  249 */       Class<?> clazz = Class.forName(str);
/*  250 */       Object object = clazz.newInstance();
/*  251 */       if (!(object instanceof weaver.interfaces.hrm.HrmSynService)) {
/*  252 */         this.messages = "classerror";
/*      */       }
/*  254 */     } catch (Throwable throwable) {
/*  255 */       this.messages = "noclasserror";
/*  256 */       this.newlog.error(throwable);
/*  257 */       throwable.printStackTrace();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void hrSynForDB() {
/*  264 */     RecordSet recordSet = new RecordSet();
/*  265 */     String str1 = Util.null2String(this.request.getParameter("dbsource"));
/*  266 */     BaseDataSource baseDataSource = new BaseDataSource();
/*  267 */     String str2 = Util.null2String(baseDataSource.testDataSource(str1, 10));
/*  268 */     if (str2.equals("0")) {
/*  269 */       recordSet.execute("select * from hrsyncset ");
/*  270 */       if (recordSet.next()) {
/*      */         
/*  272 */         String str3 = Util.null2String(recordSet.getString("subcomtable"));
/*  273 */         String str4 = Util.null2String(recordSet.getString("depttable"));
/*  274 */         String str5 = Util.null2String(recordSet.getString("jobtable"));
/*  275 */         String str6 = Util.null2String(recordSet.getString("hrmtable"));
/*      */         
/*  277 */         String str7 = Util.null2String(recordSet.getString("subcomouternew"));
/*  278 */         String str8 = Util.null2String(recordSet.getString("deptouternew"));
/*  279 */         String str9 = Util.null2String(recordSet.getString("jobouternew"));
/*  280 */         String str10 = Util.null2String(recordSet.getString("hrmouternew"));
/*      */         
/*  282 */         RecordSetDataSource recordSetDataSource = new RecordSetDataSource(str1);
/*  283 */         Map map1 = recordSetDataSource.getAllColumnWithTypes(str1, str3);
/*  284 */         Map map2 = recordSetDataSource.getAllColumnWithTypes(str1, str4);
/*  285 */         Map map3 = recordSetDataSource.getAllColumnWithTypes(str1, str5);
/*  286 */         Map map4 = recordSetDataSource.getAllColumnWithTypes(str1, str6);
/*      */         
/*  288 */         if ((map1 == null || map1.size() < 1) && !str3.equals("")) {
/*  289 */           this.messages = "tablenull_sub";
/*      */           return;
/*      */         } 
/*  292 */         if ((map2 == null || map2.size() < 1) && !str4.equals("")) {
/*  293 */           this.messages = "tablenull_dept";
/*      */           return;
/*      */         } 
/*  296 */         if ((map3 == null || map3.size() < 1) && !str5.equals("")) {
/*  297 */           this.messages = "tablenull_job";
/*      */           return;
/*      */         } 
/*  300 */         if ((map4 == null || map4.size() < 1) && !str6.equals("")) {
/*  301 */           this.messages = "tablenull_hrm";
/*      */           
/*      */           return;
/*      */         } 
/*  305 */         if (!str7.equals("")) {
/*  306 */           String str = Util.null2String(map1.get(str7));
/*  307 */           if (str.equals("")) {
/*  308 */             this.messages = "iderror_sub_" + str7;
/*      */             
/*      */             return;
/*      */           } 
/*      */         } 
/*  313 */         if (!str8.equals("")) {
/*  314 */           String str = Util.null2String(map2.get(str8));
/*  315 */           if (str.equals("")) {
/*  316 */             this.messages = "iderror_dept_" + str8;
/*      */             
/*      */             return;
/*      */           } 
/*      */         } 
/*  321 */         if (!str9.equals("")) {
/*  322 */           String str = Util.null2String(map3.get(str9));
/*  323 */           if (str.equals("")) {
/*  324 */             this.messages = "iderror_job_" + str9;
/*      */             
/*      */             return;
/*      */           } 
/*      */         } 
/*  329 */         if (!str10.equals("")) {
/*  330 */           String str = Util.null2String(map4.get(str10));
/*  331 */           if (str.equals("")) {
/*  332 */             this.messages = "iderror_hrm_" + str10;
/*      */             
/*      */             return;
/*      */           } 
/*      */         } 
/*      */         
/*  338 */         if (this.messages.equals("success")) {
/*  339 */           TreeMap<Object, Object> treeMap = new TreeMap<>();
/*  340 */           recordSet.execute("select distinct type,outfield from hrsyncsetparam order by type,outfield");
/*  341 */           while (recordSet.next()) {
/*  342 */             Map map; String str11 = Util.null2String(recordSet.getString("type"));
/*  343 */             String str12 = Util.null2String(recordSet.getString("outfield"));
/*  344 */             if (str11.equals("1")) {
/*  345 */               map = map1;
/*  346 */               str11 = "sub";
/*  347 */             } else if (str11.equals("2")) {
/*  348 */               map = map2;
/*  349 */               str11 = "dept";
/*  350 */             } else if (str11.equals("3")) {
/*  351 */               str11 = "job";
/*  352 */               map = map3;
/*  353 */             } else if (str11.equals("4")) {
/*  354 */               str11 = "hrm";
/*  355 */               map = map4;
/*      */             } 
/*  357 */             String str13 = Util.null2String(map.get(str12));
/*  358 */             if (str13.equals("")) {
/*  359 */               this.messages = str11 + "outfielderror_" + str12;
/*      */               break;
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } else {
/*  366 */       this.messages = "connerror";
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void hrSynForWS() {
/*  374 */     RecordSet recordSet = new RecordSet();
/*  375 */     String str = Util.null2String(this.request.getParameter("webserviceurl"));
/*  376 */     if (!"".equals(str)) {
/*  377 */       WSDLFacade wSDLFacade = new WSDLFacade();
/*  378 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*      */       try {
/*  380 */         recordSet.execute("select webserviceurl from wsregiste where id=" + str);
/*  381 */         recordSet.next();
/*  382 */         String str1 = recordSet.getString(1);
/*  383 */         String str2 = wSDLFacade.getAllMethod(str1);
/*  384 */         JSONArray jSONArray = JSONArray.fromObject(str2);
/*  385 */         for (byte b = 0; b < jSONArray.size(); b++) {
/*  386 */           JSONObject jSONObject = JSONObject.fromObject(jSONArray.get(b));
/*  387 */           String str3 = Util.null2String(jSONObject.getString("name"));
/*  388 */           String str4 = Util.null2String(jSONObject.getString("outparamtype"));
/*      */           
/*  390 */           hashMap.put(str3, str4);
/*      */         } 
/*  392 */       } catch (Exception exception) {
/*  393 */         this.newlog.error(exception);
/*  394 */         this.messages = "exception";
/*      */       } 
/*  396 */       if (this.messages.equals("success")) {
/*      */         
/*  398 */         recordSet.execute("select * from hrsyncset");
/*      */         
/*  400 */         if (recordSet.next()) {
/*  401 */           String str1 = Util.null2String(recordSet.getString("subcommothod"));
/*  402 */           String str2 = Util.null2String(recordSet.getString("deptmothod"));
/*  403 */           String str3 = Util.null2String(recordSet.getString("jobmothod"));
/*  404 */           String str4 = Util.null2String(recordSet.getString("hrmmethod"));
/*      */           
/*  406 */           String str5 = "";
/*  407 */           String str6 = "";
/*      */           
/*  409 */           recordSet.execute("select a.id,a.methodname,a.methodreturntype from wsregistemethod a join hrsyncset b on a.id =b.subcommothod or a.id=deptmothod or a.id=b.jobmothod or a.id=b.hrmmethod");
/*  410 */           while (recordSet.next()) {
/*  411 */             String str7 = recordSet.getString("id");
/*  412 */             String str8 = recordSet.getString("methodname");
/*  413 */             String str9 = recordSet.getString("methodreturntype");
/*  414 */             String str10 = (String)hashMap.get(str8);
/*  415 */             if (str10 == null) {
/*  416 */               if (str7.equals(str1)) {
/*  417 */                 this.messages = "submethoderror"; break;
/*  418 */               }  if (str7.equals(str2)) {
/*  419 */                 this.messages = "deptmethoderror"; break;
/*  420 */               }  if (str7.equals(str3)) {
/*  421 */                 this.messages = "jobmethoderror"; break;
/*  422 */               }  if (str7.equals(str4))
/*  423 */                 this.messages = "hrmmethoderror"; 
/*      */               break;
/*      */             } 
/*  426 */             if ("unknowtype".equals(str10) || !str9.equals(str10)) {
/*  427 */               if (str7.equals(str1)) {
/*  428 */                 this.messages = "subrevalerror"; break;
/*  429 */               }  if (str7.equals(str2)) {
/*  430 */                 this.messages = "deptrevalerror"; break;
/*  431 */               }  if (str7.equals(str3)) {
/*  432 */                 this.messages = "jobrevalerror"; break;
/*  433 */               }  if (str7.equals(str4)) {
/*  434 */                 this.messages = "hrmrevalerror";
/*      */               }
/*      */               break;
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void scheduleTest() {
/*  447 */     String str1 = Util.null2String(this.request.getParameter("classname"));
/*  448 */     String str2 = Util.null2String(this.request.getParameter("cronexpr"));
/*      */     
/*  450 */     String str3 = Util.null2String(this.request.getParameter("checkby"));
/*  451 */     if ("dbid".equals(str3)) {
/*  452 */       String str = Util.null2String(this.request.getParameter("id"));
/*  453 */       Hashtable<Object, Object> hashtable = new Hashtable<>();
/*  454 */       hashtable = (Hashtable<Object, Object>)(new ScheduleXML()).getDataHSTByid().get(str);
/*  455 */       str1 = Util.null2String(hashtable.get("construct"));
/*  456 */       str2 = Util.null2String(hashtable.get("cronExpr"));
/*      */     } 
/*      */     
/*      */     try {
/*  460 */       Class<?> clazz = Class.forName(str1);
/*  461 */       Object object = clazz.newInstance();
/*  462 */       if (!(object instanceof weaver.interfaces.schedule.BaseCronJob) && !(object instanceof weaver.interfaces.schedule.CronJob)) {
/*  463 */         this.messages = "classerror";
/*      */       }
/*  465 */     } catch (Throwable throwable) {
/*  466 */       this.messages = "noclasserror";
/*  467 */       this.newlog.error(throwable);
/*  468 */       throwable.printStackTrace();
/*      */     } 
/*  470 */     if (this.messages.equals("success")) {
/*  471 */       boolean bool = CronExpression.isValidExpression(str2);
/*  472 */       if (!bool) {
/*  473 */         this.messages = "cronexprerror";
/*      */       }
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void automaticTest() {
/*      */     try {
/*  482 */       String str1 = Util.null2String(this.request.getParameter("viewid"));
/*  483 */       RecordSet recordSet = new RecordSet();
/*  484 */       recordSet.execute("select t2.formid,t2.isvalid,t2.isbill,t.* from outerdatawfset t left join workflow_base t2 on t.workflowid=t2.id where t.id=" + str1);
/*  485 */       recordSet.next();
/*  486 */       String str2 = Util.null2String(recordSet.getString("workflowid"));
/*  487 */       String str3 = Util.null2String(recordSet.getString("datasourceid"));
/*  488 */       String str4 = Util.null2String(recordSet.getString("outermaintable"));
/*  489 */       String str5 = Util.null2String(recordSet.getString("outermainwhere"));
/*  490 */       String str6 = Util.null2String(recordSet.getString("datarecordtype"));
/*  491 */       String str7 = Util.null2String(recordSet.getString("outerdetailtables"));
/*  492 */       ArrayList arrayList1 = Util.TokenizerString(str7, ",");
/*  493 */       String str8 = Util.null2String(recordSet.getString("outerdetailwheres"));
/*  494 */       ArrayList arrayList2 = Util.TokenizerString(str8, "$@|@$");
/*  495 */       String str9 = Util.null2String(recordSet.getString("keyfield"));
/*  496 */       String str10 = Util.null2String(recordSet.getString("requestid"));
/*  497 */       String str11 = Util.null2String(recordSet.getString("FTriggerFlag"));
/*  498 */       String str12 = Util.null2String(recordSet.getString("FTriggerFlagValue"));
/*  499 */       int i = recordSet.getInt("isvalid");
/*  500 */       String str13 = Util.null2String(recordSet.getString("isbill"));
/*  501 */       String str14 = Util.null2String(recordSet.getString("formid"));
/*      */       
/*  503 */       this.messages = "-1";
/*      */       
/*  505 */       if (i != 1) {
/*  506 */         this.messages = "-2";
/*      */       } else {
/*      */         
/*  509 */         recordSet.execute("select * from datasourcesetting where pointid='" + str3 + "'");
/*  510 */         recordSet.next();
/*  511 */         String str15 = Util.null2String(recordSet.getString("iscode"));
/*  512 */         String str16 = Util.null2String(recordSet.getString("username"));
/*  513 */         String str17 = Util.null2String(recordSet.getString("password"));
/*  514 */         if (str15.equals("1")) {
/*  515 */           str16 = SecurityHelper.decrypt("ecology", str16);
/*  516 */           str17 = SecurityHelper.decrypt("ecology", str17);
/*      */         } 
/*  518 */         BaseDataSource baseDataSource = new BaseDataSource();
/*  519 */         baseDataSource.setType(recordSet.getString("type"));
/*  520 */         baseDataSource.setUrl(SecurityHelper.decrypt("ecology", recordSet.getString("url")));
/*  521 */         baseDataSource.setHost(SecurityHelper.decrypt("ecology", recordSet.getString("host")));
/*  522 */         baseDataSource.setPort(SecurityHelper.decrypt("ecology", recordSet.getString("port")));
/*  523 */         baseDataSource.setDbname(SecurityHelper.decrypt("ecology", recordSet.getString("dbname")));
/*      */         
/*  525 */         baseDataSource.setUser(str16);
/*  526 */         baseDataSource.setPassword(str17);
/*  527 */         baseDataSource.setIscluster(recordSet.getString("iscluster"));
/*  528 */         String str18 = "" + baseDataSource.testDataSource();
/*  529 */         if (!str18.equals("0")) {
/*  530 */           this.messages = "connectError";
/*      */         } else {
/*  532 */           if ("".equals(str9))
/*  533 */             str9 = "id"; 
/*  534 */           if ("2".equals(str6)) {
/*  535 */             if ("".equals(str10))
/*  536 */               str10 = "requestid"; 
/*  537 */             if ("".equals(str11))
/*  538 */               str11 = "FTriggerFlag"; 
/*      */           } 
/*  540 */           str4 = Util.replace(str4, "'", "''", 0);
/*  541 */           str9 = Util.replace(str9, "'", "''", 0);
/*  542 */           str10 = Util.replace(str10, "'", "''", 0);
/*  543 */           str11 = Util.replace(str11, "'", "''", 0);
/*  544 */           str12 = Util.replace(str12, "'", "''", 0);
/*  545 */           RecordSetDataSource recordSetDataSource = new RecordSetDataSource(str3);
/*  546 */           Map map = recordSetDataSource.getAllColumnWithTypes(str3, str4);
/*  547 */           if (map == null || map.size() < 1) {
/*  548 */             this.messages = "noMainTable";
/*      */           } else {
/*  550 */             String str19 = Util.null2String(map.get(str9));
/*  551 */             String str20 = Util.null2String(map.get(str10));
/*  552 */             String str21 = Util.null2String(map.get(str11));
/*  553 */             if (str19.equals("")) {
/*  554 */               this.messages = "noMainKeyField";
/*  555 */             } else if ("2".equals(str6) && str20.equals("")) {
/*  556 */               this.messages = "noRequestidField";
/*  557 */             } else if ("2".equals(str6) && str21.equals("")) {
/*  558 */               this.messages = "noFTriggerFlagField";
/*      */             } else {
/*  560 */               String str = "";
/*  561 */               if ("2".equals(str6) || "".equals(str6)) {
/*  562 */                 str = "select " + str9 + "," + str10 + "," + str11 + " from " + str4;
/*      */               } else {
/*  564 */                 str = "select " + str9 + " from " + str4;
/*      */               } 
/*  566 */               if (!"".equals(str5)) {
/*  567 */                 str = str + " " + str5 + " and 1=2 ";
/*      */               } else {
/*  569 */                 str = str + " where 1=2 ";
/*      */               } 
/*      */               
/*  572 */               boolean bool = recordSetDataSource.executeSql(str);
/*  573 */               if (bool && arrayList1.size() > 0) {
/*  574 */                 for (byte b = 0; b < arrayList1.size(); b++) {
/*  575 */                   String str22 = Util.null2String(arrayList1.get(b));
/*  576 */                   String str23 = Util.null2String(arrayList2.get(b));
/*  577 */                   if (str22.equals("-")) str22 = ""; 
/*  578 */                   if (str23.equals("-")) str23 = ""; 
/*  579 */                   if (!str22.equals("")) {
/*  580 */                     str = "select 1 from " + str4 + "," + str22;
/*  581 */                     if (!str23.equals("")) {
/*  582 */                       str = str + " " + str23;
/*      */                     } else {
/*  584 */                       str = str + " where 1=2 ";
/*  585 */                     }  bool = recordSetDataSource.executeSql(str);
/*      */                     
/*  587 */                     if (!bool) {
/*      */ 
/*      */                       
/*  590 */                       this.messages = "" + (b + 1);
/*      */                       break;
/*      */                     } 
/*      */                   } 
/*      */                 } 
/*      */               } else {
/*  596 */                 this.messages = "0";
/*      */               } 
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       } 
/*  602 */       if (this.messages.equals("-1")) {
/*  603 */         automaticDetailTest(str3, str1);
/*      */       }
/*  605 */     } catch (Exception exception) {
/*  606 */       this.newlog.error(exception);
/*  607 */       exception.printStackTrace();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void automaticDetailTest() {
/*  614 */     automaticDetailTest(null, null);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void automaticDetailTest(String paramString1, String paramString2) {
/*      */     try {
/*  624 */       this.messages = "success";
/*  625 */       if (paramString1 == null)
/*  626 */         paramString1 = Util.null2String(this.request.getParameter("datasourceid")); 
/*  627 */       if (paramString2 == null)
/*  628 */         paramString2 = Util.null2String(this.request.getParameter("viewid")); 
/*  629 */       String str1 = "";
/*  630 */       String str2 = "";
/*  631 */       ArrayList<String> arrayList = new ArrayList();
/*  632 */       automaticcols automaticcols = new automaticcols();
/*  633 */       RecordSet recordSet = new RecordSet();
/*  634 */       recordSet.execute("select * from outerdatawfset where id=" + paramString2);
/*  635 */       if (recordSet.next()) {
/*  636 */         str1 = Util.null2String(recordSet.getString("outermaintable"));
/*  637 */         str2 = Util.null2String(recordSet.getString("outerdetailtables"));
/*      */         
/*  639 */         ArrayList<String> arrayList1 = automaticcols.getAllColumns(paramString1, str1);
/*  640 */         for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/*  641 */           String str = str1 + "." + (String)arrayList1.get(b1);
/*  642 */           arrayList.add(str);
/*      */         } 
/*      */         
/*  645 */         ArrayList<String> arrayList2 = Util.TokenizerString(str2, ",");
/*  646 */         for (byte b2 = 0; b2 < arrayList2.size(); b2++) {
/*  647 */           String str = arrayList2.get(b2);
/*  648 */           ArrayList<String> arrayList3 = automaticcols.getAllColumns(paramString1, str);
/*  649 */           for (byte b = 0; b < arrayList3.size(); b++) {
/*  650 */             String str3 = str + "." + (String)arrayList3.get(b);
/*  651 */             arrayList.add(str3);
/*      */           } 
/*      */         } 
/*      */       } 
/*  655 */       recordSet.execute("select * from outerdatawfsetdetail where mainid=" + paramString2 + " order by id");
/*  656 */       while (recordSet.next()) {
/*  657 */         String str = Util.null2String(recordSet.getString("outerfieldname"));
/*  658 */         if (!str.equals("") && arrayList.indexOf(str) < 0) {
/*  659 */           this.messages = "fielderror";
/*      */           break;
/*      */         } 
/*      */       } 
/*  663 */     } catch (Exception exception) {
/*  664 */       this.newlog.error(exception);
/*  665 */       exception.printStackTrace();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void actionTest() {
/*  672 */     String str1 = Util.null2String(this.request.getParameter("actionid"));
/*  673 */     String str2 = Util.null2String(this.request.getParameter("classname"));
/*  674 */     RecordSet recordSet = new RecordSet();
/*  675 */     if (str1.equals("") || str2.equals("")) {
/*  676 */       String str = Util.null2String(this.request.getParameter("dataid"));
/*  677 */       recordSet.execute("select actionname,actionclass from actionsetting where id='" + str + "'");
/*  678 */       recordSet.next();
/*  679 */       str1 = Util.null2String(recordSet.getString(1));
/*  680 */       str2 = Util.null2String(recordSet.getString(2));
/*      */     } 
/*      */     try {
/*  683 */       if (str2.indexOf("weaver.interfaces.workflow.action.javacode") != -1) {
/*      */         try {
/*  685 */           recordSet.execute("select javacode from actionsetting where actionname='" + str1 + "'");
/*  686 */           recordSet.next();
/*      */           
/*  688 */           String str3 = recordSet.getString("javacode");
/*      */ 
/*      */           
/*  691 */           DynamicEngine dynamicEngine = DynamicEngine.getInstance();
/*  692 */           String str4 = dynamicEngine.javaCodeToObjectTest(str2, str3);
/*  693 */           if (!"ok".equals(str4)) {
/*  694 */             this.messages = "compileerror_" + str4;
/*      */           }
/*  696 */         } catch (Exception exception) {
/*  697 */           this.messages = "compileerror_" + printStackTraceToString(exception);
/*      */         } 
/*      */       } else {
/*  700 */         Class<?> clazz = Class.forName(str2);
/*      */         
/*  702 */         Object object = clazz.newInstance();
/*  703 */         if (!(object instanceof weaver.interfaces.workflow.action.Action)) {
/*  704 */           this.messages = "classerror";
/*      */         } else {
/*  706 */           Field[] arrayOfField = clazz.getDeclaredFields();
/*  707 */           ArrayList<String> arrayList = new ArrayList();
/*  708 */           for (byte b = 0; b < arrayOfField.length; b++) {
/*  709 */             arrayList.add(arrayOfField[b].getName());
/*      */           }
/*  711 */           recordSet.execute("select t1.attrname from actionsettingdetail t1 left join actionsetting t2 on t1.actionid=t2.id where t2.actionname='" + str1 + "'");
/*  712 */           while (recordSet.next()) {
/*  713 */             String str = Util.null2String(recordSet.getString("attrname"));
/*  714 */             if (arrayList.indexOf(str) < 0) {
/*  715 */               this.messages = "fielderror_" + str + "_" + str2;
/*      */               break;
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       } 
/*  721 */     } catch (Throwable throwable) {
/*  722 */       this.messages = "noclasserror_" + str2;
/*  723 */       this.newlog.error(throwable);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> getBrowserDataListForSearch(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  737 */     RecordSet recordSet = new RecordSet();
/*  738 */     String str1 = paramUser.getUID() + "";
/*  739 */     String str2 = Util.null2String(paramMap.get("name"));
/*  740 */     int i = Util.getIntValue(Util.null2String(paramMap.get("pageSize")), 10);
/*  741 */     int j = Util.getIntValue(Util.null2String(paramMap.get("pageIndex")), 1);
/*  742 */     String str3 = Util.null2String(paramMap.get("showOrder"));
/*      */     
/*  744 */     String str4 = Util.null2String(paramMap.get("actionname"));
/*  745 */     Browser browser = (Browser)StaticObj.getServiceByFullname("browser." + str4, Browser.class);
/*  746 */     String str5 = browser.getFrom();
/*  747 */     String str6 = replaceFieldValue(browser.getSearch() + " ");
/*  748 */     String str7 = replaceFieldValue(browser.getSearchByName() + " ");
/*      */     
/*  750 */     Map map = browser.getSearchfieldMap();
/*  751 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  752 */     if (null != map) {
/*  753 */       Set set = map.keySet();
/*  754 */       for (String str8 : set) {
/*      */         
/*  756 */         String str9 = Util.null2String(paramMap.get(str8));
/*  757 */         if (!"".equals(str9)) {
/*  758 */           hashMap.put(str8, str9.replace("@#add#@", "+"));
/*      */         }
/*      */       } 
/*      */     } 
/*  762 */     Map<String, String> map1 = browser.getParamvalues();
/*  763 */     if (null != map1) {
/*  764 */       Set set = map1.keySet();
/*  765 */       for (String str8 : set) {
/*      */         
/*  767 */         String str9 = Util.null2String(paramMap.get(str8));
/*  768 */         if (!"".equals(str9))
/*  769 */           map1.put(str8, str9.replace("@#add#@", "+")); 
/*      */       } 
/*  771 */       browser.setParamvalues(map1);
/*      */     } 
/*  773 */     FieldInputTools fieldInputTools = new FieldInputTools();
/*  774 */     List<String> list1 = fieldInputTools.getSQLFieldList(str6, "1");
/*  775 */     List<String> list2 = fieldInputTools.getSQLFieldList(str7, "1");
/*  776 */     for (byte b = 0; b < list1.size(); b++) {
/*  777 */       String str8 = Util.null2String(list1.get(b));
/*  778 */       String str9 = Util.null2String(paramMap.get(str8 + "_search"));
/*  779 */       str9 = rebuildMultiFieldValue(str9.replace("@#add#@", "+"));
/*  780 */       if ("".equals(str9)) {
/*  781 */         str9 = "''";
/*      */       }
/*  783 */       str6 = str6.replace("$" + str8 + "$", str9);
/*  784 */       str7 = str7.replace("$" + str8 + "$", str9);
/*  785 */       if (str8.indexOf("_sys_current_login_") == 0) {
/*  786 */         str6 = str6.replace("{?" + str8.substring(19) + "}", str9);
/*  787 */         str7 = str7.replace("{?" + str8.substring(19) + "}", str9);
/*      */       } 
/*      */     } 
/*  790 */     List list = null;
/*  791 */     if ("2".equals(str5)) {
/*  792 */       if (hashMap.size() > 0) {
/*  793 */         list = browser.search(str1, str6, hashMap);
/*      */       } else {
/*  795 */         list = browser.search(str1, str6);
/*      */       }
/*      */     
/*  798 */     } else if (str2.equals("")) {
/*  799 */       list = browser.search(str1, str6);
/*      */     } else {
/*  801 */       list = browser.searchByName(str1, str2, str7);
/*      */     } 
/*      */     
/*  804 */     return setDataMap(list, i, j, browser.getShowfieldMap(), browser.getFrom(), str3);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> getDataResourceListForSearch(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws Exception {
/*  818 */     String str1 = Util.null2String(paramMap.get("showtypeid"));
/*  819 */     String str2 = Util.null2String(paramMap.get("issearch"));
/*  820 */     int i = Util.getIntValue(Util.null2String(paramMap.get("pageSize")), 10);
/*  821 */     int j = Util.getIntValue(Util.null2String(paramMap.get("pageIndex")), 1);
/*  822 */     String str3 = Util.null2String(paramMap.get("name"));
/*  823 */     String str4 = Util.null2String(paramMap.get("showOrder"));
/*      */     
/*  825 */     String str5 = paramUser.getUID() + "";
/*  826 */     BaseBrowser baseBrowser = new BaseBrowser();
/*  827 */     baseBrowser.initBaseBrowser(str1, "2", "2");
/*  828 */     String str6 = baseBrowser.getShowname();
/*  829 */     String str7 = Util.null2String(baseBrowser.getOutPageURL());
/*  830 */     String str8 = Util.null2String(baseBrowser.getHref());
/*  831 */     str8 = Util.null2String(baseBrowser.getHref("" + paramUser.getUID(), str8));
/*  832 */     String str9 = Util.null2String(baseBrowser.getFrom());
/*  833 */     String str10 = Util.null2String(baseBrowser.getShowtree());
/*      */     
/*  835 */     String str11 = Util.null2String(Util.null2String(Integer.valueOf(baseBrowser.getShowclass())), "1");
/*      */     
/*  837 */     String str12 = baseBrowser.getDatasourceid();
/*  838 */     if (!"".equals(str12)) {
/*  839 */       DataSource dataSource = (DataSource)StaticObj.getServiceByFullname(str12, DataSource.class);
/*  840 */       baseBrowser.setDs(dataSource);
/*      */     } 
/*  842 */     Map map1 = baseBrowser.getSearchfieldMap();
/*  843 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  844 */     String str13 = "";
/*  845 */     if (null != map1) {
/*  846 */       Set set = map1.keySet();
/*  847 */       int k = set.size();
/*  848 */       boolean bool = false;
/*  849 */       for (String str16 : set) {
/*      */         
/*  851 */         String str17 = (String)map1.get(str16);
/*  852 */         if ("".equals(str17))
/*      */           continue; 
/*  854 */         String str18 = Util.null2String(paramMap.get(str16));
/*  855 */         str13 = Util.null2String(paramMap.get("simple_" + str16));
/*      */         
/*  857 */         if (!"".equals(str13)) {
/*  858 */           hashMap.put(str16, str13); continue;
/*  859 */         }  if (!"".equals(str18)) {
/*  860 */           hashMap.put(str16, str18);
/*      */         }
/*      */       } 
/*      */     } 
/*      */     
/*  865 */     String str14 = baseBrowser.getSearch() + " ";
/*  866 */     String str15 = baseBrowser.getSearchByName() + " ";
/*  867 */     FieldInputTools fieldInputTools = new FieldInputTools();
/*  868 */     List<String> list1 = fieldInputTools.getSQLFieldList(str14, str11);
/*  869 */     List<String> list2 = fieldInputTools.getSQLFieldList(str15, str11);
/*  870 */     for (byte b = 0; b < list1.size(); b++) {
/*  871 */       String str16 = Util.null2String(list1.get(b));
/*  872 */       String str17 = Util.null2String(paramMap.get(str16 + "_search"));
/*  873 */       str17 = rebuildMultiFieldValue(str17);
/*  874 */       if ("".equals(str17)) {
/*  875 */         str17 = "''";
/*      */       }
/*  877 */       str14 = str14.replace("$" + str16 + "$", str17);
/*  878 */       str15 = str15.replace("$" + str16 + "$", str17);
/*  879 */       if (str16.indexOf("_sys_current_login_") == 0) {
/*  880 */         str14 = str14.replace("{?" + str16.substring(19) + "}", str17);
/*  881 */         str15 = str15.replace("{?" + str16.substring(19) + "}", str17);
/*      */       } 
/*      */     } 
/*  884 */     List list = null;
/*      */     
/*  886 */     if (!"2".equals(str9)) {
/*  887 */       if (str3.equals("")) {
/*  888 */         list = baseBrowser.search(str5, str14);
/*      */       } else {
/*  890 */         list = baseBrowser.searchByName(str5, str3, str15);
/*      */       } 
/*  892 */     } else if (hashMap.size() > 0) {
/*      */ 
/*      */       
/*  895 */       list = baseBrowser.search(str5, str14, hashMap);
/*      */     } else {
/*  897 */       list = baseBrowser.search(str5, str14);
/*      */     } 
/*      */     
/*  900 */     Map map2 = baseBrowser.getShowfieldMap();
/*  901 */     return setDataMap(list, i, j, map2, str9, str4);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, Object> setDataMap(List paramList, int paramInt1, int paramInt2, Map paramMap, String paramString1, String paramString2) {
/*  915 */     ArrayList arrayList = new ArrayList();
/*  916 */     int i = paramList.size();
/*  917 */     int j = (i % paramInt1 == 0) ? (i / paramInt1) : (i / paramInt1 + 1);
/*  918 */     if (paramInt2 > j)
/*  919 */       paramInt2 = j; 
/*  920 */     if (paramInt2 < 1) {
/*  921 */       paramInt2 = 1;
/*      */     }
/*  923 */     int k = (paramInt2 - 1) * paramInt1;
/*  924 */     int m = paramInt2 * paramInt1;
/*  925 */     if (m > i)
/*  926 */       m = i; 
/*  927 */     if (paramString2.equals("all")) {
/*  928 */       arrayList.addAll(paramList);
/*      */     } else {
/*  930 */       for (int n = k; n < m; n++) {
/*  931 */         arrayList.add(paramList.get(n));
/*      */       }
/*      */     } 
/*      */     
/*  935 */     ArrayList<HashMap<Object, Object>> arrayList1 = new ArrayList();
/*  936 */     if (null != arrayList && arrayList.size() > 0) {
/*  937 */       Iterator<BrowserBean> iterator = arrayList.iterator();
/*  938 */       while (iterator.hasNext()) {
/*  939 */         BrowserBean browserBean = iterator.next();
/*  940 */         String str = browserBean.getId();
/*  941 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  942 */         hashMap1.put("ids", str);
/*  943 */         if (!"2".equals(paramString1)) {
/*  944 */           String str1 = Util.null2String(browserBean.getName());
/*  945 */           String str2 = Util.null2String(browserBean.getDescription());
/*      */           
/*  947 */           hashMap1.put("names", str1);
/*  948 */           hashMap1.put("descs", str2);
/*  949 */           arrayList1.add(hashMap1); continue;
/*      */         } 
/*  951 */         Map map = browserBean.getValueMap();
/*  952 */         Set set = paramMap.keySet();
/*  953 */         for (String str1 : set) {
/*      */           
/*  955 */           String str2 = (String)paramMap.get(str1);
/*      */           
/*  957 */           String str3 = Util.null2String((String)map.get(str1));
/*      */           
/*  959 */           hashMap1.put(str1 + "s", str3);
/*      */         } 
/*      */         
/*  962 */         arrayList1.add(hashMap1);
/*      */       } 
/*      */     } 
/*      */     
/*  966 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  967 */     hashMap.put("dataAll", arrayList1);
/*  968 */     hashMap.put("recordCount", "" + i);
/*      */     
/*  970 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String loadBrowserTreeData(String paramString) {
/*  978 */     String str1 = Util.null2String(this.request.getParameter("node"));
/*  979 */     String str2 = Util.null2String(this.request.getParameter("type"));
/*  980 */     String str3 = Util.null2String(this.request.getParameter("checked"));
/*  981 */     String str4 = "";
/*  982 */     if (!"".equals(str1) && !"root".equals(str1))
/*  983 */       str4 = str1; 
/*  984 */     Browser browser = (Browser)StaticObj.getServiceByFullname("browser." + str2, Browser.class);
/*  985 */     String str5 = Util.null2String(browser.getSearch()) + " ";
/*  986 */     JSONArray jSONArray = new JSONArray();
/*      */     
/*  988 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  989 */     String str6 = browser.getParentfield();
/*  990 */     int i = browser.getDatafrom();
/*  991 */     if (1 == i && 
/*  992 */       "".equals(str4)) {
/*  993 */       str4 = "0";
/*      */     }
/*      */     
/*  996 */     hashMap.put(str6, str4);
/*  997 */     browser.setSearchValueMap(hashMap);
/*      */     
/*  999 */     FieldInputTools fieldInputTools = new FieldInputTools();
/* 1000 */     List<String> list = fieldInputTools.getSQLFieldList(str5, "1");
/* 1001 */     for (byte b = 0; b < list.size(); b++) {
/* 1002 */       String str7 = Util.null2String(list.get(b));
/* 1003 */       String str8 = Util.null2String(this.request.getParameter(str7 + "_search"));
/* 1004 */       str8 = rebuildMultiFieldValue(str8);
/* 1005 */       if ("".equals(str8)) {
/* 1006 */         str8 = "''";
/*      */       }
/* 1008 */       str5 = str5.replace("$" + str7 + "$", str8);
/* 1009 */       if (str7.indexOf("_sys_current_login_") == 0) {
/* 1010 */         str5 = str5.replace("{?" + str7.substring(19) + "}", str8);
/*      */       }
/*      */     } 
/* 1013 */     List list1 = browser.search(paramString, str5, hashMap);
/* 1014 */     Iterator<BrowserBean> iterator = list1.iterator();
/*      */     try {
/* 1016 */       while (iterator.hasNext()) {
/* 1017 */         BrowserBean browserBean = iterator.next();
/* 1018 */         String str7 = browserBean.getId();
/* 1019 */         String str8 = browserBean.getName();
/* 1020 */         String str9 = browserBean.getParentId();
/* 1021 */         JSONObject jSONObject = new JSONObject();
/* 1022 */         jSONObject.put("id", str7);
/* 1023 */         jSONObject.put("pId", str9);
/* 1024 */         jSONObject.put("name", str8);
/* 1025 */         jSONObject.put("open", "false");
/* 1026 */         jSONArray.put(jSONObject);
/*      */       } 
/* 1028 */     } catch (Exception exception) {
/*      */       
/* 1030 */       exception.printStackTrace();
/*      */     } 
/* 1032 */     return jSONArray.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String replaceFieldValue(String paramString) {
/* 1048 */     return Util.null2String(paramString);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String rebuildMultiFieldValue(String paramString) {
/* 1058 */     String str = "";
/* 1059 */     if (paramString.indexOf(",") > -1) {
/* 1060 */       String[] arrayOfString = paramString.split(",");
/* 1061 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 1062 */         if (!arrayOfString[b].equals("")) {
/* 1063 */           if (str.equals("")) {
/* 1064 */             str = str + "'";
/*      */           } else {
/* 1066 */             str = str + ",'";
/*      */           } 
/*      */           
/* 1069 */           str = str + arrayOfString[b] + "'";
/*      */         } 
/*      */       } 
/*      */     } else {
/* 1073 */       str = paramString;
/*      */     } 
/*      */     
/* 1076 */     return str;
/*      */   }
/*      */   
/*      */   public HttpServletRequest getRequest() {
/* 1080 */     return this.request;
/*      */   }
/*      */   
/*      */   public void setRequest(HttpServletRequest paramHttpServletRequest) {
/* 1084 */     this.request = paramHttpServletRequest;
/*      */   }
/*      */   
/*      */   public String getMessages() {
/* 1088 */     return this.messages;
/*      */   }
/*      */   
/*      */   public void setMessages(String paramString) {
/* 1092 */     this.messages = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String printStackTraceToString(Throwable paramThrowable) {
/* 1100 */     StringWriter stringWriter = new StringWriter();
/* 1101 */     paramThrowable.printStackTrace(new PrintWriter(stringWriter, true));
/* 1102 */     return stringWriter.getBuffer().toString();
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/conftest/ConfigureTestUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */