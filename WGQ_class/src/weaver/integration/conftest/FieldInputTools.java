/*     */ package weaver.integration.conftest;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.interfaces.workflow.browser.Browser;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.dmlaction.DBTypeUtil;
/*     */ import weaver.workflow.dmlaction.commands.bases.FieldBase;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FieldInputTools
/*     */ {
/*  31 */   private Logger logger = LoggerFactory.getLogger(FieldInputTools.class);
/*     */   
/*  33 */   private HttpServletRequest request = null;
/*     */   
/*  35 */   private RecordSet rs = null;
/*     */   
/*  37 */   private String actionid = "";
/*     */   
/*  39 */   private String actionname = "";
/*     */   
/*  41 */   private String datatype = "";
/*     */   
/*  43 */   private Map mainsetMap = new HashMap<>();
/*     */   
/*  45 */   private User user = null;
/*     */   
/*  47 */   private Browser browser = null;
/*     */   
/*     */   public Map getInputFieldName() {
/*  50 */     if (this.datatype.equals("dml")) {
/*  51 */       return getDmlInputFieldName();
/*     */     }
/*  53 */     return getBrowserFieldName();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Map getBrowserFieldName() {
/*  62 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  63 */     if (!this.actionid.equals("") || !this.actionname.equals("")) {
/*  64 */       this.rs = new RecordSet();
/*  65 */       this.rs.execute("select showclass from datashowset where id=" + this.actionid + " or showname='" + this.actionname + "'");
/*  66 */       this.rs.next();
/*  67 */       String str = this.rs.getString(1);
/*  68 */       List<String> list1 = getSQLFieldList(this.browser.getSearch(), str);
/*  69 */       List<String> list2 = getSQLFieldList(this.browser.getSearchById(), str);
/*  70 */       hashMap.put("searchFieldList", list1);
/*  71 */       hashMap.put("searchByIdFieldList", list2);
/*     */     } 
/*  73 */     return hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map getDMLFinallySQL() {
/*  82 */     this.rs = new RecordSet();
/*  83 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  84 */     if (this.mainsetMap.size() < 1)
/*  85 */       getDmlActionSet(); 
/*  86 */     Map map = getDmlInputFieldName();
/*  87 */     String str1 = Util.null2String(this.mainsetMap.get("dmltype"));
/*  88 */     String str2 = Util.null2String(this.mainsetMap.get("dmlsql"));
/*  89 */     String str3 = Util.null2String(this.mainsetMap.get("dmlcuswhere"));
/*  90 */     String str4 = Util.null2String(this.mainsetMap.get("dmlcussql"));
/*  91 */     List list1 = (List)map.get("sqlwherefieldlist");
/*  92 */     List list2 = (List)map.get("custsqlfieldlist");
/*  93 */     str3 = setDmlCusSqlOrWhereValues(list1, str3, "whsql");
/*  94 */     str4 = setDmlCusSqlOrWhereValues(list2, str4, "cussql");
/*     */     
/*  96 */     if (str1.equals("insert")) {
/*  97 */       str3 = "";
/*     */     }
/*  99 */     if (!str1.equals("custom")) {
/* 100 */       str4 = "";
/*     */     }
/* 102 */     str2 = loadPropsql(str2, str3, str4);
/*     */     
/* 104 */     List list3 = (List)map.get("valuefieldlist");
/* 105 */     List list4 = (List)map.get("wherefieldlist");
/* 106 */     str2 = packagesSQL(str2, list3, list4);
/* 107 */     hashMap.put("dmlSQL", str2);
/* 108 */     hashMap.put("dmlcussql", str4);
/* 109 */     return hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map getDmlInputFieldName() {
/* 118 */     if (this.mainsetMap.size() < 1)
/* 119 */       getDmlActionSet(); 
/* 120 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 121 */     this.rs = new RecordSet();
/* 122 */     this.rs.execute("select formid,isbill,dmltype from formactionset where id=" + this.actionid);
/* 123 */     String str1 = "";
/* 124 */     String str2 = "1";
/* 125 */     String str3 = "";
/* 126 */     if (this.rs.next()) {
/* 127 */       str1 = this.rs.getString("formid");
/* 128 */       str2 = this.rs.getString("isbill");
/* 129 */       str3 = this.rs.getString("dmltype");
/*     */     } 
/* 131 */     ArrayList arrayList1 = new ArrayList();
/* 132 */     ArrayList arrayList2 = new ArrayList();
/* 133 */     if (!str3.equals("insert")) {
/* 134 */       String str = Util.null2String(this.mainsetMap.get("dmlcuswhere"));
/* 135 */       loadRequestFieldMap(str, arrayList1);
/*     */     } 
/* 137 */     if (str3.equals("custom")) {
/* 138 */       String str = Util.null2String(this.mainsetMap.get("dmlcussql"));
/* 139 */       loadRequestFieldMap(str, arrayList2);
/*     */     } 
/*     */ 
/*     */     
/* 143 */     FieldBase fieldBase = new FieldBase();
/* 144 */     fieldBase.getFormTableFields(this.user, this.rs, Util.getIntValue(str1), Util.getIntValue(str2), 0);
/*     */     
/* 146 */     List<String> list = fieldBase.getFieldList();
/* 147 */     Map map1 = fieldBase.getFieldLabelMap();
/* 148 */     Map map2 = fieldBase.getFieldNameMap();
/* 149 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 150 */     for (byte b = 0; b < list.size(); b++) {
/* 151 */       String str4 = list.get(b);
/* 152 */       String str5 = Util.null2String((String)map2.get(str4));
/* 153 */       String str6 = Util.null2String((String)map1.get(str4));
/*     */       
/* 155 */       hashMap2.put(str5, str6);
/*     */     } 
/*     */     
/* 158 */     ArrayList<String> arrayList3 = new ArrayList();
/* 159 */     ArrayList<String> arrayList4 = new ArrayList();
/* 160 */     if (!str3.equals("custom")) {
/* 161 */       String str = "select fieldvalue,maptype from formactionfieldmap where actionsqlsetid=" + this.actionid;
/* 162 */       if (str3.equals("delete")) {
/* 163 */         str = str + " and maptype <> '0'";
/* 164 */       } else if (str3.equals("insert")) {
/* 165 */         str = str + " and maptype <> '1'";
/* 166 */       }  str = str + " order by maptype,id";
/* 167 */       this.rs.execute(str);
/* 168 */       while (this.rs.next()) {
/* 169 */         String str4 = Util.null2String(this.rs.getString("fieldvalue"));
/* 170 */         String str5 = Util.null2String(this.rs.getString("maptype"));
/* 171 */         if (str5.equals("0")) {
/* 172 */           arrayList3.add(str4); continue;
/* 173 */         }  if (str5.equals("1")) {
/* 174 */           arrayList4.add(str4);
/*     */         }
/*     */       } 
/*     */     } 
/* 178 */     hashMap1.put("dmltype", str3);
/* 179 */     hashMap1.put("namelabelMap", hashMap2);
/* 180 */     hashMap1.put("valuefieldlist", arrayList3);
/* 181 */     hashMap1.put("wherefieldlist", arrayList4);
/* 182 */     hashMap1.put("sqlwherefieldlist", arrayList1);
/* 183 */     hashMap1.put("custsqlfieldlist", arrayList2);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 189 */     return hashMap1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List getInputFieldByDmlcuswhere() {
/* 198 */     ArrayList arrayList = new ArrayList();
/* 199 */     String str1 = Util.null2String(this.mainsetMap.get("dmlcuswhere"));
/* 200 */     String str2 = Util.null2String(this.mainsetMap.get("dmlcussql"));
/* 201 */     loadRequestFieldMap(str1, arrayList);
/* 202 */     loadRequestFieldMap(str2, arrayList);
/* 203 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void getDmlActionSet() {
/*     */     try {
/* 215 */       this.rs = new RecordSet();
/* 216 */       StringBuffer stringBuffer = new StringBuffer();
/* 217 */       stringBuffer.append("select t.id,");
/* 218 */       stringBuffer.append("  f.datasourceid,");
/* 219 */       stringBuffer.append("  f.dmltype,");
/* 220 */       stringBuffer.append("  t.dmlsql,");
/* 221 */       stringBuffer.append("  t.dmlfieldtypes,");
/* 222 */       stringBuffer.append("  t.dmlfieldnames,");
/* 223 */       stringBuffer.append("  t.dmlcuswhere,");
/* 224 */       stringBuffer.append("  t.dmlmainsqltype,");
/* 225 */       stringBuffer.append("  t.dmlcussql,");
/* 226 */       stringBuffer.append("  f.dmlactionname");
/* 227 */       stringBuffer.append("    from formactionset f,formactionsqlset t");
/* 228 */       stringBuffer.append("  where f.id=t.actionid and f.id=" + this.actionid);
/*     */       
/* 230 */       this.rs.executeSql(stringBuffer.toString());
/* 231 */       if (this.rs.next()) {
/* 232 */         this.mainsetMap.put("id", this.rs.getString("id"));
/* 233 */         this.mainsetMap.put("datasourceid", this.rs.getString("datasourceid"));
/* 234 */         this.mainsetMap.put("dmltype", this.rs.getString("dmltype"));
/* 235 */         this.mainsetMap.put("dmlsql", this.rs.getString("dmlsql"));
/* 236 */         this.mainsetMap.put("dmlfieldtypes", this.rs.getString("dmlfieldtypes"));
/* 237 */         this.mainsetMap.put("dmlfieldnames", this.rs.getString("dmlfieldnames"));
/* 238 */         this.mainsetMap.put("dmlcuswhere", this.rs.getString("dmlcuswhere"));
/* 239 */         this.mainsetMap.put("dmlmainsqltype", this.rs.getString("dmlmainsqltype"));
/* 240 */         this.mainsetMap.put("dmlcussql", this.rs.getString("dmlcussql"));
/* 241 */         this.mainsetMap.put("dmlactionname", this.rs.getString("dmlactionname"));
/*     */       } 
/* 243 */     } catch (Exception exception) {
/* 244 */       this.logger.error(exception);
/* 245 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */   
/*     */   private void doDmlTest() throws Exception {
/* 250 */     String str = Util.null2String(this.request.getParameter("actionid"));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void loadRequestFieldMap(String paramString, List<String> paramList) {
/* 259 */     if (!"".equals(paramString)) {
/* 260 */       Pattern pattern = Pattern.compile("(\\{?[a-zA-Z][a-zA-Z0-9_]*\\})");
/* 261 */       Matcher matcher = pattern.matcher(paramString);
/* 262 */       while (matcher.find()) {
/* 263 */         String str = matcher.group();
/* 264 */         str = str.substring(0, str.length() - 1);
/* 265 */         if (paramList.indexOf(str) < 0) {
/* 266 */           paramList.add(str);
/*     */         }
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String setDmlCusSqlOrWhereValues(List paramList, String paramString1, String paramString2) {
/* 281 */     for (byte b = 0; b < paramList.size(); b++) {
/* 282 */       String str1 = Util.null2String(paramList.get(b));
/* 283 */       String str2 = Util.null2String(this.request.getParameter(str1 + "_" + paramString2));
/* 284 */       paramString1 = DBTypeUtil.replaceStringIgnoreCase(paramString1, "\\{\\?" + str1.toLowerCase() + "\\}", str2);
/*     */     } 
/* 286 */     return paramString1;
/*     */   }
/*     */   
/*     */   private String loadPropsql(String paramString1, String paramString2, String paramString3) {
/* 290 */     if (!"".equals(paramString2)) {
/* 291 */       if (StringUtils.isNotBlank(paramString1)) {
/* 292 */         if (paramString1.toLowerCase().indexOf(" where ") > -1) {
/* 293 */           paramString1 = paramString1 + " and " + paramString2;
/*     */         } else {
/* 295 */           paramString1 = paramString1 + " where " + paramString2;
/*     */         } 
/*     */       }
/* 298 */       if (StringUtils.isNotBlank(paramString3)) {
/* 299 */         if (paramString3.toLowerCase().indexOf(" where ") > -1) {
/* 300 */           paramString3 = paramString3 + " and " + paramString2;
/*     */         } else {
/* 302 */           paramString3 = paramString3 + " where " + paramString2;
/*     */         } 
/*     */       }
/*     */     } 
/* 306 */     return paramString1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String packagesSQL(String paramString, List paramList1, List paramList2) {
/* 317 */     ArrayList<String> arrayList = new ArrayList(); byte b1;
/* 318 */     for (b1 = 0; b1 < paramList1.size(); b1++) {
/* 319 */       String str1 = Util.null2String(this.request.getParameter((new StringBuilder()).append(paramList1.get(b1)).append("_value").toString()));
/* 320 */       arrayList.add(str1);
/*     */     } 
/* 322 */     for (b1 = 0; b1 < paramList2.size(); b1++) {
/* 323 */       String str1 = Util.null2String(this.request.getParameter((new StringBuilder()).append(paramList2.get(b1)).append("_where").toString()));
/* 324 */       arrayList.add(str1);
/*     */     } 
/* 326 */     String[] arrayOfString = paramString.split("\\?");
/* 327 */     String str = "";
/* 328 */     boolean bool = false;
/* 329 */     for (byte b2 = 0; b2 < arrayOfString.length; b2++) {
/* 330 */       if (!paramString.endsWith("?") && b2 == arrayOfString.length - 1) {
/* 331 */         str = str + arrayOfString[b2];
/*     */       } else {
/* 333 */         str = str + arrayOfString[b2] + arrayList.get(b2);
/*     */       } 
/*     */     } 
/* 336 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<String> getSQLFieldList(String paramString1, String paramString2) {
/* 343 */     ArrayList<String> arrayList = new ArrayList();
/* 344 */     if (paramString2 == null || "1".equals(paramString2)) {
/* 345 */       Pattern pattern = Pattern.compile("(\\$[a-zA-Z][a-zA-Z0-9_]*\\$)|(\\$formtable_main_[0-9]+_dt[0-9]+_[a-zA-Z][a-zA-Z0-9_]*\\$)|(\\$detail_[a-zA-Z][a-zA-Z0-9_]*\\$)");
/* 346 */       Matcher matcher = pattern.matcher(paramString1);
/* 347 */       while (matcher.find()) {
/* 348 */         String str = matcher.group();
/* 349 */         str = str.substring(1, str.length() - 1);
/* 350 */         arrayList.add(str);
/*     */       } 
/*     */     } 
/* 353 */     if (paramString1.indexOf("{?userid}") > -1) {
/* 354 */       arrayList.add("_sys_current_login_userid");
/* 355 */     } else if (paramString1.indexOf("{?loginid}") > -1) {
/* 356 */       arrayList.add("_sys_current_login_loginid");
/* 357 */     } else if (paramString1.indexOf("{?username}") > -1) {
/* 358 */       arrayList.add("_sys_current_login_username");
/* 359 */     } else if (paramString1.indexOf("{?workcode}") > -1) {
/* 360 */       arrayList.add("_sys_current_login_workcode");
/* 361 */     } else if (paramString1.indexOf("{?password}") > -1) {
/* 362 */       arrayList.add("_sys_current_login_password");
/* 363 */     } else if (paramString1.indexOf("{?departmentid}") > -1) {
/* 364 */       arrayList.add("_sys_current_login_departmentid");
/* 365 */     } else if (paramString1.indexOf("{?departmentcode}") > -1) {
/* 366 */       arrayList.add("_sys_current_login_departmentcode");
/* 367 */     } else if (paramString1.indexOf("{?departmentname}") > -1) {
/* 368 */       arrayList.add("_sys_current_login_departmentname");
/* 369 */     } else if (paramString1.indexOf("{?subcompanyid}") > -1) {
/* 370 */       arrayList.add("_sys_current_login_subcompanyid");
/* 371 */     } else if (paramString1.indexOf("{?subcompanycode}") > -1) {
/* 372 */       arrayList.add("_sys_current_login_subcompanycode");
/* 373 */     } else if (paramString1.indexOf("{?subcompanyname}") > -1) {
/* 374 */       arrayList.add("_sys_current_login_subcompanyname");
/*     */     } 
/* 376 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map loadSysCurrentLoginParamLabel() {
/* 384 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 385 */     hashMap.put("_sys_current_login_userid", SystemEnv.getHtmlLabelName(32679, this.user.getLanguage()));
/* 386 */     hashMap.put("_sys_current_login_loginid", SystemEnv.getHtmlLabelName(16017, this.user.getLanguage()));
/* 387 */     hashMap.put("_sys_current_login_workcode", SystemEnv.getHtmlLabelName(714, this.user.getLanguage()));
/* 388 */     hashMap.put("_sys_current_login_password", SystemEnv.getHtmlLabelName(409, this.user.getLanguage()));
/* 389 */     hashMap.put("_sys_current_login_departmentid", SystemEnv.getHtmlLabelName(22279, this.user.getLanguage()));
/* 390 */     hashMap.put("_sys_current_login_departmentcode", SystemEnv.getHtmlLabelName(22806, this.user.getLanguage()));
/* 391 */     hashMap.put("_sys_current_login_departmentname", SystemEnv.getHtmlLabelName(15390, this.user.getLanguage()));
/* 392 */     hashMap.put("_sys_current_login_subcompanyid", SystemEnv.getHtmlLabelName(141, this.user.getLanguage()) + "ID");
/* 393 */     hashMap.put("_sys_current_login_subcompanycode", SystemEnv.getHtmlLabelName(22809, this.user.getLanguage()));
/* 394 */     hashMap.put("_sys_current_login_subcompanyname", SystemEnv.getHtmlLabelName(1878, this.user.getLanguage()));
/* 395 */     return hashMap;
/*     */   }
/*     */   
/*     */   public HttpServletRequest getRequest() {
/* 399 */     return this.request;
/*     */   }
/*     */   
/*     */   public void setRequest(HttpServletRequest paramHttpServletRequest) {
/* 403 */     this.request = paramHttpServletRequest;
/*     */   }
/*     */   
/*     */   public String getActionid() {
/* 407 */     return this.actionid;
/*     */   }
/*     */   
/*     */   public void setActionid(String paramString) {
/* 411 */     this.actionid = paramString;
/*     */   }
/*     */   
/*     */   public String getDatatype() {
/* 415 */     return this.datatype;
/*     */   }
/*     */   
/*     */   public void setDatatype(String paramString) {
/* 419 */     this.datatype = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {}
/*     */ 
/*     */   
/*     */   public Map getMainsetMap() {
/* 427 */     return this.mainsetMap;
/*     */   }
/*     */   
/*     */   public void setMainsetMap(Map paramMap) {
/* 431 */     this.mainsetMap = paramMap;
/*     */   }
/*     */   
/*     */   public User getUser() {
/* 435 */     return this.user;
/*     */   }
/*     */   
/*     */   public void setUser(User paramUser) {
/* 439 */     this.user = paramUser;
/*     */   }
/*     */   
/*     */   public String getActionname() {
/* 443 */     return this.actionname;
/*     */   }
/*     */   
/*     */   public void setActionname(String paramString) {
/* 447 */     this.actionname = paramString;
/*     */   }
/*     */   
/*     */   public Browser getBrowser() {
/* 451 */     return this.browser;
/*     */   }
/*     */   
/*     */   public void setBrowser(Browser paramBrowser) {
/* 455 */     this.browser = paramBrowser;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/conftest/FieldInputTools.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */