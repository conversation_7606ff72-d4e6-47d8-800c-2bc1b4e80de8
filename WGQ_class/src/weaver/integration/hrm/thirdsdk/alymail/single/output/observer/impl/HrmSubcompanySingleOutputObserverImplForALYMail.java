/*    */ package weaver.integration.hrm.thirdsdk.alymail.single.output.observer.impl;
/*    */ 
/*    */ import java.util.List;
/*    */ import weaver.integration.framework.data.field.FieldData;
/*    */ import weaver.integration.framework.data.record.SimpleRecordData;
/*    */ import weaver.integration.hrm.output.observer.IObserver;
/*    */ import weaver.integration.hrm.thirdsdk.alymail.single.output.mapping.impl.HrmSubcompanySingleOutputMappingImplForALYMail;
/*    */ import weaver.integration.thirdsdk.alymail.biz.HrmSubcompanySubscriber4ALY;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmSubcompanySingleOutputObserverImplForALYMail
/*    */   implements IObserver<SimpleRecordData>
/*    */ {
/*    */   public void save(SimpleRecordData paramSimpleRecordData) {
/* 17 */     HrmSubcompanySingleOutputMappingImplForALYMail hrmSubcompanySingleOutputMappingImplForALYMail = new HrmSubcompanySingleOutputMappingImplForALYMail();
/* 18 */     List<FieldData> list = hrmSubcompanySingleOutputMappingImplForALYMail.execute(paramSimpleRecordData);
/* 19 */     System.out.println(list);
/*    */     
/* 21 */     callThirdSdk(list, paramSimpleRecordData.getAction());
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private void callThirdSdk(List<FieldData> paramList, String paramString) {
/* 29 */     (new HrmSubcompanySubscriber4ALY()).synData(paramList, paramString);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/hrm/thirdsdk/alymail/single/output/observer/impl/HrmSubcompanySingleOutputObserverImplForALYMail.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */