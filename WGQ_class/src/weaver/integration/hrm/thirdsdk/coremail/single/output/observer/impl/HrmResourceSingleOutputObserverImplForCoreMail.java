/*    */ package weaver.integration.hrm.thirdsdk.coremail.single.output.observer.impl;
/*    */ 
/*    */ import java.util.List;
/*    */ import weaver.integration.framework.data.field.FieldData;
/*    */ import weaver.integration.framework.data.record.SimpleRecordData;
/*    */ import weaver.integration.hrm.output.observer.IObserver;
/*    */ import weaver.integration.hrm.thirdsdk.coremail.single.output.mapping.impl.HrmResourceSingleOutputMappingImplForCoreMail;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ import weaver.integration.thirdsdk.coremail.biz.HrmResourceSubscriber4CoreMail;
/*    */ 
/*    */ public class HrmResourceSingleOutputObserverImplForCoreMail
/*    */   implements IObserver<SimpleRecordData>
/*    */ {
/* 15 */   private Logger log = LoggerFactory.getLogger(HrmResourceSingleOutputObserverImplForCoreMail.class);
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void save(SimpleRecordData paramSimpleRecordData) {
/* 21 */     HrmResourceSingleOutputMappingImplForCoreMail hrmResourceSingleOutputMappingImplForCoreMail = new HrmResourceSingleOutputMappingImplForCoreMail();
/* 22 */     List<FieldData> list = hrmResourceSingleOutputMappingImplForCoreMail.execute(paramSimpleRecordData);
/*    */ 
/*    */     
/* 25 */     callThirdSdk(list, paramSimpleRecordData.getAction());
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private void callThirdSdk(List<FieldData> paramList, String paramString) {
/* 33 */     (new HrmResourceSubscriber4CoreMail()).synData(paramList, paramString);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/hrm/thirdsdk/coremail/single/output/observer/impl/HrmResourceSingleOutputObserverImplForCoreMail.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */