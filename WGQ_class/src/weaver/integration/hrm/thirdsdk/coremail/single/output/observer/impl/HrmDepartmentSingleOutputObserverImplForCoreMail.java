/*    */ package weaver.integration.hrm.thirdsdk.coremail.single.output.observer.impl;
/*    */ 
/*    */ import java.util.List;
/*    */ import weaver.integration.framework.data.field.FieldData;
/*    */ import weaver.integration.framework.data.record.SimpleRecordData;
/*    */ import weaver.integration.hrm.output.observer.IObserver;
/*    */ import weaver.integration.hrm.thirdsdk.coremail.single.output.mapping.impl.HrmDepartmentSingleOutputMappingImplForCoreMail;
/*    */ import weaver.integration.thirdsdk.coremail.biz.HrmDepartmentSubscriber4CoreMail;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmDepartmentSingleOutputObserverImplForCoreMail
/*    */   implements IObserver<SimpleRecordData>
/*    */ {
/*    */   public void save(SimpleRecordData paramSimpleRecordData) {
/* 18 */     HrmDepartmentSingleOutputMappingImplForCoreMail hrmDepartmentSingleOutputMappingImplForCoreMail = new HrmDepartmentSingleOutputMappingImplForCoreMail();
/* 19 */     List<FieldData> list = hrmDepartmentSingleOutputMappingImplForCoreMail.execute(paramSimpleRecordData);
/*    */ 
/*    */     
/* 22 */     callThirdSdk(list, paramSimpleRecordData.getAction());
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private void callThirdSdk(List<FieldData> paramList, String paramString) {
/* 30 */     (new HrmDepartmentSubscriber4CoreMail()).synData(paramList, paramString);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/hrm/thirdsdk/coremail/single/output/observer/impl/HrmDepartmentSingleOutputObserverImplForCoreMail.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */