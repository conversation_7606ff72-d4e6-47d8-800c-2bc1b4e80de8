/*    */ package weaver.integration.hrm.thirdsdk.coremail.single.output.observer.impl;
/*    */ 
/*    */ import java.util.List;
/*    */ import weaver.integration.framework.data.field.FieldData;
/*    */ import weaver.integration.framework.data.record.SimpleRecordData;
/*    */ import weaver.integration.hrm.output.observer.IObserver;
/*    */ import weaver.integration.hrm.thirdsdk.coremail.single.output.mapping.impl.HrmSubcompanySingleOutputMappingImplForCoreMail;
/*    */ import weaver.integration.thirdsdk.coremail.biz.HrmSubcompanySubscriber4CoreMail;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmSubcompanySingleOutputObserverImplForCoreMail
/*    */   implements IObserver<SimpleRecordData>
/*    */ {
/*    */   public void save(SimpleRecordData paramSimpleRecordData) {
/* 17 */     HrmSubcompanySingleOutputMappingImplForCoreMail hrmSubcompanySingleOutputMappingImplForCoreMail = new HrmSubcompanySingleOutputMappingImplForCoreMail();
/* 18 */     List<FieldData> list = hrmSubcompanySingleOutputMappingImplForCoreMail.execute(paramSimpleRecordData);
/*    */ 
/*    */     
/* 21 */     callThirdSdk(list, paramSimpleRecordData.getAction());
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private void callThirdSdk(List<FieldData> paramList, String paramString) {
/* 29 */     (new HrmSubcompanySubscriber4CoreMail()).synData(paramList, paramString);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/hrm/thirdsdk/coremail/single/output/observer/impl/HrmSubcompanySingleOutputObserverImplForCoreMail.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */