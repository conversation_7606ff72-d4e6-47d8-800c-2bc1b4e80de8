/*    */ package weaver.integration.hrm.thirdsdk.coremail.single.output.mapping.impl;
/*    */ 
/*    */ import com.engine.integration.biz.mapper.FieldMapperFactory;
/*    */ import java.util.List;
/*    */ import weaver.integration.framework.mapping.impl.FieldDataListMapping;
/*    */ import weaver.integration.hrm.mapping.AbstractHrmOutputMapping;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrmResourceSingleOutputMappingImplForCoreMail
/*    */   extends AbstractHrmOutputMapping
/*    */ {
/*    */   public FieldDataListMapping getFieldDataListMappingDefine() {
/* 18 */     return buildMapping();
/*    */   }
/*    */ 
/*    */   
/*    */   private FieldDataListMapping buildMapping() {
/* 23 */     FieldDataListMapping fieldDataListMapping = new FieldDataListMapping();
/* 24 */     List list = (List)FieldMapperFactory.getFieldMapper(2).getMapper(null, 4);
/* 25 */     fieldDataListMapping.setFieldDataMappingList(list);
/* 26 */     return fieldDataListMapping;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/hrm/thirdsdk/coremail/single/output/mapping/impl/HrmResourceSingleOutputMappingImplForCoreMail.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */